import { Product, ProductCategory, ProductType, WallpaperStyle, DeviceType } from '@/types/product';

// Shared wallpapers data that both admin and main site will use
export const wallpapersData: Product[] = [
  {
    id: '1',
    title: 'Abstract Neon Waves',
    description: 'Vibrant neon waves flowing across a dark background, perfect for modern setups',
    price: 4.99,
    originalPrice: 7.99,
    category: ProductCategory.WALLPAPERS,
    type: ProductType.DIGITAL,
    images: ['/api/placeholder/400/600'],
    tags: ['abstract', 'neon', 'waves', 'modern'],
    inStock: true,
    featured: true,
    isDigital: true,
    fileFormat: ['JPG', 'PNG'],
    fileSize: '15-25 MB',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    wallpaperData: {
      resolutions: [
        { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '25 MB' },
        { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '18 MB' },
        { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '12 MB' },
        { width: 1536, height: 2048, label: 'Tablet', downloadUrl: '/downloads/tablet', fileSize: '10 MB' },
        { width: 1080, height: 2340, label: 'Mobile', downloadUrl: '/downloads/mobile', fileSize: '8 MB' },
      ],
      aspectRatio: '16:9',
      colorPalette: ['#FF00FF', '#00FFFF', '#FF6B6B', '#4ECDC4', '#1A1A1A'],
      style: WallpaperStyle.ABSTRACT,
      deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.TABLET, DeviceType.MOBILE],
      previewUrl: '/api/placeholder/800/600',
      watermarkedPreview: '/api/placeholder/400/600',
      downloadCount: 1250,
    },
  },
  {
    id: '2',
    title: 'Minimalist Mountain Range',
    description: 'Clean, minimalist mountain silhouettes with gradient sky',
    price: 3.99,
    category: ProductCategory.WALLPAPERS,
    type: ProductType.DIGITAL,
    images: ['/api/placeholder/400/600'],
    tags: ['minimalist', 'mountains', 'nature', 'gradient'],
    inStock: true,
    featured: false,
    isDigital: true,
    fileFormat: ['JPG', 'PNG'],
    fileSize: '10-20 MB',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
    wallpaperData: {
      resolutions: [
        { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '20 MB' },
        { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '15 MB' },
        { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '10 MB' },
      ],
      aspectRatio: '16:9',
      colorPalette: ['#87CEEB', '#FFB6C1', '#DDA0DD', '#F0F8FF', '#2F4F4F'],
      style: WallpaperStyle.MINIMALIST,
      deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.ULTRAWIDE],
      previewUrl: '/api/placeholder/800/600',
      watermarkedPreview: '/api/placeholder/400/600',
      downloadCount: 890,
    },
  },
  {
    id: '3',
    title: 'Gaming RGB Setup',
    description: 'Dynamic RGB lighting effects perfect for gaming setups',
    price: 5.99,
    category: ProductCategory.WALLPAPERS,
    type: ProductType.DIGITAL,
    images: ['/api/placeholder/400/600'],
    tags: ['gaming', 'rgb', 'neon', 'tech'],
    inStock: true,
    featured: true,
    isDigital: true,
    fileFormat: ['JPG', 'PNG'],
    fileSize: '20-30 MB',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z',
    wallpaperData: {
      resolutions: [
        { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '30 MB' },
        { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '22 MB' },
        { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '15 MB' },
        { width: 3440, height: 1440, label: 'Ultrawide', downloadUrl: '/downloads/ultrawide', fileSize: '25 MB' },
      ],
      aspectRatio: '16:9',
      colorPalette: ['#FF0080', '#00FF80', '#8000FF', '#FF8000', '#000000'],
      style: WallpaperStyle.GAMING,
      deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.ULTRAWIDE],
      previewUrl: '/api/placeholder/800/600',
      watermarkedPreview: '/api/placeholder/400/600',
      downloadCount: 2100,
    },
  },
  {
    id: '4',
    title: 'Nature Forest Path',
    description: 'Serene forest path with morning sunlight filtering through trees',
    price: 2.99,
    category: ProductCategory.WALLPAPERS,
    type: ProductType.DIGITAL,
    images: ['/api/placeholder/400/600'],
    tags: ['nature', 'forest', 'peaceful', 'photography'],
    inStock: true,
    featured: false,
    isDigital: true,
    fileFormat: ['JPG'],
    fileSize: '12-18 MB',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
    wallpaperData: {
      resolutions: [
        { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '18 MB' },
        { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '12 MB' },
        { width: 1080, height: 2340, label: 'Mobile', downloadUrl: '/downloads/mobile', fileSize: '6 MB' },
      ],
      aspectRatio: '16:9',
      colorPalette: ['#228B22', '#8FBC8F', '#F5DEB3', '#DEB887', '#654321'],
      style: WallpaperStyle.NATURE,
      deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.MOBILE, DeviceType.TABLET],
      previewUrl: '/api/placeholder/800/600',
      watermarkedPreview: '/api/placeholder/400/600',
      downloadCount: 756,
    },
  },
  {
    id: '5',
    title: 'Geometric Patterns',
    description: 'Modern geometric patterns in vibrant colors',
    price: 3.99,
    category: ProductCategory.WALLPAPERS,
    type: ProductType.DIGITAL,
    images: ['/api/placeholder/400/600'],
    tags: ['geometric', 'patterns', 'vibrant', 'modern'],
    inStock: true,
    featured: true,
    isDigital: true,
    fileFormat: ['JPG', 'PNG'],
    fileSize: '15-22 MB',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-05T00:00:00Z',
    wallpaperData: {
      resolutions: [
        { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '22 MB' },
        { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '16 MB' },
        { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '12 MB' },
        { width: 1080, height: 2340, label: 'Mobile', downloadUrl: '/downloads/mobile', fileSize: '8 MB' },
      ],
      aspectRatio: '16:9',
      colorPalette: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
      style: WallpaperStyle.GEOMETRIC,
      deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.MOBILE, DeviceType.TABLET],
      previewUrl: '/api/placeholder/800/600',
      watermarkedPreview: '/api/placeholder/400/600',
      downloadCount: 1420,
    },
  },
  {
    id: '6',
    title: 'Dark Aesthetic',
    description: 'Sleek dark theme wallpaper with subtle textures',
    price: 2.99,
    category: ProductCategory.WALLPAPERS,
    type: ProductType.DIGITAL,
    images: ['/api/placeholder/400/600'],
    tags: ['dark', 'aesthetic', 'minimal', 'texture'],
    inStock: true,
    featured: false,
    isDigital: true,
    fileFormat: ['JPG', 'PNG'],
    fileSize: '8-15 MB',
    createdAt: '2024-01-06T00:00:00Z',
    updatedAt: '2024-01-06T00:00:00Z',
    wallpaperData: {
      resolutions: [
        { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '15 MB' },
        { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '12 MB' },
        { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '8 MB' },
        { width: 1080, height: 2340, label: 'Mobile', downloadUrl: '/downloads/mobile', fileSize: '5 MB' },
      ],
      aspectRatio: '16:9',
      colorPalette: ['#1A1A1A', '#2D2D2D', '#404040', '#666666', '#808080'],
      style: WallpaperStyle.DARK,
      deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.MOBILE, DeviceType.TABLET],
      previewUrl: '/api/placeholder/800/600',
      watermarkedPreview: '/api/placeholder/400/600',
      downloadCount: 980,
    },
  }
];

// Helper functions for wallpaper management
export const getWallpaperById = (id: string): Product | undefined => {
  return wallpapersData.find(wallpaper => wallpaper.id === id);
};

export const getFeaturedWallpapers = (): Product[] => {
  return wallpapersData.filter(wallpaper => wallpaper.featured);
};

export const getWallpapersByCategory = (style: WallpaperStyle): Product[] => {
  return wallpapersData.filter(wallpaper => wallpaper.wallpaperData?.style === style);
};

export const getTotalDownloads = (): number => {
  return wallpapersData.reduce((total, wallpaper) => 
    total + (wallpaper.wallpaperData?.downloadCount || 0), 0
  );
};

// Categories for filtering
export const wallpaperCategories = [
  'All',
  'Abstract',
  'Nature',
  'Geometric',
  'Minimal',
  'Dark',
  'Light',
  'Gaming'
];

// Convert Product to admin Wallpaper format
export const convertToAdminWallpaper = (product: Product) => {
  return {
    id: product.id,
    title: product.title,
    description: product.description,
    category: product.wallpaperData?.style || 'Abstract',
    tags: product.tags,
    price: product.price,
    originalPrice: product.originalPrice,
    featured: product.featured,
    downloads: product.wallpaperData?.downloadCount || 0,
    imageUrl: product.images[0],
    thumbnailUrl: product.wallpaperData?.watermarkedPreview || product.images[0],
    resolutions: {
      desktop: product.wallpaperData?.resolutions.find(r => r.label === '1080p' || r.label === '1440p') ? {
        width: product.wallpaperData.resolutions.find(r => r.label === '1080p' || r.label === '1440p')!.width,
        height: product.wallpaperData.resolutions.find(r => r.label === '1080p' || r.label === '1440p')!.height,
        url: product.wallpaperData.resolutions.find(r => r.label === '1080p' || r.label === '1440p')!.downloadUrl,
      } : { width: 1920, height: 1080, url: '/api/placeholder/1920/1080' },
      mobile: product.wallpaperData?.resolutions.find(r => r.label === 'Mobile') ? {
        width: product.wallpaperData.resolutions.find(r => r.label === 'Mobile')!.width,
        height: product.wallpaperData.resolutions.find(r => r.label === 'Mobile')!.height,
        url: product.wallpaperData.resolutions.find(r => r.label === 'Mobile')!.downloadUrl,
      } : { width: 1080, height: 1920, url: '/api/placeholder/1080/1920' },
      tablet: product.wallpaperData?.resolutions.find(r => r.label === 'Tablet') ? {
        width: product.wallpaperData.resolutions.find(r => r.label === 'Tablet')!.width,
        height: product.wallpaperData.resolutions.find(r => r.label === 'Tablet')!.height,
        url: product.wallpaperData.resolutions.find(r => r.label === 'Tablet')!.downloadUrl,
      } : { width: 1536, height: 2048, url: '/api/placeholder/1536/2048' },
    },
    createdAt: product.createdAt,
    updatedAt: product.updatedAt
  };
};
