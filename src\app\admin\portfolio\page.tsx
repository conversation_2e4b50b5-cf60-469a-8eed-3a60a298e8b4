'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  GlobeAltIcon,
  CodeBracketIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';

interface PortfolioProject {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  category: string;
  technologies: string[];
  featured: boolean;
  liveUrl?: string;
  githubUrl?: string;
  images: string[];
  createdAt: string;
  updatedAt: string;
}

const PortfolioManagement: React.FC = () => {
  const [projects, setProjects] = useState<PortfolioProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);

  // Mock data - in real app, this would come from Firebase
  const mockProjects: PortfolioProject[] = [
    {
      id: '1',
      title: 'E-Commerce Platform',
      description: 'Modern e-commerce solution with advanced filtering and payment integration',
      longDescription: 'A comprehensive e-commerce platform built with React and Node.js, featuring advanced product filtering, secure payment processing with Stripe, real-time inventory management, and a responsive admin dashboard.',
      category: 'Web Development',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Redux'],
      featured: true,
      liveUrl: 'https://example-ecommerce.com',
      githubUrl: 'https://github.com/example/ecommerce',
      images: ['/api/placeholder/600/400'],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    },
    {
      id: '2',
      title: 'Mobile Banking App',
      description: 'Secure mobile banking application with biometric authentication',
      longDescription: 'A secure mobile banking application featuring biometric authentication, real-time transaction monitoring, budget tracking, and seamless money transfers.',
      category: 'Mobile Development',
      technologies: ['React Native', 'Firebase', 'TypeScript', 'Expo'],
      featured: true,
      liveUrl: 'https://example-banking.com',
      githubUrl: 'https://github.com/example/banking-app',
      images: ['/api/placeholder/600/400'],
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-14T00:00:00Z'
    },
    {
      id: '3',
      title: 'AI Dashboard',
      description: 'Analytics dashboard with machine learning insights',
      longDescription: 'An intelligent analytics dashboard that leverages machine learning to provide actionable insights, featuring interactive data visualizations and predictive analytics.',
      category: 'Data Science',
      technologies: ['Next.js', 'Python', 'TensorFlow', 'D3.js', 'PostgreSQL'],
      featured: false,
      liveUrl: 'https://example-ai-dashboard.com',
      githubUrl: 'https://github.com/example/ai-dashboard',
      images: ['/api/placeholder/600/400'],
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-13T00:00:00Z'
    },
    {
      id: '4',
      title: 'Social Media Platform',
      description: 'Full-stack social media application with real-time features',
      longDescription: 'A modern social media platform with real-time messaging, post sharing, story features, and advanced privacy controls.',
      category: 'Web Development',
      technologies: ['Vue.js', 'Express.js', 'Socket.io', 'Redis', 'AWS'],
      featured: false,
      liveUrl: 'https://example-social.com',
      images: ['/api/placeholder/600/400'],
      createdAt: '2024-01-04T00:00:00Z',
      updatedAt: '2024-01-12T00:00:00Z'
    }
  ];

  const categories = ['all', 'Web Development', 'Mobile Development', 'Data Science', 'UI/UX Design', 'Other'];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProjects(mockProjects);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || project.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prev => 
      prev.includes(projectId) 
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProjects.length === filteredProjects.length) {
      setSelectedProjects([]);
    } else {
      setSelectedProjects(filteredProjects.map(p => p.id));
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      // In real app, call API to delete project
      setProjects(prev => prev.filter(p => p.id !== projectId));
      setSelectedProjects(prev => prev.filter(id => id !== projectId));
      setShowDeleteModal(false);
      setProjectToDelete(null);
    } catch (error) {
      console.error('Failed to delete project:', error);
    }
  };

  const handleBulkDelete = async () => {
    try {
      // In real app, call API to delete multiple projects
      setProjects(prev => prev.filter(p => !selectedProjects.includes(p.id)));
      setSelectedProjects([]);
    } catch (error) {
      console.error('Failed to delete projects:', error);
    }
  };

  const toggleFeatured = async (projectId: string) => {
    try {
      // In real app, call API to update project
      setProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, featured: !p.featured } : p
      ));
    } catch (error) {
      console.error('Failed to update project:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Portfolio</h1>
          <p className="text-gray-600">Manage your portfolio projects</p>
        </div>
        <Link
          href="/admin/portfolio/new"
          className="mt-4 sm:mt-0 inline-flex items-center space-x-2 bg-purple-yellow-gradient text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
        >
          <PlusIcon className="w-5 h-5" />
          <span>Add Project</span>
        </Link>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[
          { label: 'Total Projects', value: projects.length, color: 'text-blue-600' },
          { label: 'Featured', value: projects.filter(p => p.featured).length, color: 'text-yellow-600' },
          { label: 'Categories', value: new Set(projects.map(p => p.category)).size, color: 'text-green-600' },
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
          >
            <p className="text-sm font-medium text-gray-600">{stat.label}</p>
            <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
          </motion.div>
        ))}
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div className="flex items-center space-x-4">
            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>

            {/* Bulk Actions */}
            {selectedProjects.length > 0 && (
              <button
                onClick={handleBulkDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
              >
                Delete Selected ({selectedProjects.length})
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredProjects.map((project) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              {/* Project Image */}
              <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-4xl font-bold text-primary-400">
                    {project.title.charAt(0)}
                  </div>
                </div>

                {/* Selection Checkbox */}
                <div className="absolute top-3 left-3">
                  <input
                    type="checkbox"
                    checked={selectedProjects.includes(project.id)}
                    onChange={() => handleSelectProject(project.id)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>

                {/* Featured Badge */}
                <div className="absolute top-3 right-3">
                  <button
                    onClick={() => toggleFeatured(project.id)}
                    className={`p-1 rounded-full ${
                      project.featured ? 'text-yellow-500' : 'text-gray-400 hover:text-yellow-500'
                    } transition-colors duration-200`}
                    title={project.featured ? 'Remove from featured' : 'Add to featured'}
                  >
                    {project.featured ? (
                      <StarSolidIcon className="w-5 h-5" />
                    ) : (
                      <StarIcon className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="px-2 py-1 text-xs font-medium bg-primary-100 text-primary-700 rounded-full">
                    {project.category}
                  </span>
                  {project.featured && (
                    <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-700 rounded-full">
                      Featured
                    </span>
                  )}
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-2">{project.title}</h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{project.description}</p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {project.technologies.slice(0, 3).map((tech, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="text-xs text-gray-500">
                      +{project.technologies.length - 3} more
                    </span>
                  )}
                </div>

                {/* Links */}
                <div className="flex items-center space-x-3 mb-4">
                  {project.liveUrl && (
                    <a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm"
                    >
                      <GlobeAltIcon className="w-4 h-4" />
                      <span>Live</span>
                    </a>
                  )}
                  {project.githubUrl && (
                    <a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-1 text-gray-600 hover:text-gray-700 text-sm"
                    >
                      <CodeBracketIcon className="w-4 h-4" />
                      <span>Code</span>
                    </a>
                  )}
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">
                    {new Date(project.updatedAt).toLocaleDateString()}
                  </span>
                  <div className="flex space-x-2">
                    <Link
                      href={`/portfolio/${project.id}`}
                      className="text-blue-600 hover:text-blue-900 p-1"
                      title="View"
                    >
                      <EyeIcon className="w-4 h-4" />
                    </Link>
                    <Link
                      href={`/admin/portfolio/${project.id}/edit`}
                      className="text-green-600 hover:text-green-900 p-1"
                      title="Edit"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </Link>
                    <button
                      onClick={() => {
                        setProjectToDelete(project.id);
                        setShowDeleteModal(true);
                      }}
                      className="text-red-600 hover:text-red-900 p-1"
                      title="Delete"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredProjects.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">💼</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || selectedCategory !== 'all' 
              ? 'Try adjusting your search or filter criteria'
              : 'Get started by adding your first project'
            }
          </p>
          <Link
            href="/admin/portfolio/new"
            className="inline-flex items-center space-x-2 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200"
          >
            <PlusIcon className="w-5 h-5" />
            <span>Add Project</span>
          </Link>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Project</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this project? This action cannot be undone.
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={() => projectToDelete && handleDeleteProject(projectToDelete)}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                >
                  Delete
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PortfolioManagement;
