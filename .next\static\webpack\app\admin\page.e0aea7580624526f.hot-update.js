"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ChartBarIcon,CheckCircleIcon,CloudArrowUpIcon,CurrencyDollarIcon,EyeIcon,PencilIcon,PhotoIcon,ShoppingBagIcon,TrashIcon,UsersIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Profile Image Manager Component\nconst ProfileImageManager = ()=>{\n    _s();\n    const [profileImage, setProfileImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadStatus, setUploadStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [previewImage, setPreviewImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    // Load existing profile image on component mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const existingImage = localStorage.getItem(\"profileImage\");\n        if (existingImage) {\n            setProfileImage(existingImage);\n        }\n    }, []);\n    const handleFileSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            if (!file.type.startsWith(\"image/\")) {\n                setUploadStatus(\"error\");\n                return;\n            }\n            if (file.size > 5 * 1024 * 1024) {\n                setUploadStatus(\"error\");\n                return;\n            }\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setPreviewImage(result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const handleUpload = async ()=>{\n        var _fileInputRef_current_files, _fileInputRef_current;\n        if (!previewImage || !((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : (_fileInputRef_current_files = _fileInputRef_current.files) === null || _fileInputRef_current_files === void 0 ? void 0 : _fileInputRef_current_files[0])) return;\n        setIsUploading(true);\n        setUploadStatus(\"idle\");\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", fileInputRef.current.files[0]);\n            const response = await fetch(\"/api/upload/profile\", {\n                method: \"POST\",\n                body: formData\n            });\n            const result = await response.json();\n            if (result.success) {\n                localStorage.setItem(\"profileImage\", result.url);\n                setProfileImage(result.url);\n                setUploadStatus(\"success\");\n                setPreviewImage(null);\n                if (fileInputRef.current) {\n                    fileInputRef.current.value = \"\";\n                }\n            } else {\n                setUploadStatus(\"error\");\n            }\n        } catch (error) {\n            setUploadStatus(\"error\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleRemoveImage = ()=>{\n        localStorage.removeItem(\"profileImage\");\n        setProfileImage(null);\n        setPreviewImage(null);\n        setUploadStatus(\"idle\");\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            delay: 0.3\n        },\n        className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-6 h-6 mr-3 text-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Profile Image Management\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Current Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-32 h-32 rounded-full overflow-hidden shadow-md border-2 border-gray-200 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: profileImage,\n                                                alt: \"Profile\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRemoveImage,\n                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 h-32 rounded-full bg-gray-100 flex items-center justify-center mx-auto border-2 border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-8 h-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: \"Upload New Image\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-primary-400 transition-colors duration-200 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            var _fileInputRef_current;\n                                            return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                        },\n                                        className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                        children: \"Choose File\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileInputRef,\n                                        type: \"file\",\n                                        accept: \"image/*\",\n                                        onChange: handleFileSelect,\n                                        className: \"hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"Max 5MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            previewImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full overflow-hidden shadow-sm border border-gray-200 mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: previewImage,\n                                            alt: \"Preview\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleUpload,\n                                                disabled: isUploading,\n                                                className: \"bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors duration-200 disabled:opacity-50\",\n                                                children: isUploading ? \"Uploading...\" : \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setPreviewImage(null),\n                                                className: \"bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600 transition-colors duration-200\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            uploadStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded p-2 flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-800 text-sm\",\n                                        children: \"Uploaded successfully!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, undefined),\n                            uploadStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded p-2 flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-800 text-sm\",\n                                        children: \"Upload failed. Try again.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfileImageManager, \"fhn1HqICwlJC3m16kOnvSNboChA=\");\n_c = ProfileImageManager;\nconst AdminDashboard = ()=>{\n    _s1();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalRevenue: 12847.50,\n        totalOrders: 156,\n        totalProducts: 24,\n        totalUsers: 89,\n        revenueChange: 12.5,\n        ordersChange: 8.2,\n        productsChange: 4.1,\n        usersChange: 15.3\n    });\n    const [recentOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"ORD-001\",\n            customer: \"John Doe\",\n            email: \"<EMAIL>\",\n            total: 89.99,\n            status: \"completed\",\n            date: \"2024-01-15T10:30:00Z\"\n        },\n        {\n            id: \"ORD-002\",\n            customer: \"Jane Smith\",\n            email: \"<EMAIL>\",\n            total: 59.99,\n            status: \"processing\",\n            date: \"2024-01-15T09:15:00Z\"\n        },\n        {\n            id: \"ORD-003\",\n            customer: \"Mike Johnson\",\n            email: \"<EMAIL>\",\n            total: 129.99,\n            status: \"pending\",\n            date: \"2024-01-15T08:45:00Z\"\n        },\n        {\n            id: \"ORD-004\",\n            customer: \"Sarah Wilson\",\n            email: \"<EMAIL>\",\n            total: 39.99,\n            status: \"completed\",\n            date: \"2024-01-14T16:20:00Z\"\n        }\n    ]);\n    const [topProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            name: \"Premium UI Kit Pro\",\n            sales: 45,\n            revenue: 4049.55,\n            image: \"/api/placeholder/60/60\"\n        },\n        {\n            id: \"2\",\n            name: \"React Dashboard Template\",\n            sales: 32,\n            revenue: 1919.68,\n            image: \"/api/placeholder/60/60\"\n        },\n        {\n            id: \"3\",\n            name: \"Icon Pack Collection\",\n            sales: 28,\n            revenue: 839.72,\n            image: \"/api/placeholder/60/60\"\n        },\n        {\n            id: \"4\",\n            name: \"Animation Library\",\n            sales: 21,\n            revenue: 839.79,\n            image: \"/api/placeholder/60/60\"\n        }\n    ]);\n    const statCards = [\n        {\n            title: \"Total Revenue\",\n            value: \"$\".concat(stats.totalRevenue.toLocaleString()),\n            change: stats.revenueChange,\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"text-green-600\",\n            bgColor: \"bg-green-100\"\n        },\n        {\n            title: \"Total Orders\",\n            value: stats.totalOrders.toLocaleString(),\n            change: stats.ordersChange,\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"text-blue-600\",\n            bgColor: \"bg-blue-100\"\n        },\n        {\n            title: \"Total Products\",\n            value: stats.totalProducts.toLocaleString(),\n            change: stats.productsChange,\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"text-purple-600\",\n            bgColor: \"bg-purple-100\"\n        },\n        {\n            title: \"Total Users\",\n            value: stats.totalUsers.toLocaleString(),\n            change: stats.usersChange,\n            icon: _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"text-orange-600\",\n            bgColor: \"bg-orange-100\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"bg-green-100 text-green-800\";\n            case \"processing\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"cancelled\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6\n                },\n                className: \"bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Welcome back, Admin!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg opacity-90\",\n                        children: \"Here's what's happening with your store today.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                variants: containerVariants,\n                initial: \"hidden\",\n                animate: \"visible\",\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: statCards.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        variants: itemVariants,\n                        whileHover: {\n                            scale: 1.02\n                        },\n                        className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mt-2\",\n                                            children: [\n                                                stat.change > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 text-green-500 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-500 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(stat.change > 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        Math.abs(stat.change),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-1\",\n                                                    children: \"vs last month\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(stat.bgColor),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"w-6 h-6 \".concat(stat.color)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, undefined)\n                    }, stat.title, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfileImageManager, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.2\n                        },\n                        className: \"bg-white rounded-xl shadow-lg border border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Recent Orders\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin/orders\",\n                                            className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        order.id\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(order.status)),\n                                                                    children: order.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: order.customer\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: order.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                order.total\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: new Date(order.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 ml-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-gray-400 hover:text-green-600 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ChartBarIcon_CheckCircleIcon_CloudArrowUpIcon_CurrencyDollarIcon_EyeIcon_PencilIcon_PhotoIcon_ShoppingBagIcon_TrashIcon_UsersIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, order.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.3\n                        },\n                        className: \"bg-white rounded-xl shadow-lg border border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Top Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin/products\",\n                                            className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: topProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-primary-400\",\n                                                        children: product.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-900 truncate\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                product.sales,\n                                                                \" sales\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.revenue.toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Revenue\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, product.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 411,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.4\n                },\n                className: \"bg-white rounded-xl shadow-lg p-6 border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            {\n                                name: \"Add Product\",\n                                href: \"/admin/products/new\",\n                                color: \"bg-blue-500 hover:bg-blue-600\"\n                            },\n                            {\n                                name: \"Add Portfolio Item\",\n                                href: \"/admin/portfolio/new\",\n                                color: \"bg-green-500 hover:bg-green-600\"\n                            },\n                            {\n                                name: \"View Orders\",\n                                href: \"/admin/orders\",\n                                color: \"bg-purple-500 hover:bg-purple-600\"\n                            },\n                            {\n                                name: \"Manage Users\",\n                                href: \"/admin/users\",\n                                color: \"bg-orange-500 hover:bg-orange-600\"\n                            }\n                        ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: action.href,\n                                className: \"\".concat(action.color, \" text-white p-4 rounded-lg text-center font-medium transition-colors duration-200 hover:shadow-lg\"),\n                                children: action.name\n                            }, action.name, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 354,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AdminDashboard, \"vZkoQ2O+n/65rSvxQsstd7wt7HM=\");\n_c1 = AdminDashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AdminDashboard);\nvar _c, _c1;\n$RefreshReg$(_c, \"ProfileImageManager\");\n$RefreshReg$(_c1, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});