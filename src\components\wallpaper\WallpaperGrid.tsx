'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  EyeIcon,
  HeartIcon,
  ShoppingCartIcon,
  DownloadIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { Product, WallpaperStyle, DeviceType } from '@/types/product';
import WallpaperPreview from './WallpaperPreview';

interface WallpaperGridProps {
  products: Product[];
  onAddToCart?: (product: Product) => void;
  onToggleFavorite?: (productId: string) => void;
  favorites?: string[];
}

const WallpaperGrid: React.FC<WallpaperGridProps> = ({
  products,
  onAddToCart,
  onToggleFavorite,
  favorites = [],
}) => {
  const [previewProduct, setPreviewProduct] = useState<Product | null>(null);
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null);

  const handlePreview = (product: Product) => {
    setPreviewProduct(product);
  };

  const handleDownload = (product: Product) => {
    // In a real app, this would handle the purchase/download process
    console.log('Download:', product.title);
    onAddToCart?.(product);
  };

  const getStyleColor = (style: WallpaperStyle): string => {
    const colors: Record<WallpaperStyle, string> = {
      [WallpaperStyle.ABSTRACT]: 'bg-purple-500',
      [WallpaperStyle.NATURE]: 'bg-green-500',
      [WallpaperStyle.MINIMALIST]: 'bg-gray-500',
      [WallpaperStyle.GAMING]: 'bg-red-500',
      [WallpaperStyle.ANIME]: 'bg-pink-500',
      [WallpaperStyle.PHOTOGRAPHY]: 'bg-blue-500',
      [WallpaperStyle.GRADIENT]: 'bg-gradient-to-r from-purple-500 to-pink-500',
      [WallpaperStyle.PATTERN]: 'bg-indigo-500',
      [WallpaperStyle.DARK]: 'bg-gray-900',
      [WallpaperStyle.LIGHT]: 'bg-gray-100',
      [WallpaperStyle.NEON]: 'bg-cyan-500',
      [WallpaperStyle.VINTAGE]: 'bg-amber-500',
      [WallpaperStyle.MODERN]: 'bg-slate-500',
      [WallpaperStyle.ARTISTIC]: 'bg-orange-500',
    };
    return colors[style] || 'bg-gray-500';
  };

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AnimatePresence>
          {products.map((product) => (
            <motion.div
              key={product.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300"
              onMouseEnter={() => setHoveredProduct(product.id)}
              onMouseLeave={() => setHoveredProduct(null)}
            >
              {/* Image Container */}
              <div className="relative aspect-[3/4] overflow-hidden">
                <img
                  src={product.wallpaperData?.watermarkedPreview || product.images[0]}
                  alt={product.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* Action Buttons */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => handlePreview(product)}
                      className="p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200"
                      title="Preview"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => onToggleFavorite?.(product.id)}
                      className="p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200"
                      title="Add to Favorites"
                    >
                      {favorites.includes(product.id) ? (
                        <HeartSolidIcon className="w-5 h-5 text-red-500" />
                      ) : (
                        <HeartIcon className="w-5 h-5" />
                      )}
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => handleDownload(product)}
                      className="p-3 bg-purple-yellow-gradient rounded-full text-white hover:shadow-lg transition-all duration-200"
                      title="Add to Cart"
                    >
                      <ShoppingCartIcon className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>

                {/* Featured Badge */}
                {product.featured && (
                  <div className="absolute top-3 left-3">
                    <div className="flex items-center space-x-1 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      <StarIcon className="w-3 h-3" />
                      <span>Featured</span>
                    </div>
                  </div>
                )}

                {/* Style Badge */}
                {product.wallpaperData?.style && (
                  <div className="absolute top-3 right-3">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getStyleColor(product.wallpaperData.style)}`}>
                      {product.wallpaperData.style.charAt(0).toUpperCase() + product.wallpaperData.style.slice(1)}
                    </div>
                  </div>
                )}

                {/* Resolution Info */}
                {product.wallpaperData?.resolutions && (
                  <div className="absolute bottom-3 left-3">
                    <div className="bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium">
                      {product.wallpaperData.resolutions.length} resolutions
                    </div>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1 line-clamp-1">
                  {product.title}
                </h3>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {product.description}
                </p>

                {/* Metadata */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    {product.wallpaperData?.aspectRatio && (
                      <span>{product.wallpaperData.aspectRatio}</span>
                    )}
                    {product.fileFormat && (
                      <>
                        <span>•</span>
                        <span>{product.fileFormat.join(', ')}</span>
                      </>
                    )}
                  </div>
                  
                  {product.wallpaperData?.downloadCount && (
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <DownloadIcon className="w-3 h-3" />
                      <span>{product.wallpaperData.downloadCount.toLocaleString()}</span>
                    </div>
                  )}
                </div>

                {/* Color Palette Preview */}
                {product.wallpaperData?.colorPalette && (
                  <div className="flex space-x-1 mb-3">
                    {product.wallpaperData.colorPalette.slice(0, 5).map((color, index) => (
                      <div
                        key={index}
                        className="w-4 h-4 rounded-full border border-gray-200"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                    {product.wallpaperData.colorPalette.length > 5 && (
                      <div className="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs text-gray-600">
                        +{product.wallpaperData.colorPalette.length - 5}
                      </div>
                    )}
                  </div>
                )}

                {/* Device Compatibility */}
                {product.wallpaperData?.deviceCompatibility && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {product.wallpaperData.deviceCompatibility.slice(0, 3).map((device) => (
                      <span
                        key={device}
                        className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md"
                      >
                        {device}
                      </span>
                    ))}
                    {product.wallpaperData.deviceCompatibility.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{product.wallpaperData.deviceCompatibility.length - 3} more
                      </span>
                    )}
                  </div>
                )}

                {/* Price and Action */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">
                      ${product.price.toFixed(2)}
                    </span>
                    {product.originalPrice && product.originalPrice > product.price && (
                      <span className="text-sm text-gray-500 line-through">
                        ${product.originalPrice.toFixed(2)}
                      </span>
                    )}
                  </div>
                  
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleDownload(product)}
                    className="px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-200"
                  >
                    Add to Cart
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Preview Modal */}
      {previewProduct && (
        <WallpaperPreview
          product={previewProduct}
          isOpen={!!previewProduct}
          onClose={() => setPreviewProduct(null)}
          onDownload={(resolution) => {
            console.log('Download resolution:', resolution);
            handleDownload(previewProduct);
            setPreviewProduct(null);
          }}
        />
      )}
    </>
  );
};

export default WallpaperGrid;

// Wallpaper Filter Component
export interface WallpaperFilters {
  style?: WallpaperStyle[];
  devices?: DeviceType[];
  priceRange?: [number, number];
  aspectRatio?: string[];
  colors?: string[];
  sortBy?: 'newest' | 'popular' | 'price-low' | 'price-high' | 'downloads';
}

interface WallpaperFiltersProps {
  filters: WallpaperFilters;
  onFiltersChange: (filters: WallpaperFilters) => void;
  onClearFilters: () => void;
}

export const WallpaperFiltersPanel: React.FC<WallpaperFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
}) => {
  const styles = Object.values(WallpaperStyle);
  const devices = Object.values(DeviceType);
  const aspectRatios = ['16:9', '16:10', '21:9', '4:3', '9:16', '3:2'];
  const commonColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        <button
          onClick={onClearFilters}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          Clear All
        </button>
      </div>

      {/* Style Filter */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Style</h4>
        <div className="grid grid-cols-2 gap-2">
          {styles.map((style) => (
            <label key={style} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.style?.includes(style) || false}
                onChange={(e) => {
                  const newStyles = e.target.checked
                    ? [...(filters.style || []), style]
                    : (filters.style || []).filter(s => s !== style);
                  onFiltersChange({ ...filters, style: newStyles });
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-700 capitalize">{style}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Device Filter */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Device</h4>
        <div className="space-y-2">
          {devices.map((device) => (
            <label key={device} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.devices?.includes(device) || false}
                onChange={(e) => {
                  const newDevices = e.target.checked
                    ? [...(filters.devices || []), device]
                    : (filters.devices || []).filter(d => d !== device);
                  onFiltersChange({ ...filters, devices: newDevices });
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-700 capitalize">{device.replace('_', ' ')}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Aspect Ratio Filter */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Aspect Ratio</h4>
        <div className="grid grid-cols-2 gap-2">
          {aspectRatios.map((ratio) => (
            <label key={ratio} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.aspectRatio?.includes(ratio) || false}
                onChange={(e) => {
                  const newRatios = e.target.checked
                    ? [...(filters.aspectRatio || []), ratio]
                    : (filters.aspectRatio || []).filter(r => r !== ratio);
                  onFiltersChange({ ...filters, aspectRatio: newRatios });
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-700">{ratio}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Color Filter */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Colors</h4>
        <div className="grid grid-cols-4 gap-2">
          {commonColors.map((color) => (
            <button
              key={color}
              onClick={() => {
                const newColors = filters.colors?.includes(color)
                  ? (filters.colors || []).filter(c => c !== color)
                  : [...(filters.colors || []), color];
                onFiltersChange({ ...filters, colors: newColors });
              }}
              className={`w-8 h-8 rounded-full border-2 transition-all duration-200 ${
                filters.colors?.includes(color)
                  ? 'border-gray-800 scale-110'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              style={{ backgroundColor: color }}
              title={color}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
