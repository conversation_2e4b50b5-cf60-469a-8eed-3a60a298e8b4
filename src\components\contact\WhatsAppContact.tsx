'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useServices } from '@/contexts/ServicesContext';
import {
  ChatBubbleLeftRightIcon,
  XMarkIcon,
  PaintBrushIcon,
  ComputerDesktopIcon,
  CameraIcon,
  VideoCameraIcon
} from '@heroicons/react/24/outline';

// Services are now managed through ServicesContext

const WhatsAppContact: React.FC = () => {
  const { activeServices } = useServices();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<string>('');
  const [customMessage, setCustomMessage] = useState('');

  const whatsappNumber = '254703973225'; // Your WhatsApp number

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'PaintBrushIcon':
        return PaintBrushIcon;
      case 'ComputerDesktopIcon':
        return ComputerDesktopIcon;
      case 'CameraIcon':
        return CameraIcon;
      case 'VideoCameraIcon':
        return VideoCameraIcon;
      default:
        return PaintBrushIcon;
    }
  };

  const generateWhatsAppMessage = (service?: any) => {
    let message = `Hi Elias! 👋\n\nI'm interested in your services.\n\n`;
    
    if (service) {
      message += `Service: ${service.title}\n`;
      message += `Details: ${service.description}\n\n`;
    }
    
    if (customMessage.trim()) {
      message += `Additional Details:\n${customMessage}\n\n`;
    }
    
    message += `Please let me know about pricing and availability.\n\nThank you!`;
    
    return encodeURIComponent(message);
  };

  const openWhatsApp = (service?: any) => {
    const message = generateWhatsAppMessage(service);
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
    setIsOpen(false);
  };

  const handleQuickContact = () => {
    const message = encodeURIComponent(
      `Hi Elias! 👋\n\nI'd like to discuss your creative services. Please let me know when you're available to chat.\n\nThank you!`
    );
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <>
      {/* Floating WhatsApp Button */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="fixed bottom-6 right-6 z-50"
      >
        <motion.button
          onClick={() => setIsOpen(true)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2"
        >
          <ChatBubbleLeftRightIcon className="w-6 h-6" />
          <span className="hidden sm:block font-medium">Get Services</span>
        </motion.button>
      </motion.div>

      {/* WhatsApp Contact Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto"
            >
              {/* Header */}
              <div className="bg-green-500 text-white p-6 rounded-t-2xl">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <ChatBubbleLeftRightIcon className="w-8 h-8" />
                    <div>
                      <h3 className="text-xl font-bold">WhatsApp Contact</h3>
                      <p className="text-green-100">Get in touch for services</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-white hover:bg-green-600 p-2 rounded-full transition-colors duration-200"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Quick Contact */}
                <div className="mb-6">
                  <button
                    onClick={handleQuickContact}
                    className="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors duration-200 flex items-center justify-center space-x-2"
                  >
                    <ChatBubbleLeftRightIcon className="w-5 h-5" />
                    <span>Quick Contact</span>
                  </button>
                  <p className="text-sm text-gray-500 text-center mt-2">
                    Start a general conversation
                  </p>
                </div>

                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or choose a service</span>
                  </div>
                </div>

                {/* Services */}
                <div className="space-y-3 mb-6">
                  {activeServices.map((service) => {
                    const IconComponent = getIconComponent(service.icon);
                    return (
                      <motion.button
                        key={service.id}
                        onClick={() => openWhatsApp(service)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="w-full p-4 border border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all duration-200 text-left"
                      >
                        <div className="flex items-start space-x-3">
                          <div
                            className="p-2 rounded-lg"
                            style={{
                              background: `linear-gradient(135deg, ${service.color.includes('purple') ? '#8B5CF6' : service.color.includes('blue') ? '#3B82F6' : service.color.includes('green') ? '#10B981' : '#EF4444'} 0%, ${service.color.includes('purple') ? '#A855F7' : service.color.includes('blue') ? '#1D4ED8' : service.color.includes('green') ? '#059669' : '#DC2626'} 100%)`
                            }}
                          >
                            <IconComponent className="w-5 h-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900">{service.title}</h4>
                            <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                {/* Custom Message */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Details (Optional)
                  </label>
                  <textarea
                    value={customMessage}
                    onChange={(e) => setCustomMessage(e.target.value)}
                    placeholder="Tell me more about your project requirements..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                  />
                </div>

                {/* Contact Info */}
                <div className="bg-gray-50 p-4 rounded-xl">
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">WhatsApp Number</p>
                    <p className="font-semibold text-gray-900">+254 703 973 225</p>
                    <p className="text-xs text-gray-500 mt-2">
                      Available Monday - Friday, 9 AM - 6 PM EAT
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default WhatsAppContact;
