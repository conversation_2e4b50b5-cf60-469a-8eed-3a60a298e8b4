"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_home_HeroTest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_ServicesPreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_FeaturedPortfolio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_FeaturedProducts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_Skills__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_Testimonials__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_CallToAction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-16 lg:pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_HeroTest__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_About__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_ServicesPreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Skills__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_FeaturedPortfolio__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_FeaturedProducts__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Testimonials__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_CallToAction__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                initial: {\n                    opacity: 0,\n                    scale: 0\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: ()=>window.scrollTo({\n                        top: 0,\n                        behavior: \"smooth\"\n                    }),\n                className: \"fixed bottom-8 right-8 w-12 h-12 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40\",\n                style: {\n                    background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-6 h-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftEllipsisIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// My Creative Process\nconst processSteps = [\n    {\n        id: 1,\n        title: \"Consultation\",\n        description: \"Understanding your vision, requirements, and goals through detailed discussion and planning.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\",\n        step: \"01\"\n    },\n    {\n        id: 2,\n        title: \"Planning\",\n        description: \"Creating a strategic roadmap, timeline, and detailed project plan tailored to your needs.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\",\n        step: \"02\"\n    },\n    {\n        id: 3,\n        title: \"Creation\",\n        description: \"Bringing your ideas to life with professional design, development, and creative execution.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"from-yellow-500 to-orange-500\",\n        bgColor: \"bg-yellow-50\",\n        borderColor: \"border-yellow-200\",\n        step: \"03\"\n    },\n    {\n        id: 4,\n        title: \"Delivery\",\n        description: \"Final review, refinements, and seamless handover of your completed project.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\",\n        step: \"04\"\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6\n        }\n    }\n};\nconst ProcessPreview = ()=>{\n    const openWhatsApp = ()=>{\n        const message = encodeURIComponent(\"Hi Elias! \\uD83D\\uDC4B\\n\\nI'm interested in working with you. Let's discuss my project requirements and your creative process.\\n\\nThank you!\");\n        const whatsappUrl = \"https://wa.me/254703973225?text=\".concat(message);\n        window.open(whatsappUrl, \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"My Creative \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        backgroundClip: \"text\"\n                                    },\n                                    children: \"Process\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                            children: \"A streamlined approach to bringing your creative vision to life. From initial consultation to final delivery, every step is designed for excellence.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            onClick: openWhatsApp,\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Your Project\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    children: processSteps.map((step, index)=>{\n                        const IconComponent = step.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            variants: itemVariants,\n                            className: \"\".concat(step.bgColor, \" \").concat(step.borderColor, \" border-2 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer relative overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-4 right-4 text-2xl font-bold text-gray-300 group-hover:text-gray-400 transition-colors duration-300\",\n                                    children: step.step\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r \".concat(step.color, \" p-3 rounded-lg mb-4 w-fit\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                    children: step.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm leading-relaxed\",\n                                    children: step.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, undefined),\n                                index < processSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-gray-300 to-transparent transform -translate-y-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm font-medium text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Step \",\n                                                    step.step\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, step.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-2xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"Ready to Start Your Project?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Let's discuss your vision and how my creative process can bring it to life. Every great project starts with a conversation.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/services\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"bg-white border-2 border-gray-300 text-gray-700 hover:border-purple-500 hover:text-purple-600 px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"View My Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        onClick: openWhatsApp,\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Let's Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProcessPreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessPreview);\nvar _c;\n$RefreshReg$(_c, \"ProcessPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftEllipsisIcon.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftEllipsisIcon.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChatBubbleLeftEllipsisIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.625 9.75a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 0 1 .778-.332 48.294 48.294 0 0 0 5.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChatBubbleLeftEllipsisIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftEllipsisIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChatBubbleLeftRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChatBubbleLeftRightIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClipboardDocumentListIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClipboardDocumentListIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0NsaXBib2FyZERvY3VtZW50TGlzdEljb24uanMiLCJtYXBwaW5ncyI6Ijs7QUFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsc0JBQXNCLGdEQUFtQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLCtCQUErQixnREFBbUI7QUFDckQ7QUFDQSxHQUFHLDhCQUE4QixnREFBbUI7QUFDcEQ7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsaUNBQWlDLDZDQUFnQjtBQUNqRCwrREFBZSxVQUFVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0NsaXBib2FyZERvY3VtZW50TGlzdEljb24uanM/NzU5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIENsaXBib2FyZERvY3VtZW50TGlzdEljb24oe1xuICB0aXRsZSxcbiAgdGl0bGVJZCxcbiAgLi4ucHJvcHNcbn0sIHN2Z1JlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzdmdcIiwgT2JqZWN0LmFzc2lnbih7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICBmaWxsOiBcIm5vbmVcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIHN0cm9rZVdpZHRoOiAxLjUsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgXCJkYXRhLXNsb3RcIjogXCJpY29uXCIsXG4gICAgcmVmOiBzdmdSZWYsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogdGl0bGVJZFxuICB9LCBwcm9wcyksIHRpdGxlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJ0aXRsZVwiLCB7XG4gICAgaWQ6IHRpdGxlSWRcbiAgfSwgdGl0bGUpIDogbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICBkOiBcIk05IDEyaDMuNzVNOSAxNWgzLjc1TTkgMThoMy43NW0zIC43NUgxOGEyLjI1IDIuMjUgMCAwIDAgMi4yNS0yLjI1VjYuMTA4YzAtMS4xMzUtLjg0NS0yLjA5OC0xLjk3Ni0yLjE5MmE0OC40MjQgNDguNDI0IDAgMCAwLTEuMTIzLS4wOG0tNS44MDEgMGMtLjA2NS4yMS0uMS40MzMtLjEuNjY0IDAgLjQxNC4zMzYuNzUuNzUuNzVoNC41YS43NS43NSAwIDAgMCAuNzUtLjc1IDIuMjUgMi4yNSAwIDAgMC0uMS0uNjY0bS01LjggMEEyLjI1MSAyLjI1MSAwIDAgMSAxMy41IDIuMjVIMTVjMS4wMTIgMCAxLjg2Ny42NjggMi4xNSAxLjU4Nm0tNS44IDBjLS4zNzYuMDIzLS43NS4wNS0xLjEyNC4wOEM5LjA5NSA0LjAxIDguMjUgNC45NzMgOC4yNSA2LjEwOFY4LjI1bTAgMEg0Ljg3NWMtLjYyMSAwLTEuMTI1LjUwNC0xLjEyNSAxLjEyNXYxMS4yNWMwIC42MjEuNTA0IDEuMTI1IDEuMTI1IDEuMTI1aDkuNzVjLjYyMSAwIDEuMTI1LS41MDQgMS4xMjUtMS4xMjVWOS4zNzVjMC0uNjIxLS41MDQtMS4xMjUtMS4xMjUtMS4xMjVIOC4yNVpNNi43NSAxMmguMDA4di4wMDhINi43NVYxMlptMCAzaC4wMDh2LjAwOEg2Ljc1VjE1Wm0wIDNoLjAwOHYuMDA4SDYuNzVWMThaXCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihDbGlwYm9hcmREb2N1bWVudExpc3RJY29uKTtcbmV4cG9ydCBkZWZhdWx0IEZvcndhcmRSZWY7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\n"));

/***/ })

});