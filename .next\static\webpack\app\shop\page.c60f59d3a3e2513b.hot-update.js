"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./src/app/shop/page.tsx":
/*!*******************************!*\
  !*** ./src/app/shop/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ShopPage = ()=>{\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1000\n    ]);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { addItem } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart)();\n    // Mock data - in real app, this would come from Firebase\n    const mockProducts = [\n        {\n            id: \"1\",\n            name: \"Premium UI Kit Pro\",\n            description: \"Complete design system with 300+ components, dark mode support, and Figma files included.\",\n            price: 89.99,\n            originalPrice: 129.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"UI Kits\",\n            rating: 4.9,\n            reviews: 234,\n            inStock: true,\n            featured: true,\n            isOnSale: true,\n            tags: [\n                \"ui\",\n                \"design\",\n                \"components\",\n                \"figma\"\n            ]\n        },\n        {\n            id: \"2\",\n            name: \"React Dashboard Template\",\n            description: \"Modern admin dashboard with charts, tables, and responsive design. Built with React and TypeScript.\",\n            price: 59.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Templates\",\n            rating: 4.8,\n            reviews: 156,\n            inStock: true,\n            featured: true,\n            isOnSale: false,\n            tags: [\n                \"react\",\n                \"dashboard\",\n                \"admin\",\n                \"typescript\"\n            ]\n        },\n        {\n            id: \"3\",\n            name: \"Icon Pack Collection\",\n            description: \"2000+ premium icons in multiple formats (SVG, PNG, AI). Perfect for web and mobile apps.\",\n            price: 29.99,\n            originalPrice: 49.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Icons\",\n            rating: 4.7,\n            reviews: 89,\n            inStock: true,\n            featured: false,\n            isOnSale: true,\n            tags: [\n                \"icons\",\n                \"svg\",\n                \"design\",\n                \"mobile\"\n            ]\n        },\n        {\n            id: \"4\",\n            name: \"Animation Library\",\n            description: \"CSS and JavaScript animations for modern web applications. Easy to implement and customize.\",\n            price: 39.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Code\",\n            rating: 4.6,\n            reviews: 67,\n            inStock: true,\n            featured: false,\n            isOnSale: false,\n            tags: [\n                \"animation\",\n                \"css\",\n                \"javascript\",\n                \"web\"\n            ]\n        },\n        {\n            id: \"5\",\n            name: \"E-commerce Template Bundle\",\n            description: \"5 complete e-commerce templates with shopping cart, checkout, and admin panel.\",\n            price: 149.99,\n            originalPrice: 199.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Templates\",\n            rating: 4.9,\n            reviews: 123,\n            inStock: true,\n            featured: true,\n            isOnSale: true,\n            tags: [\n                \"ecommerce\",\n                \"shopping\",\n                \"template\",\n                \"bundle\"\n            ]\n        },\n        {\n            id: \"6\",\n            name: \"Mobile App UI Kit\",\n            description: \"Complete mobile app design system with 150+ screens and components for iOS and Android.\",\n            price: 79.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"UI Kits\",\n            rating: 4.8,\n            reviews: 198,\n            inStock: true,\n            featured: false,\n            isOnSale: false,\n            tags: [\n                \"mobile\",\n                \"app\",\n                \"ui\",\n                \"ios\",\n                \"android\"\n            ]\n        }\n    ];\n    const categories = [\n        \"all\",\n        \"Wallpapers\",\n        \"UI Kits\",\n        \"Templates\",\n        \"Icons\",\n        \"Code\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate API call\n        setTimeout(()=>{\n            setProducts(mockProducts);\n            setFilteredProducts(mockProducts);\n            setLoading(false);\n        }, 1000);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = products;\n        // Filter by search term\n        if (searchTerm) {\n            filtered = filtered.filter((product)=>product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()) || product.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n        }\n        // Filter by category\n        if (selectedCategory !== \"all\") {\n            filtered = filtered.filter((product)=>product.category === selectedCategory);\n        }\n        // Filter by price range\n        filtered = filtered.filter((product)=>product.price >= priceRange[0] && product.price <= priceRange[1]);\n        // Sort products\n        switch(sortBy){\n            case \"price-low\":\n                filtered.sort((a, b)=>a.price - b.price);\n                break;\n            case \"price-high\":\n                filtered.sort((a, b)=>b.price - a.price);\n                break;\n            case \"rating\":\n                filtered.sort((a, b)=>b.rating - a.rating);\n                break;\n            case \"newest\":\n                break;\n            default:\n                filtered.sort((a, b)=>(b.featured ? 1 : 0) - (a.featured ? 1 : 0));\n        }\n        setFilteredProducts(filtered);\n    }, [\n        products,\n        searchTerm,\n        selectedCategory,\n        sortBy,\n        priceRange\n    ]);\n    const handleAddToCart = (product)=>{\n        addItem({\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            image: product.image\n        });\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-20 min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading products...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-20 min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent\",\n                                    children: \"Shop\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Discover premium digital products, wallpapers, and resources to accelerate your projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.2\n                    },\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-600 to-yellow-500 rounded-2xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-1/2 mb-6 lg:mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-4\",\n                                            children: \"Premium Wallpapers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg opacity-90 mb-6\",\n                                            children: \"Transform your devices with our stunning collection of high-quality wallpapers. Available in multiple resolutions including 4K, perfect for any device.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"4K Quality\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Multiple Formats\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Instant Download\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/shop/wallpapers\",\n                                            className: \"inline-flex items-center space-x-2 bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Browse Wallpapers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-1/2 lg:pl-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-32 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\uD83C\\uDF05\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-24 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl\",\n                                                            children: \"\\uD83C\\uDFAE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-24 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl\",\n                                                            children: \"\\uD83C\\uDF0A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-32 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"✨\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.1\n                    },\n                    className: \"bg-white rounded-2xl shadow-lg p-6 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row gap-4 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1 max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search products...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCategory,\n                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: category,\n                                        children: category === \"all\" ? \"All Categories\" : category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: sortBy,\n                                onChange: (e)=>setSortBy(e.target.value),\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"featured\",\n                                        children: \"Featured\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"newest\",\n                                        children: \"Newest\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price-low\",\n                                        children: \"Price: Low to High\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price-high\",\n                                        children: \"Price: High to Low\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"rating\",\n                                        children: \"Highest Rated\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"grid\"),\n                                        className: \"p-2 rounded-md transition-colors \".concat(viewMode === \"grid\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"list\"),\n                                        className: \"p-2 rounded-md transition-colors \".concat(viewMode === \"list\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Showing \",\n                            filteredProducts.length,\n                            \" of \",\n                            products.length,\n                            \" products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    className: \"grid gap-6 \".concat(viewMode === \"grid\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"),\n                    children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                y: -5\n                            },\n                            className: \"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100 \".concat(viewMode === \"list\" ? \"flex\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden bg-gradient-to-br from-primary-100 to-secondary-100 \".concat(viewMode === \"list\" ? \"w-48 h-48\" : \"h-48\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-primary-400\",\n                                                children: product.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3 flex flex-col space-y-2\",\n                                            children: [\n                                                product.isOnSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-md\",\n                                                    children: \"SALE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-secondary-500 text-white text-xs font-medium px-2 py-1 rounded-md\",\n                                                    children: \"FEATURED\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            onClick: ()=>toggleFavorite(product.id),\n                                            className: \"absolute top-3 right-3 w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200\",\n                                            children: favorites.includes(product.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: ()=>handleAddToCart(product),\n                                                className: \"bg-white text-primary-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Add to Cart\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 \".concat(viewMode === \"list\" ? \"flex-1\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-primary-600 font-medium\",\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 text-yellow-400 fill-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                product.rating,\n                                                                \" (\",\n                                                                product.reviews,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: product.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        product.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 line-through\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.originalPrice\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/shop/product/\".concat(product.id),\n                                                    className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, undefined),\n                filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDD0D\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"No products found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Try adjusting your search or filter criteria\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setSearchTerm(\"\");\n                                setSelectedCategory(\"all\");\n                                setPriceRange([\n                                    0,\n                                    1000\n                                ]);\n                            },\n                            className: \"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200\",\n                            children: \"Clear Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ShopPage, \"VdfCNcrqJ6Bw7uU2puDzEnawtfc=\", false, function() {\n    return [\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.useCart\n    ];\n});\n_c = ShopPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ShopPage);\nvar _c;\n$RefreshReg$(_c, \"ShopPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/shop/page.tsx\n"));

/***/ })

});