"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/wallpapers/page",{

/***/ "(app-pages-browser)/./src/data/wallpapers.ts":
/*!********************************!*\
  !*** ./src/data/wallpapers.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToAdminWallpaper: function() { return /* binding */ convertToAdminWallpaper; },\n/* harmony export */   getFeaturedWallpapers: function() { return /* binding */ getFeaturedWallpapers; },\n/* harmony export */   getTotalDownloads: function() { return /* binding */ getTotalDownloads; },\n/* harmony export */   getWallpaperById: function() { return /* binding */ getWallpaperById; },\n/* harmony export */   getWallpapersByCategory: function() { return /* binding */ getWallpapersByCategory; },\n/* harmony export */   wallpaperCategories: function() { return /* binding */ wallpaperCategories; },\n/* harmony export */   wallpapersData: function() { return /* binding */ wallpapersData; }\n/* harmony export */ });\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/product */ \"(app-pages-browser)/./src/types/product.ts\");\n\n// Shared wallpapers data that both admin and main site will use\nconst wallpapersData = [\n    {\n        id: \"1\",\n        title: \"Abstract Neon Waves\",\n        description: \"Vibrant neon waves flowing across a dark background, perfect for modern setups\",\n        price: 4.99,\n        originalPrice: 7.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"abstract\",\n            \"neon\",\n            \"waves\",\n            \"modern\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"15-25 MB\",\n        createdAt: \"2024-01-01T00:00:00Z\",\n        updatedAt: \"2024-01-01T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"25 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"18 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"8 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF00FF\",\n                \"#00FFFF\",\n                \"#FF6B6B\",\n                \"#4ECDC4\",\n                \"#1A1A1A\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.ABSTRACT,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 1250\n        }\n    },\n    {\n        id: \"2\",\n        title: \"Minimalist Mountain Range\",\n        description: \"Clean, minimalist mountain silhouettes with gradient sky\",\n        price: 3.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"minimalist\",\n            \"mountains\",\n            \"nature\",\n            \"gradient\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"10-20 MB\",\n        createdAt: \"2024-01-02T00:00:00Z\",\n        updatedAt: \"2024-01-02T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"20 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"10 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#87CEEB\",\n                \"#FFB6C1\",\n                \"#DDA0DD\",\n                \"#F0F8FF\",\n                \"#2F4F4F\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.MINIMALIST,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.ULTRAWIDE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 890\n        }\n    },\n    {\n        id: \"3\",\n        title: \"Gaming RGB Setup\",\n        description: \"Dynamic RGB lighting effects perfect for gaming setups\",\n        price: 5.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"gaming\",\n            \"rgb\",\n            \"neon\",\n            \"tech\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"20-30 MB\",\n        createdAt: \"2024-01-03T00:00:00Z\",\n        updatedAt: \"2024-01-03T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"30 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"22 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 3440,\n                    height: 1440,\n                    label: \"Ultrawide\",\n                    downloadUrl: \"/downloads/ultrawide\",\n                    fileSize: \"25 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF0080\",\n                \"#00FF80\",\n                \"#8000FF\",\n                \"#FF8000\",\n                \"#000000\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.GAMING,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.ULTRAWIDE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 2100\n        }\n    },\n    {\n        id: \"4\",\n        title: \"Nature Forest Path\",\n        description: \"Serene forest path with morning sunlight filtering through trees\",\n        price: 2.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"nature\",\n            \"forest\",\n            \"peaceful\",\n            \"photography\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\"\n        ],\n        fileSize: \"12-18 MB\",\n        createdAt: \"2024-01-04T00:00:00Z\",\n        updatedAt: \"2024-01-04T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"18 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"6 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#228B22\",\n                \"#8FBC8F\",\n                \"#F5DEB3\",\n                \"#DEB887\",\n                \"#654321\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.NATURE,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 756\n        }\n    },\n    {\n        id: \"5\",\n        title: \"Geometric Patterns\",\n        description: \"Modern geometric patterns in vibrant colors\",\n        price: 3.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"geometric\",\n            \"patterns\",\n            \"vibrant\",\n            \"modern\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"15-22 MB\",\n        createdAt: \"2024-01-05T00:00:00Z\",\n        updatedAt: \"2024-01-05T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"22 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"16 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"8 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF6B6B\",\n                \"#4ECDC4\",\n                \"#45B7D1\",\n                \"#96CEB4\",\n                \"#FFEAA7\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.GEOMETRIC,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 1420\n        }\n    },\n    {\n        id: \"6\",\n        title: \"Dark Aesthetic\",\n        description: \"Sleek dark theme wallpaper with subtle textures\",\n        price: 2.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"dark\",\n            \"aesthetic\",\n            \"minimal\",\n            \"texture\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"8-15 MB\",\n        createdAt: \"2024-01-06T00:00:00Z\",\n        updatedAt: \"2024-01-06T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"8 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"5 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#1A1A1A\",\n                \"#2D2D2D\",\n                \"#404040\",\n                \"#666666\",\n                \"#808080\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.DARK,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 980\n        }\n    }\n];\n// Helper functions for wallpaper management\nconst getWallpaperById = (id)=>{\n    return wallpapersData.find((wallpaper)=>wallpaper.id === id);\n};\nconst getFeaturedWallpapers = ()=>{\n    return wallpapersData.filter((wallpaper)=>wallpaper.featured);\n};\nconst getWallpapersByCategory = (style)=>{\n    return wallpapersData.filter((wallpaper)=>{\n        var _wallpaper_wallpaperData;\n        return ((_wallpaper_wallpaperData = wallpaper.wallpaperData) === null || _wallpaper_wallpaperData === void 0 ? void 0 : _wallpaper_wallpaperData.style) === style;\n    });\n};\nconst getTotalDownloads = ()=>{\n    return wallpapersData.reduce((total, wallpaper)=>{\n        var _wallpaper_wallpaperData;\n        return total + (((_wallpaper_wallpaperData = wallpaper.wallpaperData) === null || _wallpaper_wallpaperData === void 0 ? void 0 : _wallpaper_wallpaperData.downloadCount) || 0);\n    }, 0);\n};\n// Categories for filtering\nconst wallpaperCategories = [\n    \"All\",\n    \"Abstract\",\n    \"Minimalist\",\n    \"Gaming\",\n    \"Nature\",\n    \"Geometric\",\n    \"Dark\"\n];\n// Convert Product to admin Wallpaper format\nconst convertToAdminWallpaper = (product)=>{\n    var _product_wallpaperData, _product_wallpaperData1, _product_wallpaperData2, _product_wallpaperData3, _product_wallpaperData4, _product_wallpaperData5;\n    return {\n        id: product.id,\n        title: product.title,\n        description: product.description,\n        category: ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.style) || \"Abstract\",\n        tags: product.tags,\n        price: product.price,\n        originalPrice: product.originalPrice,\n        featured: product.featured,\n        downloads: ((_product_wallpaperData1 = product.wallpaperData) === null || _product_wallpaperData1 === void 0 ? void 0 : _product_wallpaperData1.downloadCount) || 0,\n        imageUrl: product.images[0],\n        thumbnailUrl: ((_product_wallpaperData2 = product.wallpaperData) === null || _product_wallpaperData2 === void 0 ? void 0 : _product_wallpaperData2.watermarkedPreview) || product.images[0],\n        resolutions: {\n            desktop: ((_product_wallpaperData3 = product.wallpaperData) === null || _product_wallpaperData3 === void 0 ? void 0 : _product_wallpaperData3.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\")) ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").downloadUrl\n            } : {\n                width: 1920,\n                height: 1080,\n                url: \"/api/placeholder/1920/1080\"\n            },\n            mobile: ((_product_wallpaperData4 = product.wallpaperData) === null || _product_wallpaperData4 === void 0 ? void 0 : _product_wallpaperData4.resolutions.find((r)=>r.label === \"Mobile\")) ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").downloadUrl\n            } : {\n                width: 1080,\n                height: 1920,\n                url: \"/api/placeholder/1080/1920\"\n            },\n            tablet: ((_product_wallpaperData5 = product.wallpaperData) === null || _product_wallpaperData5 === void 0 ? void 0 : _product_wallpaperData5.resolutions.find((r)=>r.label === \"Tablet\")) ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").downloadUrl\n            } : {\n                width: 1536,\n                height: 2048,\n                url: \"/api/placeholder/1536/2048\"\n            }\n        },\n        createdAt: product.createdAt,\n        updatedAt: product.updatedAt\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9kYXRhL3dhbGxwYXBlcnMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBb0c7QUFFcEcsZ0VBQWdFO0FBQ3pELE1BQU1JLGlCQUE0QjtJQUN2QztRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLGVBQWU7UUFDZkMsVUFBVVYsMkRBQWVBLENBQUNXLFVBQVU7UUFDcENDLE1BQU1YLHVEQUFXQSxDQUFDWSxPQUFPO1FBQ3pCQyxRQUFRO1lBQUM7U0FBMkI7UUFDcENDLE1BQU07WUFBQztZQUFZO1lBQVE7WUFBUztTQUFTO1FBQzdDQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsV0FBVztRQUNYQyxZQUFZO1lBQUM7WUFBTztTQUFNO1FBQzFCQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxlQUFlO1lBQ2JDLGFBQWE7Z0JBQ1g7b0JBQUVDLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQU1DLGFBQWE7b0JBQWlCUixVQUFVO2dCQUFRO2dCQUMxRjtvQkFBRUssT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBU0MsYUFBYTtvQkFBb0JSLFVBQVU7Z0JBQVE7Z0JBQ2hHO29CQUFFSyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFTQyxhQUFhO29CQUFvQlIsVUFBVTtnQkFBUTtnQkFDaEc7b0JBQUVLLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQVVDLGFBQWE7b0JBQXFCUixVQUFVO2dCQUFPO2FBQ2xHO1lBQ0RTLGFBQWE7WUFDYkMsY0FBYztnQkFBQztnQkFBVztnQkFBVztnQkFBVztnQkFBVzthQUFVO1lBQ3JFQyxPQUFPN0IsMERBQWNBLENBQUM4QixRQUFRO1lBQzlCQyxxQkFBcUI7Z0JBQUM5QixzREFBVUEsQ0FBQytCLE9BQU87Z0JBQUUvQixzREFBVUEsQ0FBQ2dDLE1BQU07Z0JBQUVoQyxzREFBVUEsQ0FBQ2lDLE1BQU07YUFBQztZQUMvRUMsWUFBWTtZQUNaQyxvQkFBb0I7WUFDcEJDLGVBQWU7UUFDakI7SUFDRjtJQUNBO1FBQ0VsQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BFLFVBQVVWLDJEQUFlQSxDQUFDVyxVQUFVO1FBQ3BDQyxNQUFNWCx1REFBV0EsQ0FBQ1ksT0FBTztRQUN6QkMsUUFBUTtZQUFDO1NBQTJCO1FBQ3BDQyxNQUFNO1lBQUM7WUFBYztZQUFhO1lBQVU7U0FBVztRQUN2REMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsWUFBWTtZQUFDO1lBQU87U0FBTTtRQUMxQkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsZUFBZTtZQUNiQyxhQUFhO2dCQUNYO29CQUFFQyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFNQyxhQUFhO29CQUFpQlIsVUFBVTtnQkFBUTtnQkFDMUY7b0JBQUVLLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQVNDLGFBQWE7b0JBQW9CUixVQUFVO2dCQUFRO2dCQUNoRztvQkFBRUssT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBU0MsYUFBYTtvQkFBb0JSLFVBQVU7Z0JBQVE7YUFDakc7WUFDRFMsYUFBYTtZQUNiQyxjQUFjO2dCQUFDO2dCQUFXO2dCQUFXO2dCQUFXO2dCQUFXO2FBQVU7WUFDckVDLE9BQU83QiwwREFBY0EsQ0FBQ3NDLFVBQVU7WUFDaENQLHFCQUFxQjtnQkFBQzlCLHNEQUFVQSxDQUFDK0IsT0FBTztnQkFBRS9CLHNEQUFVQSxDQUFDZ0MsTUFBTTtnQkFBRWhDLHNEQUFVQSxDQUFDc0MsU0FBUzthQUFDO1lBQ2xGSixZQUFZO1lBQ1pDLG9CQUFvQjtZQUNwQkMsZUFBZTtRQUNqQjtJQUNGO0lBQ0E7UUFDRWxDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEUsVUFBVVYsMkRBQWVBLENBQUNXLFVBQVU7UUFDcENDLE1BQU1YLHVEQUFXQSxDQUFDWSxPQUFPO1FBQ3pCQyxRQUFRO1lBQUM7U0FBMkI7UUFDcENDLE1BQU07WUFBQztZQUFVO1lBQU87WUFBUTtTQUFPO1FBQ3ZDQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsV0FBVztRQUNYQyxZQUFZO1lBQUM7WUFBTztTQUFNO1FBQzFCQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxlQUFlO1lBQ2JDLGFBQWE7Z0JBQ1g7b0JBQUVDLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQU1DLGFBQWE7b0JBQWlCUixVQUFVO2dCQUFRO2dCQUMxRjtvQkFBRUssT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBU0MsYUFBYTtvQkFBb0JSLFVBQVU7Z0JBQVE7Z0JBQ2hHO29CQUFFSyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFTQyxhQUFhO29CQUFvQlIsVUFBVTtnQkFBUTtnQkFDaEc7b0JBQUVLLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQWFDLGFBQWE7b0JBQXdCUixVQUFVO2dCQUFRO2FBQ3pHO1lBQ0RTLGFBQWE7WUFDYkMsY0FBYztnQkFBQztnQkFBVztnQkFBVztnQkFBVztnQkFBVzthQUFVO1lBQ3JFQyxPQUFPN0IsMERBQWNBLENBQUN3QyxNQUFNO1lBQzVCVCxxQkFBcUI7Z0JBQUM5QixzREFBVUEsQ0FBQytCLE9BQU87Z0JBQUUvQixzREFBVUEsQ0FBQ2dDLE1BQU07Z0JBQUVoQyxzREFBVUEsQ0FBQ3NDLFNBQVM7YUFBQztZQUNsRkosWUFBWTtZQUNaQyxvQkFBb0I7WUFDcEJDLGVBQWU7UUFDakI7SUFDRjtJQUNBO1FBQ0VsQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BFLFVBQVVWLDJEQUFlQSxDQUFDVyxVQUFVO1FBQ3BDQyxNQUFNWCx1REFBV0EsQ0FBQ1ksT0FBTztRQUN6QkMsUUFBUTtZQUFDO1NBQTJCO1FBQ3BDQyxNQUFNO1lBQUM7WUFBVTtZQUFVO1lBQVk7U0FBYztRQUNyREMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsWUFBWTtZQUFDO1NBQU07UUFDbkJDLFVBQVU7UUFDVkMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLGVBQWU7WUFDYkMsYUFBYTtnQkFDWDtvQkFBRUMsT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBTUMsYUFBYTtvQkFBaUJSLFVBQVU7Z0JBQVE7Z0JBQzFGO29CQUFFSyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFTQyxhQUFhO29CQUFvQlIsVUFBVTtnQkFBUTtnQkFDaEc7b0JBQUVLLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQVVDLGFBQWE7b0JBQXFCUixVQUFVO2dCQUFPO2FBQ2xHO1lBQ0RTLGFBQWE7WUFDYkMsY0FBYztnQkFBQztnQkFBVztnQkFBVztnQkFBVztnQkFBVzthQUFVO1lBQ3JFQyxPQUFPN0IsMERBQWNBLENBQUN5QyxNQUFNO1lBQzVCVixxQkFBcUI7Z0JBQUM5QixzREFBVUEsQ0FBQytCLE9BQU87Z0JBQUUvQixzREFBVUEsQ0FBQ2dDLE1BQU07Z0JBQUVoQyxzREFBVUEsQ0FBQ2lDLE1BQU07Z0JBQUVqQyxzREFBVUEsQ0FBQ3lDLE1BQU07YUFBQztZQUNsR1AsWUFBWTtZQUNaQyxvQkFBb0I7WUFDcEJDLGVBQWU7UUFDakI7SUFDRjtJQUNBO1FBQ0VsQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BFLFVBQVVWLDJEQUFlQSxDQUFDVyxVQUFVO1FBQ3BDQyxNQUFNWCx1REFBV0EsQ0FBQ1ksT0FBTztRQUN6QkMsUUFBUTtZQUFDO1NBQTJCO1FBQ3BDQyxNQUFNO1lBQUM7WUFBYTtZQUFZO1lBQVc7U0FBUztRQUNwREMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsWUFBWTtZQUFDO1lBQU87U0FBTTtRQUMxQkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsZUFBZTtZQUNiQyxhQUFhO2dCQUNYO29CQUFFQyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFNQyxhQUFhO29CQUFpQlIsVUFBVTtnQkFBUTtnQkFDMUY7b0JBQUVLLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQVNDLGFBQWE7b0JBQW9CUixVQUFVO2dCQUFRO2dCQUNoRztvQkFBRUssT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBU0MsYUFBYTtvQkFBb0JSLFVBQVU7Z0JBQVE7Z0JBQ2hHO29CQUFFSyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFVQyxhQUFhO29CQUFxQlIsVUFBVTtnQkFBTzthQUNsRztZQUNEUyxhQUFhO1lBQ2JDLGNBQWM7Z0JBQUM7Z0JBQVc7Z0JBQVc7Z0JBQVc7Z0JBQVc7YUFBVTtZQUNyRUMsT0FBTzdCLDBEQUFjQSxDQUFDMkMsU0FBUztZQUMvQloscUJBQXFCO2dCQUFDOUIsc0RBQVVBLENBQUMrQixPQUFPO2dCQUFFL0Isc0RBQVVBLENBQUNnQyxNQUFNO2dCQUFFaEMsc0RBQVVBLENBQUNpQyxNQUFNO2dCQUFFakMsc0RBQVVBLENBQUN5QyxNQUFNO2FBQUM7WUFDbEdQLFlBQVk7WUFDWkMsb0JBQW9CO1lBQ3BCQyxlQUFlO1FBQ2pCO0lBQ0Y7SUFDQTtRQUNFbEMsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsT0FBTztRQUNQRSxVQUFVViwyREFBZUEsQ0FBQ1csVUFBVTtRQUNwQ0MsTUFBTVgsdURBQVdBLENBQUNZLE9BQU87UUFDekJDLFFBQVE7WUFBQztTQUEyQjtRQUNwQ0MsTUFBTTtZQUFDO1lBQVE7WUFBYTtZQUFXO1NBQVU7UUFDakRDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLFlBQVk7WUFBQztZQUFPO1NBQU07UUFDMUJDLFVBQVU7UUFDVkMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLGVBQWU7WUFDYkMsYUFBYTtnQkFDWDtvQkFBRUMsT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBTUMsYUFBYTtvQkFBaUJSLFVBQVU7Z0JBQVE7Z0JBQzFGO29CQUFFSyxPQUFPO29CQUFNQyxRQUFRO29CQUFNQyxPQUFPO29CQUFTQyxhQUFhO29CQUFvQlIsVUFBVTtnQkFBUTtnQkFDaEc7b0JBQUVLLE9BQU87b0JBQU1DLFFBQVE7b0JBQU1DLE9BQU87b0JBQVNDLGFBQWE7b0JBQW9CUixVQUFVO2dCQUFPO2dCQUMvRjtvQkFBRUssT0FBTztvQkFBTUMsUUFBUTtvQkFBTUMsT0FBTztvQkFBVUMsYUFBYTtvQkFBcUJSLFVBQVU7Z0JBQU87YUFDbEc7WUFDRFMsYUFBYTtZQUNiQyxjQUFjO2dCQUFDO2dCQUFXO2dCQUFXO2dCQUFXO2dCQUFXO2FBQVU7WUFDckVDLE9BQU83QiwwREFBY0EsQ0FBQzRDLElBQUk7WUFDMUJiLHFCQUFxQjtnQkFBQzlCLHNEQUFVQSxDQUFDK0IsT0FBTztnQkFBRS9CLHNEQUFVQSxDQUFDZ0MsTUFBTTtnQkFBRWhDLHNEQUFVQSxDQUFDaUMsTUFBTTtnQkFBRWpDLHNEQUFVQSxDQUFDeUMsTUFBTTthQUFDO1lBQ2xHUCxZQUFZO1lBQ1pDLG9CQUFvQjtZQUNwQkMsZUFBZTtRQUNqQjtJQUNGO0NBQ0QsQ0FBQztBQUVGLDRDQUE0QztBQUNyQyxNQUFNUSxtQkFBbUIsQ0FBQzFDO0lBQy9CLE9BQU9ELGVBQWU0QyxJQUFJLENBQUNDLENBQUFBLFlBQWFBLFVBQVU1QyxFQUFFLEtBQUtBO0FBQzNELEVBQUU7QUFFSyxNQUFNNkMsd0JBQXdCO0lBQ25DLE9BQU85QyxlQUFlK0MsTUFBTSxDQUFDRixDQUFBQSxZQUFhQSxVQUFVaEMsUUFBUTtBQUM5RCxFQUFFO0FBRUssTUFBTW1DLDBCQUEwQixDQUFDckI7SUFDdEMsT0FBTzNCLGVBQWUrQyxNQUFNLENBQUNGLENBQUFBO1lBQWFBO2VBQUFBLEVBQUFBLDJCQUFBQSxVQUFVMUIsYUFBYSxjQUF2QjBCLCtDQUFBQSx5QkFBeUJsQixLQUFLLE1BQUtBOztBQUMvRSxFQUFFO0FBRUssTUFBTXNCLG9CQUFvQjtJQUMvQixPQUFPakQsZUFBZWtELE1BQU0sQ0FBQyxDQUFDQyxPQUFPTjtZQUMxQkE7ZUFBVE0sUUFBU04sQ0FBQUEsRUFBQUEsMkJBQUFBLFVBQVUxQixhQUFhLGNBQXZCMEIsK0NBQUFBLHlCQUF5QlYsYUFBYSxLQUFJO09BQUk7QUFFM0QsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNaUIsc0JBQXNCO0lBQ2pDO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0QsQ0FBQztBQUVGLDRDQUE0QztBQUNyQyxNQUFNQywwQkFBMEIsQ0FBQ0M7UUFLMUJBLHdCQUtDQSx5QkFFR0EseUJBRUhBLHlCQUtEQSx5QkFLQUE7SUF2QlosT0FBTztRQUNMckQsSUFBSXFELFFBQVFyRCxFQUFFO1FBQ2RDLE9BQU9vRCxRQUFRcEQsS0FBSztRQUNwQkMsYUFBYW1ELFFBQVFuRCxXQUFXO1FBQ2hDRyxVQUFVZ0QsRUFBQUEseUJBQUFBLFFBQVFuQyxhQUFhLGNBQXJCbUMsNkNBQUFBLHVCQUF1QjNCLEtBQUssS0FBSTtRQUMxQ2hCLE1BQU0yQyxRQUFRM0MsSUFBSTtRQUNsQlAsT0FBT2tELFFBQVFsRCxLQUFLO1FBQ3BCQyxlQUFlaUQsUUFBUWpELGFBQWE7UUFDcENRLFVBQVV5QyxRQUFRekMsUUFBUTtRQUMxQjBDLFdBQVdELEVBQUFBLDBCQUFBQSxRQUFRbkMsYUFBYSxjQUFyQm1DLDhDQUFBQSx3QkFBdUJuQixhQUFhLEtBQUk7UUFDbkRxQixVQUFVRixRQUFRNUMsTUFBTSxDQUFDLEVBQUU7UUFDM0IrQyxjQUFjSCxFQUFBQSwwQkFBQUEsUUFBUW5DLGFBQWEsY0FBckJtQyw4Q0FBQUEsd0JBQXVCcEIsa0JBQWtCLEtBQUlvQixRQUFRNUMsTUFBTSxDQUFDLEVBQUU7UUFDNUVVLGFBQWE7WUFDWHNDLFNBQVNKLEVBQUFBLDBCQUFBQSxRQUFRbkMsYUFBYSxjQUFyQm1DLDhDQUFBQSx3QkFBdUJsQyxXQUFXLENBQUN3QixJQUFJLENBQUNlLENBQUFBLElBQUtBLEVBQUVwQyxLQUFLLEtBQUssV0FBV29DLEVBQUVwQyxLQUFLLEtBQUssWUFBVztnQkFDbEdGLE9BQU9pQyxRQUFRbkMsYUFBYSxDQUFDQyxXQUFXLENBQUN3QixJQUFJLENBQUNlLENBQUFBLElBQUtBLEVBQUVwQyxLQUFLLEtBQUssV0FBV29DLEVBQUVwQyxLQUFLLEtBQUssU0FBVUYsS0FBSztnQkFDckdDLFFBQVFnQyxRQUFRbkMsYUFBYSxDQUFDQyxXQUFXLENBQUN3QixJQUFJLENBQUNlLENBQUFBLElBQUtBLEVBQUVwQyxLQUFLLEtBQUssV0FBV29DLEVBQUVwQyxLQUFLLEtBQUssU0FBVUQsTUFBTTtnQkFDdkdzQyxLQUFLTixRQUFRbkMsYUFBYSxDQUFDQyxXQUFXLENBQUN3QixJQUFJLENBQUNlLENBQUFBLElBQUtBLEVBQUVwQyxLQUFLLEtBQUssV0FBV29DLEVBQUVwQyxLQUFLLEtBQUssU0FBVUMsV0FBVztZQUMzRyxJQUFJO2dCQUFFSCxPQUFPO2dCQUFNQyxRQUFRO2dCQUFNc0MsS0FBSztZQUE2QjtZQUNuRUMsUUFBUVAsRUFBQUEsMEJBQUFBLFFBQVFuQyxhQUFhLGNBQXJCbUMsOENBQUFBLHdCQUF1QmxDLFdBQVcsQ0FBQ3dCLElBQUksQ0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRXBDLEtBQUssS0FBSyxhQUFZO2dCQUMzRUYsT0FBT2lDLFFBQVFuQyxhQUFhLENBQUNDLFdBQVcsQ0FBQ3dCLElBQUksQ0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRXBDLEtBQUssS0FBSyxVQUFXRixLQUFLO2dCQUMvRUMsUUFBUWdDLFFBQVFuQyxhQUFhLENBQUNDLFdBQVcsQ0FBQ3dCLElBQUksQ0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRXBDLEtBQUssS0FBSyxVQUFXRCxNQUFNO2dCQUNqRnNDLEtBQUtOLFFBQVFuQyxhQUFhLENBQUNDLFdBQVcsQ0FBQ3dCLElBQUksQ0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRXBDLEtBQUssS0FBSyxVQUFXQyxXQUFXO1lBQ3JGLElBQUk7Z0JBQUVILE9BQU87Z0JBQU1DLFFBQVE7Z0JBQU1zQyxLQUFLO1lBQTZCO1lBQ25FRSxRQUFRUixFQUFBQSwwQkFBQUEsUUFBUW5DLGFBQWEsY0FBckJtQyw4Q0FBQUEsd0JBQXVCbEMsV0FBVyxDQUFDd0IsSUFBSSxDQUFDZSxDQUFBQSxJQUFLQSxFQUFFcEMsS0FBSyxLQUFLLGFBQVk7Z0JBQzNFRixPQUFPaUMsUUFBUW5DLGFBQWEsQ0FBQ0MsV0FBVyxDQUFDd0IsSUFBSSxDQUFDZSxDQUFBQSxJQUFLQSxFQUFFcEMsS0FBSyxLQUFLLFVBQVdGLEtBQUs7Z0JBQy9FQyxRQUFRZ0MsUUFBUW5DLGFBQWEsQ0FBQ0MsV0FBVyxDQUFDd0IsSUFBSSxDQUFDZSxDQUFBQSxJQUFLQSxFQUFFcEMsS0FBSyxLQUFLLFVBQVdELE1BQU07Z0JBQ2pGc0MsS0FBS04sUUFBUW5DLGFBQWEsQ0FBQ0MsV0FBVyxDQUFDd0IsSUFBSSxDQUFDZSxDQUFBQSxJQUFLQSxFQUFFcEMsS0FBSyxLQUFLLFVBQVdDLFdBQVc7WUFDckYsSUFBSTtnQkFBRUgsT0FBTztnQkFBTUMsUUFBUTtnQkFBTXNDLEtBQUs7WUFBNkI7UUFDckU7UUFDQTNDLFdBQVdxQyxRQUFRckMsU0FBUztRQUM1QkMsV0FBV29DLFFBQVFwQyxTQUFTO0lBQzlCO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvZGF0YS93YWxscGFwZXJzLnRzPzA0OWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJvZHVjdCwgUHJvZHVjdENhdGVnb3J5LCBQcm9kdWN0VHlwZSwgV2FsbHBhcGVyU3R5bGUsIERldmljZVR5cGUgfSBmcm9tICdAL3R5cGVzL3Byb2R1Y3QnO1xuXG4vLyBTaGFyZWQgd2FsbHBhcGVycyBkYXRhIHRoYXQgYm90aCBhZG1pbiBhbmQgbWFpbiBzaXRlIHdpbGwgdXNlXG5leHBvcnQgY29uc3Qgd2FsbHBhcGVyc0RhdGE6IFByb2R1Y3RbXSA9IFtcbiAge1xuICAgIGlkOiAnMScsXG4gICAgdGl0bGU6ICdBYnN0cmFjdCBOZW9uIFdhdmVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1ZpYnJhbnQgbmVvbiB3YXZlcyBmbG93aW5nIGFjcm9zcyBhIGRhcmsgYmFja2dyb3VuZCwgcGVyZmVjdCBmb3IgbW9kZXJuIHNldHVwcycsXG4gICAgcHJpY2U6IDQuOTksXG4gICAgb3JpZ2luYWxQcmljZTogNy45OSxcbiAgICBjYXRlZ29yeTogUHJvZHVjdENhdGVnb3J5LldBTExQQVBFUlMsXG4gICAgdHlwZTogUHJvZHVjdFR5cGUuRElHSVRBTCxcbiAgICBpbWFnZXM6IFsnL2FwaS9wbGFjZWhvbGRlci80MDAvNjAwJ10sXG4gICAgdGFnczogWydhYnN0cmFjdCcsICduZW9uJywgJ3dhdmVzJywgJ21vZGVybiddLFxuICAgIGluU3RvY2s6IHRydWUsXG4gICAgZmVhdHVyZWQ6IHRydWUsXG4gICAgaXNEaWdpdGFsOiB0cnVlLFxuICAgIGZpbGVGb3JtYXQ6IFsnSlBHJywgJ1BORyddLFxuICAgIGZpbGVTaXplOiAnMTUtMjUgTUInLFxuICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICB1cGRhdGVkQXQ6ICcyMDI0LTAxLTAxVDAwOjAwOjAwWicsXG4gICAgd2FsbHBhcGVyRGF0YToge1xuICAgICAgcmVzb2x1dGlvbnM6IFtcbiAgICAgICAgeyB3aWR0aDogMzg0MCwgaGVpZ2h0OiAyMTYwLCBsYWJlbDogJzRLJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzRrJywgZmlsZVNpemU6ICcyNSBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMjU2MCwgaGVpZ2h0OiAxNDQwLCBsYWJlbDogJzE0NDBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzE0NDBwJywgZmlsZVNpemU6ICcxOCBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMTkyMCwgaGVpZ2h0OiAxMDgwLCBsYWJlbDogJzEwODBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzEwODBwJywgZmlsZVNpemU6ICcxMiBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMTA4MCwgaGVpZ2h0OiAyMzQwLCBsYWJlbDogJ01vYmlsZScsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy9tb2JpbGUnLCBmaWxlU2l6ZTogJzggTUInIH0sXG4gICAgICBdLFxuICAgICAgYXNwZWN0UmF0aW86ICcxNjo5JyxcbiAgICAgIGNvbG9yUGFsZXR0ZTogWycjRkYwMEZGJywgJyMwMEZGRkYnLCAnI0ZGNkI2QicsICcjNEVDREM0JywgJyMxQTFBMUEnXSxcbiAgICAgIHN0eWxlOiBXYWxscGFwZXJTdHlsZS5BQlNUUkFDVCxcbiAgICAgIGRldmljZUNvbXBhdGliaWxpdHk6IFtEZXZpY2VUeXBlLkRFU0tUT1AsIERldmljZVR5cGUuTEFQVE9QLCBEZXZpY2VUeXBlLk1PQklMRV0sXG4gICAgICBwcmV2aWV3VXJsOiAnL2FwaS9wbGFjZWhvbGRlci84MDAvNjAwJyxcbiAgICAgIHdhdGVybWFya2VkUHJldmlldzogJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzYwMCcsXG4gICAgICBkb3dubG9hZENvdW50OiAxMjUwLFxuICAgIH0sXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIHRpdGxlOiAnTWluaW1hbGlzdCBNb3VudGFpbiBSYW5nZScsXG4gICAgZGVzY3JpcHRpb246ICdDbGVhbiwgbWluaW1hbGlzdCBtb3VudGFpbiBzaWxob3VldHRlcyB3aXRoIGdyYWRpZW50IHNreScsXG4gICAgcHJpY2U6IDMuOTksXG4gICAgY2F0ZWdvcnk6IFByb2R1Y3RDYXRlZ29yeS5XQUxMUEFQRVJTLFxuICAgIHR5cGU6IFByb2R1Y3RUeXBlLkRJR0lUQUwsXG4gICAgaW1hZ2VzOiBbJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzYwMCddLFxuICAgIHRhZ3M6IFsnbWluaW1hbGlzdCcsICdtb3VudGFpbnMnLCAnbmF0dXJlJywgJ2dyYWRpZW50J10sXG4gICAgaW5TdG9jazogdHJ1ZSxcbiAgICBmZWF0dXJlZDogZmFsc2UsXG4gICAgaXNEaWdpdGFsOiB0cnVlLFxuICAgIGZpbGVGb3JtYXQ6IFsnSlBHJywgJ1BORyddLFxuICAgIGZpbGVTaXplOiAnMTAtMjAgTUInLFxuICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDJUMDA6MDA6MDBaJyxcbiAgICB1cGRhdGVkQXQ6ICcyMDI0LTAxLTAyVDAwOjAwOjAwWicsXG4gICAgd2FsbHBhcGVyRGF0YToge1xuICAgICAgcmVzb2x1dGlvbnM6IFtcbiAgICAgICAgeyB3aWR0aDogMzg0MCwgaGVpZ2h0OiAyMTYwLCBsYWJlbDogJzRLJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzRrJywgZmlsZVNpemU6ICcyMCBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMjU2MCwgaGVpZ2h0OiAxNDQwLCBsYWJlbDogJzE0NDBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzE0NDBwJywgZmlsZVNpemU6ICcxNSBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMTkyMCwgaGVpZ2h0OiAxMDgwLCBsYWJlbDogJzEwODBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzEwODBwJywgZmlsZVNpemU6ICcxMCBNQicgfSxcbiAgICAgIF0sXG4gICAgICBhc3BlY3RSYXRpbzogJzE2OjknLFxuICAgICAgY29sb3JQYWxldHRlOiBbJyM4N0NFRUInLCAnI0ZGQjZDMScsICcjRERBMEREJywgJyNGMEY4RkYnLCAnIzJGNEY0RiddLFxuICAgICAgc3R5bGU6IFdhbGxwYXBlclN0eWxlLk1JTklNQUxJU1QsXG4gICAgICBkZXZpY2VDb21wYXRpYmlsaXR5OiBbRGV2aWNlVHlwZS5ERVNLVE9QLCBEZXZpY2VUeXBlLkxBUFRPUCwgRGV2aWNlVHlwZS5VTFRSQVdJREVdLFxuICAgICAgcHJldmlld1VybDogJy9hcGkvcGxhY2Vob2xkZXIvODAwLzYwMCcsXG4gICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgZG93bmxvYWRDb3VudDogODkwLFxuICAgIH0sXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIHRpdGxlOiAnR2FtaW5nIFJHQiBTZXR1cCcsXG4gICAgZGVzY3JpcHRpb246ICdEeW5hbWljIFJHQiBsaWdodGluZyBlZmZlY3RzIHBlcmZlY3QgZm9yIGdhbWluZyBzZXR1cHMnLFxuICAgIHByaWNlOiA1Ljk5LFxuICAgIGNhdGVnb3J5OiBQcm9kdWN0Q2F0ZWdvcnkuV0FMTFBBUEVSUyxcbiAgICB0eXBlOiBQcm9kdWN0VHlwZS5ESUdJVEFMLFxuICAgIGltYWdlczogWycvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnXSxcbiAgICB0YWdzOiBbJ2dhbWluZycsICdyZ2InLCAnbmVvbicsICd0ZWNoJ10sXG4gICAgaW5TdG9jazogdHJ1ZSxcbiAgICBmZWF0dXJlZDogdHJ1ZSxcbiAgICBpc0RpZ2l0YWw6IHRydWUsXG4gICAgZmlsZUZvcm1hdDogWydKUEcnLCAnUE5HJ10sXG4gICAgZmlsZVNpemU6ICcyMC0zMCBNQicsXG4gICAgY3JlYXRlZEF0OiAnMjAyNC0wMS0wM1QwMDowMDowMFonLFxuICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDNUMDA6MDA6MDBaJyxcbiAgICB3YWxscGFwZXJEYXRhOiB7XG4gICAgICByZXNvbHV0aW9uczogW1xuICAgICAgICB7IHdpZHRoOiAzODQwLCBoZWlnaHQ6IDIxNjAsIGxhYmVsOiAnNEsnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvNGsnLCBmaWxlU2l6ZTogJzMwIE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAyNTYwLCBoZWlnaHQ6IDE0NDAsIGxhYmVsOiAnMTQ0MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTQ0MHAnLCBmaWxlU2l6ZTogJzIyIE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAxOTIwLCBoZWlnaHQ6IDEwODAsIGxhYmVsOiAnMTA4MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTA4MHAnLCBmaWxlU2l6ZTogJzE1IE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAzNDQwLCBoZWlnaHQ6IDE0NDAsIGxhYmVsOiAnVWx0cmF3aWRlJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzL3VsdHJhd2lkZScsIGZpbGVTaXplOiAnMjUgTUInIH0sXG4gICAgICBdLFxuICAgICAgYXNwZWN0UmF0aW86ICcxNjo5JyxcbiAgICAgIGNvbG9yUGFsZXR0ZTogWycjRkYwMDgwJywgJyMwMEZGODAnLCAnIzgwMDBGRicsICcjRkY4MDAwJywgJyMwMDAwMDAnXSxcbiAgICAgIHN0eWxlOiBXYWxscGFwZXJTdHlsZS5HQU1JTkcsXG4gICAgICBkZXZpY2VDb21wYXRpYmlsaXR5OiBbRGV2aWNlVHlwZS5ERVNLVE9QLCBEZXZpY2VUeXBlLkxBUFRPUCwgRGV2aWNlVHlwZS5VTFRSQVdJREVdLFxuICAgICAgcHJldmlld1VybDogJy9hcGkvcGxhY2Vob2xkZXIvODAwLzYwMCcsXG4gICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgZG93bmxvYWRDb3VudDogMjEwMCxcbiAgICB9LFxuICB9LFxuICB7XG4gICAgaWQ6ICc0JyxcbiAgICB0aXRsZTogJ05hdHVyZSBGb3Jlc3QgUGF0aCcsXG4gICAgZGVzY3JpcHRpb246ICdTZXJlbmUgZm9yZXN0IHBhdGggd2l0aCBtb3JuaW5nIHN1bmxpZ2h0IGZpbHRlcmluZyB0aHJvdWdoIHRyZWVzJyxcbiAgICBwcmljZTogMi45OSxcbiAgICBjYXRlZ29yeTogUHJvZHVjdENhdGVnb3J5LldBTExQQVBFUlMsXG4gICAgdHlwZTogUHJvZHVjdFR5cGUuRElHSVRBTCxcbiAgICBpbWFnZXM6IFsnL2FwaS9wbGFjZWhvbGRlci80MDAvNjAwJ10sXG4gICAgdGFnczogWyduYXR1cmUnLCAnZm9yZXN0JywgJ3BlYWNlZnVsJywgJ3Bob3RvZ3JhcGh5J10sXG4gICAgaW5TdG9jazogdHJ1ZSxcbiAgICBmZWF0dXJlZDogZmFsc2UsXG4gICAgaXNEaWdpdGFsOiB0cnVlLFxuICAgIGZpbGVGb3JtYXQ6IFsnSlBHJ10sXG4gICAgZmlsZVNpemU6ICcxMi0xOCBNQicsXG4gICAgY3JlYXRlZEF0OiAnMjAyNC0wMS0wNFQwMDowMDowMFonLFxuICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDRUMDA6MDA6MDBaJyxcbiAgICB3YWxscGFwZXJEYXRhOiB7XG4gICAgICByZXNvbHV0aW9uczogW1xuICAgICAgICB7IHdpZHRoOiAzODQwLCBoZWlnaHQ6IDIxNjAsIGxhYmVsOiAnNEsnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvNGsnLCBmaWxlU2l6ZTogJzE4IE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAxOTIwLCBoZWlnaHQ6IDEwODAsIGxhYmVsOiAnMTA4MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTA4MHAnLCBmaWxlU2l6ZTogJzEyIE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAxMDgwLCBoZWlnaHQ6IDIzNDAsIGxhYmVsOiAnTW9iaWxlJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzL21vYmlsZScsIGZpbGVTaXplOiAnNiBNQicgfSxcbiAgICAgIF0sXG4gICAgICBhc3BlY3RSYXRpbzogJzE2OjknLFxuICAgICAgY29sb3JQYWxldHRlOiBbJyMyMjhCMjInLCAnIzhGQkM4RicsICcjRjVERUIzJywgJyNERUI4ODcnLCAnIzY1NDMyMSddLFxuICAgICAgc3R5bGU6IFdhbGxwYXBlclN0eWxlLk5BVFVSRSxcbiAgICAgIGRldmljZUNvbXBhdGliaWxpdHk6IFtEZXZpY2VUeXBlLkRFU0tUT1AsIERldmljZVR5cGUuTEFQVE9QLCBEZXZpY2VUeXBlLk1PQklMRSwgRGV2aWNlVHlwZS5UQUJMRVRdLFxuICAgICAgcHJldmlld1VybDogJy9hcGkvcGxhY2Vob2xkZXIvODAwLzYwMCcsXG4gICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgZG93bmxvYWRDb3VudDogNzU2LFxuICAgIH0sXG4gIH0sXG4gIHtcbiAgICBpZDogJzUnLFxuICAgIHRpdGxlOiAnR2VvbWV0cmljIFBhdHRlcm5zJyxcbiAgICBkZXNjcmlwdGlvbjogJ01vZGVybiBnZW9tZXRyaWMgcGF0dGVybnMgaW4gdmlicmFudCBjb2xvcnMnLFxuICAgIHByaWNlOiAzLjk5LFxuICAgIGNhdGVnb3J5OiBQcm9kdWN0Q2F0ZWdvcnkuV0FMTFBBUEVSUyxcbiAgICB0eXBlOiBQcm9kdWN0VHlwZS5ESUdJVEFMLFxuICAgIGltYWdlczogWycvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnXSxcbiAgICB0YWdzOiBbJ2dlb21ldHJpYycsICdwYXR0ZXJucycsICd2aWJyYW50JywgJ21vZGVybiddLFxuICAgIGluU3RvY2s6IHRydWUsXG4gICAgZmVhdHVyZWQ6IHRydWUsXG4gICAgaXNEaWdpdGFsOiB0cnVlLFxuICAgIGZpbGVGb3JtYXQ6IFsnSlBHJywgJ1BORyddLFxuICAgIGZpbGVTaXplOiAnMTUtMjIgTUInLFxuICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDVUMDA6MDA6MDBaJyxcbiAgICB1cGRhdGVkQXQ6ICcyMDI0LTAxLTA1VDAwOjAwOjAwWicsXG4gICAgd2FsbHBhcGVyRGF0YToge1xuICAgICAgcmVzb2x1dGlvbnM6IFtcbiAgICAgICAgeyB3aWR0aDogMzg0MCwgaGVpZ2h0OiAyMTYwLCBsYWJlbDogJzRLJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzRrJywgZmlsZVNpemU6ICcyMiBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMjU2MCwgaGVpZ2h0OiAxNDQwLCBsYWJlbDogJzE0NDBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzE0NDBwJywgZmlsZVNpemU6ICcxNiBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMTkyMCwgaGVpZ2h0OiAxMDgwLCBsYWJlbDogJzEwODBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzEwODBwJywgZmlsZVNpemU6ICcxMiBNQicgfSxcbiAgICAgICAgeyB3aWR0aDogMTA4MCwgaGVpZ2h0OiAyMzQwLCBsYWJlbDogJ01vYmlsZScsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy9tb2JpbGUnLCBmaWxlU2l6ZTogJzggTUInIH0sXG4gICAgICBdLFxuICAgICAgYXNwZWN0UmF0aW86ICcxNjo5JyxcbiAgICAgIGNvbG9yUGFsZXR0ZTogWycjRkY2QjZCJywgJyM0RUNEQzQnLCAnIzQ1QjdEMScsICcjOTZDRUI0JywgJyNGRkVBQTcnXSxcbiAgICAgIHN0eWxlOiBXYWxscGFwZXJTdHlsZS5HRU9NRVRSSUMsXG4gICAgICBkZXZpY2VDb21wYXRpYmlsaXR5OiBbRGV2aWNlVHlwZS5ERVNLVE9QLCBEZXZpY2VUeXBlLkxBUFRPUCwgRGV2aWNlVHlwZS5NT0JJTEUsIERldmljZVR5cGUuVEFCTEVUXSxcbiAgICAgIHByZXZpZXdVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzgwMC82MDAnLFxuICAgICAgd2F0ZXJtYXJrZWRQcmV2aWV3OiAnL2FwaS9wbGFjZWhvbGRlci80MDAvNjAwJyxcbiAgICAgIGRvd25sb2FkQ291bnQ6IDE0MjAsXG4gICAgfSxcbiAgfSxcbiAge1xuICAgIGlkOiAnNicsXG4gICAgdGl0bGU6ICdEYXJrIEFlc3RoZXRpYycsXG4gICAgZGVzY3JpcHRpb246ICdTbGVlayBkYXJrIHRoZW1lIHdhbGxwYXBlciB3aXRoIHN1YnRsZSB0ZXh0dXJlcycsXG4gICAgcHJpY2U6IDIuOTksXG4gICAgY2F0ZWdvcnk6IFByb2R1Y3RDYXRlZ29yeS5XQUxMUEFQRVJTLFxuICAgIHR5cGU6IFByb2R1Y3RUeXBlLkRJR0lUQUwsXG4gICAgaW1hZ2VzOiBbJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzYwMCddLFxuICAgIHRhZ3M6IFsnZGFyaycsICdhZXN0aGV0aWMnLCAnbWluaW1hbCcsICd0ZXh0dXJlJ10sXG4gICAgaW5TdG9jazogdHJ1ZSxcbiAgICBmZWF0dXJlZDogZmFsc2UsXG4gICAgaXNEaWdpdGFsOiB0cnVlLFxuICAgIGZpbGVGb3JtYXQ6IFsnSlBHJywgJ1BORyddLFxuICAgIGZpbGVTaXplOiAnOC0xNSBNQicsXG4gICAgY3JlYXRlZEF0OiAnMjAyNC0wMS0wNlQwMDowMDowMFonLFxuICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDZUMDA6MDA6MDBaJyxcbiAgICB3YWxscGFwZXJEYXRhOiB7XG4gICAgICByZXNvbHV0aW9uczogW1xuICAgICAgICB7IHdpZHRoOiAzODQwLCBoZWlnaHQ6IDIxNjAsIGxhYmVsOiAnNEsnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvNGsnLCBmaWxlU2l6ZTogJzE1IE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAyNTYwLCBoZWlnaHQ6IDE0NDAsIGxhYmVsOiAnMTQ0MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTQ0MHAnLCBmaWxlU2l6ZTogJzEyIE1CJyB9LFxuICAgICAgICB7IHdpZHRoOiAxOTIwLCBoZWlnaHQ6IDEwODAsIGxhYmVsOiAnMTA4MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTA4MHAnLCBmaWxlU2l6ZTogJzggTUInIH0sXG4gICAgICAgIHsgd2lkdGg6IDEwODAsIGhlaWdodDogMjM0MCwgbGFiZWw6ICdNb2JpbGUnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvbW9iaWxlJywgZmlsZVNpemU6ICc1IE1CJyB9LFxuICAgICAgXSxcbiAgICAgIGFzcGVjdFJhdGlvOiAnMTY6OScsXG4gICAgICBjb2xvclBhbGV0dGU6IFsnIzFBMUExQScsICcjMkQyRDJEJywgJyM0MDQwNDAnLCAnIzY2NjY2NicsICcjODA4MDgwJ10sXG4gICAgICBzdHlsZTogV2FsbHBhcGVyU3R5bGUuREFSSyxcbiAgICAgIGRldmljZUNvbXBhdGliaWxpdHk6IFtEZXZpY2VUeXBlLkRFU0tUT1AsIERldmljZVR5cGUuTEFQVE9QLCBEZXZpY2VUeXBlLk1PQklMRSwgRGV2aWNlVHlwZS5UQUJMRVRdLFxuICAgICAgcHJldmlld1VybDogJy9hcGkvcGxhY2Vob2xkZXIvODAwLzYwMCcsXG4gICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgZG93bmxvYWRDb3VudDogOTgwLFxuICAgIH0sXG4gIH1cbl07XG5cbi8vIEhlbHBlciBmdW5jdGlvbnMgZm9yIHdhbGxwYXBlciBtYW5hZ2VtZW50XG5leHBvcnQgY29uc3QgZ2V0V2FsbHBhcGVyQnlJZCA9IChpZDogc3RyaW5nKTogUHJvZHVjdCB8IHVuZGVmaW5lZCA9PiB7XG4gIHJldHVybiB3YWxscGFwZXJzRGF0YS5maW5kKHdhbGxwYXBlciA9PiB3YWxscGFwZXIuaWQgPT09IGlkKTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRGZWF0dXJlZFdhbGxwYXBlcnMgPSAoKTogUHJvZHVjdFtdID0+IHtcbiAgcmV0dXJuIHdhbGxwYXBlcnNEYXRhLmZpbHRlcih3YWxscGFwZXIgPT4gd2FsbHBhcGVyLmZlYXR1cmVkKTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRXYWxscGFwZXJzQnlDYXRlZ29yeSA9IChzdHlsZTogV2FsbHBhcGVyU3R5bGUpOiBQcm9kdWN0W10gPT4ge1xuICByZXR1cm4gd2FsbHBhcGVyc0RhdGEuZmlsdGVyKHdhbGxwYXBlciA9PiB3YWxscGFwZXIud2FsbHBhcGVyRGF0YT8uc3R5bGUgPT09IHN0eWxlKTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRUb3RhbERvd25sb2FkcyA9ICgpOiBudW1iZXIgPT4ge1xuICByZXR1cm4gd2FsbHBhcGVyc0RhdGEucmVkdWNlKCh0b3RhbCwgd2FsbHBhcGVyKSA9PiBcbiAgICB0b3RhbCArICh3YWxscGFwZXIud2FsbHBhcGVyRGF0YT8uZG93bmxvYWRDb3VudCB8fCAwKSwgMFxuICApO1xufTtcblxuLy8gQ2F0ZWdvcmllcyBmb3IgZmlsdGVyaW5nXG5leHBvcnQgY29uc3Qgd2FsbHBhcGVyQ2F0ZWdvcmllcyA9IFtcbiAgJ0FsbCcsXG4gICdBYnN0cmFjdCcsXG4gICdNaW5pbWFsaXN0JywgXG4gICdHYW1pbmcnLFxuICAnTmF0dXJlJyxcbiAgJ0dlb21ldHJpYycsXG4gICdEYXJrJ1xuXTtcblxuLy8gQ29udmVydCBQcm9kdWN0IHRvIGFkbWluIFdhbGxwYXBlciBmb3JtYXRcbmV4cG9ydCBjb25zdCBjb252ZXJ0VG9BZG1pbldhbGxwYXBlciA9IChwcm9kdWN0OiBQcm9kdWN0KSA9PiB7XG4gIHJldHVybiB7XG4gICAgaWQ6IHByb2R1Y3QuaWQsXG4gICAgdGl0bGU6IHByb2R1Y3QudGl0bGUsXG4gICAgZGVzY3JpcHRpb246IHByb2R1Y3QuZGVzY3JpcHRpb24sXG4gICAgY2F0ZWdvcnk6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YT8uc3R5bGUgfHwgJ0Fic3RyYWN0JyxcbiAgICB0YWdzOiBwcm9kdWN0LnRhZ3MsXG4gICAgcHJpY2U6IHByb2R1Y3QucHJpY2UsXG4gICAgb3JpZ2luYWxQcmljZTogcHJvZHVjdC5vcmlnaW5hbFByaWNlLFxuICAgIGZlYXR1cmVkOiBwcm9kdWN0LmZlYXR1cmVkLFxuICAgIGRvd25sb2FkczogcHJvZHVjdC53YWxscGFwZXJEYXRhPy5kb3dubG9hZENvdW50IHx8IDAsXG4gICAgaW1hZ2VVcmw6IHByb2R1Y3QuaW1hZ2VzWzBdLFxuICAgIHRodW1ibmFpbFVybDogcHJvZHVjdC53YWxscGFwZXJEYXRhPy53YXRlcm1hcmtlZFByZXZpZXcgfHwgcHJvZHVjdC5pbWFnZXNbMF0sXG4gICAgcmVzb2x1dGlvbnM6IHtcbiAgICAgIGRlc2t0b3A6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YT8ucmVzb2x1dGlvbnMuZmluZChyID0+IHIubGFiZWwgPT09ICcxMDgwcCcgfHwgci5sYWJlbCA9PT0gJzE0NDBwJykgPyB7XG4gICAgICAgIHdpZHRoOiBwcm9kdWN0LndhbGxwYXBlckRhdGEucmVzb2x1dGlvbnMuZmluZChyID0+IHIubGFiZWwgPT09ICcxMDgwcCcgfHwgci5sYWJlbCA9PT0gJzE0NDBwJykhLndpZHRoLFxuICAgICAgICBoZWlnaHQ6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5yZXNvbHV0aW9ucy5maW5kKHIgPT4gci5sYWJlbCA9PT0gJzEwODBwJyB8fCByLmxhYmVsID09PSAnMTQ0MHAnKSEuaGVpZ2h0LFxuICAgICAgICB1cmw6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5yZXNvbHV0aW9ucy5maW5kKHIgPT4gci5sYWJlbCA9PT0gJzEwODBwJyB8fCByLmxhYmVsID09PSAnMTQ0MHAnKSEuZG93bmxvYWRVcmwsXG4gICAgICB9IDogeyB3aWR0aDogMTkyMCwgaGVpZ2h0OiAxMDgwLCB1cmw6ICcvYXBpL3BsYWNlaG9sZGVyLzE5MjAvMTA4MCcgfSxcbiAgICAgIG1vYmlsZTogcHJvZHVjdC53YWxscGFwZXJEYXRhPy5yZXNvbHV0aW9ucy5maW5kKHIgPT4gci5sYWJlbCA9PT0gJ01vYmlsZScpID8ge1xuICAgICAgICB3aWR0aDogcHJvZHVjdC53YWxscGFwZXJEYXRhLnJlc29sdXRpb25zLmZpbmQociA9PiByLmxhYmVsID09PSAnTW9iaWxlJykhLndpZHRoLFxuICAgICAgICBoZWlnaHQ6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5yZXNvbHV0aW9ucy5maW5kKHIgPT4gci5sYWJlbCA9PT0gJ01vYmlsZScpIS5oZWlnaHQsXG4gICAgICAgIHVybDogcHJvZHVjdC53YWxscGFwZXJEYXRhLnJlc29sdXRpb25zLmZpbmQociA9PiByLmxhYmVsID09PSAnTW9iaWxlJykhLmRvd25sb2FkVXJsLFxuICAgICAgfSA6IHsgd2lkdGg6IDEwODAsIGhlaWdodDogMTkyMCwgdXJsOiAnL2FwaS9wbGFjZWhvbGRlci8xMDgwLzE5MjAnIH0sXG4gICAgICB0YWJsZXQ6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YT8ucmVzb2x1dGlvbnMuZmluZChyID0+IHIubGFiZWwgPT09ICdUYWJsZXQnKSA/IHtcbiAgICAgICAgd2lkdGg6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5yZXNvbHV0aW9ucy5maW5kKHIgPT4gci5sYWJlbCA9PT0gJ1RhYmxldCcpIS53aWR0aCxcbiAgICAgICAgaGVpZ2h0OiBwcm9kdWN0LndhbGxwYXBlckRhdGEucmVzb2x1dGlvbnMuZmluZChyID0+IHIubGFiZWwgPT09ICdUYWJsZXQnKSEuaGVpZ2h0LFxuICAgICAgICB1cmw6IHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5yZXNvbHV0aW9ucy5maW5kKHIgPT4gci5sYWJlbCA9PT0gJ1RhYmxldCcpIS5kb3dubG9hZFVybCxcbiAgICAgIH0gOiB7IHdpZHRoOiAxNTM2LCBoZWlnaHQ6IDIwNDgsIHVybDogJy9hcGkvcGxhY2Vob2xkZXIvMTUzNi8yMDQ4JyB9LFxuICAgIH0sXG4gICAgY3JlYXRlZEF0OiBwcm9kdWN0LmNyZWF0ZWRBdCxcbiAgICB1cGRhdGVkQXQ6IHByb2R1Y3QudXBkYXRlZEF0XG4gIH07XG59O1xuIl0sIm5hbWVzIjpbIlByb2R1Y3RDYXRlZ29yeSIsIlByb2R1Y3RUeXBlIiwiV2FsbHBhcGVyU3R5bGUiLCJEZXZpY2VUeXBlIiwid2FsbHBhcGVyc0RhdGEiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJwcmljZSIsIm9yaWdpbmFsUHJpY2UiLCJjYXRlZ29yeSIsIldBTExQQVBFUlMiLCJ0eXBlIiwiRElHSVRBTCIsImltYWdlcyIsInRhZ3MiLCJpblN0b2NrIiwiZmVhdHVyZWQiLCJpc0RpZ2l0YWwiLCJmaWxlRm9ybWF0IiwiZmlsZVNpemUiLCJjcmVhdGVkQXQiLCJ1cGRhdGVkQXQiLCJ3YWxscGFwZXJEYXRhIiwicmVzb2x1dGlvbnMiLCJ3aWR0aCIsImhlaWdodCIsImxhYmVsIiwiZG93bmxvYWRVcmwiLCJhc3BlY3RSYXRpbyIsImNvbG9yUGFsZXR0ZSIsInN0eWxlIiwiQUJTVFJBQ1QiLCJkZXZpY2VDb21wYXRpYmlsaXR5IiwiREVTS1RPUCIsIkxBUFRPUCIsIk1PQklMRSIsInByZXZpZXdVcmwiLCJ3YXRlcm1hcmtlZFByZXZpZXciLCJkb3dubG9hZENvdW50IiwiTUlOSU1BTElTVCIsIlVMVFJBV0lERSIsIkdBTUlORyIsIk5BVFVSRSIsIlRBQkxFVCIsIkdFT01FVFJJQyIsIkRBUksiLCJnZXRXYWxscGFwZXJCeUlkIiwiZmluZCIsIndhbGxwYXBlciIsImdldEZlYXR1cmVkV2FsbHBhcGVycyIsImZpbHRlciIsImdldFdhbGxwYXBlcnNCeUNhdGVnb3J5IiwiZ2V0VG90YWxEb3dubG9hZHMiLCJyZWR1Y2UiLCJ0b3RhbCIsIndhbGxwYXBlckNhdGVnb3JpZXMiLCJjb252ZXJ0VG9BZG1pbldhbGxwYXBlciIsInByb2R1Y3QiLCJkb3dubG9hZHMiLCJpbWFnZVVybCIsInRodW1ibmFpbFVybCIsImRlc2t0b3AiLCJyIiwidXJsIiwibW9iaWxlIiwidGFibGV0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/wallpapers.ts\n"));

/***/ })

});