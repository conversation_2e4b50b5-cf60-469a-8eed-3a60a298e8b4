{"name": "elishop-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "firebase": "^10.7.1", "react-icons": "^4.12.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4"}}