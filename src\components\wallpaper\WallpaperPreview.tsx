'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon,
  ArrowsPointingOutIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import { Product, DeviceType, Resolution } from '@/types/product';

interface WallpaperPreviewProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onDownload?: (resolution: Resolution) => void;
}

const WallpaperPreview: React.FC<WallpaperPreviewProps> = ({
  product,
  isOpen,
  onClose,
  onDownload,
}) => {
  const [selectedDevice, setSelectedDevice] = useState<DeviceType>(DeviceType.DESKTOP);
  const [isZoomed, setIsZoomed] = useState(false);
  const [selectedResolution, setSelectedResolution] = useState<Resolution | null>(
    product.wallpaperData?.resolutions[0] || null
  );

  if (!product.wallpaperData) return null;

  const { wallpaperData } = product;

  const deviceMockups = {
    [DeviceType.DESKTOP]: {
      icon: ComputerDesktopIcon,
      label: 'Desktop',
      aspectRatio: '16/9',
      width: 'w-80',
      height: 'h-45',
    },
    [DeviceType.LAPTOP]: {
      icon: ComputerDesktopIcon,
      label: 'Laptop',
      aspectRatio: '16/10',
      width: 'w-72',
      height: 'h-45',
    },
    [DeviceType.TABLET]: {
      icon: DeviceTabletIcon,
      label: 'Tablet',
      aspectRatio: '4/3',
      width: 'w-60',
      height: 'h-45',
    },
    [DeviceType.MOBILE]: {
      icon: DevicePhoneMobileIcon,
      label: 'Mobile',
      aspectRatio: '9/19.5',
      width: 'w-32',
      height: 'h-64',
    },
  };

  const availableDevices = wallpaperData.deviceCompatibility.filter(
    device => device in deviceMockups
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{product.title}</h2>
                <p className="text-gray-600 mt-1">
                  {wallpaperData.aspectRatio} • {wallpaperData.style}
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
              >
                <XMarkIcon className="w-6 h-6 text-gray-600" />
              </button>
            </div>

            <div className="flex flex-col lg:flex-row">
              {/* Preview Area */}
              <div className="flex-1 p-6">
                {/* Device Selector */}
                <div className="flex items-center space-x-4 mb-6">
                  <span className="text-sm font-medium text-gray-700">Preview on:</span>
                  <div className="flex space-x-2">
                    {availableDevices.map((device) => {
                      const mockup = deviceMockups[device];
                      const Icon = mockup.icon;
                      return (
                        <button
                          key={device}
                          onClick={() => setSelectedDevice(device)}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                            selectedDevice === device
                              ? 'bg-purple-yellow-gradient text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span>{mockup.label}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Device Mockup */}
                <div className="flex items-center justify-center min-h-[400px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
                  <div className="relative">
                    {/* Device Frame */}
                    <div
                      className={`relative ${deviceMockups[selectedDevice].width} ${deviceMockups[selectedDevice].height} bg-gray-800 rounded-lg p-2 shadow-2xl`}
                    >
                      {/* Screen */}
                      <div
                        className="w-full h-full rounded-md overflow-hidden cursor-pointer relative group"
                        onClick={() => setIsZoomed(true)}
                      >
                        <img
                          src={wallpaperData.watermarkedPreview}
                          alt={product.title}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                          <MagnifyingGlassIcon className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                        </div>
                      </div>
                    </div>

                    {/* Device Label */}
                    <div className="text-center mt-4">
                      <p className="text-sm text-gray-600">
                        {deviceMockups[selectedDevice].label} Preview
                      </p>
                    </div>
                  </div>
                </div>

                {/* Color Palette */}
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Color Palette</h3>
                  <div className="flex space-x-2">
                    {wallpaperData.colorPalette.map((color, index) => (
                      <div
                        key={index}
                        className="w-8 h-8 rounded-full border-2 border-white shadow-md"
                        style={{ backgroundColor: color }}
                        title={color}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="w-full lg:w-80 border-l border-gray-200 p-6">
                {/* Resolution Options */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Resolutions</h3>
                  <div className="space-y-3">
                    {wallpaperData.resolutions.map((resolution, index) => (
                      <div
                        key={index}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors duration-200 ${
                          selectedResolution?.label === resolution.label
                            ? 'border-primary-500 bg-primary-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedResolution(resolution)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{resolution.label}</p>
                            <p className="text-sm text-gray-600">
                              {resolution.width} × {resolution.height}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-600">{resolution.fileSize}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Product Info */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Details</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Aspect Ratio:</span>
                      <span className="font-medium">{wallpaperData.aspectRatio}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Style:</span>
                      <span className="font-medium capitalize">{wallpaperData.style}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Format:</span>
                      <span className="font-medium">{product.fileFormat?.join(', ')}</span>
                    </div>
                    {wallpaperData.downloadCount && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Downloads:</span>
                        <span className="font-medium">{wallpaperData.downloadCount.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={() => selectedResolution && onDownload?.(selectedResolution)}
                    disabled={!selectedResolution}
                    className="w-full bg-purple-yellow-gradient text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                  >
                    <ArrowDownTrayIcon className="w-5 h-5" />
                    <span>Download {selectedResolution?.label}</span>
                  </button>
                  
                  <button
                    onClick={() => setIsZoomed(true)}
                    className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center space-x-2"
                  >
                    <EyeIcon className="w-5 h-5" />
                    <span>Full Size Preview</span>
                  </button>
                </div>

                {/* Price */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold text-gray-900">
                      ${product.price.toFixed(2)}
                    </span>
                    {product.originalPrice && product.originalPrice > product.price && (
                      <span className="text-sm text-gray-500 line-through">
                        ${product.originalPrice.toFixed(2)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Full Size Modal */}
          <AnimatePresence>
            {isZoomed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-black bg-opacity-95 flex items-center justify-center p-4 z-10"
                onClick={() => setIsZoomed(false)}
              >
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  exit={{ scale: 0.8 }}
                  className="relative max-w-full max-h-full"
                >
                  <img
                    src={wallpaperData.previewUrl}
                    alt={product.title}
                    className="max-w-full max-h-full object-contain rounded-lg"
                  />
                  <button
                    onClick={() => setIsZoomed(false)}
                    className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors duration-200"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </button>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WallpaperPreview;
