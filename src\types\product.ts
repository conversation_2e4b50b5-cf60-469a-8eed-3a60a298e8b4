export interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: ProductCategory;
  type: ProductType;
  images: string[];
  tags: string[];
  inStock: boolean;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Digital product specific fields
  isDigital: boolean;
  downloadUrl?: string;
  fileSize?: string;
  fileFormat?: string[];
  
  // Wallpaper specific fields
  wallpaperData?: WallpaperData;
  
  // Physical product specific fields
  physicalData?: PhysicalData;
}

export interface WallpaperData {
  resolutions: Resolution[];
  aspectRatio: string;
  colorPalette: string[];
  style: WallpaperStyle;
  deviceCompatibility: DeviceType[];
  previewUrl: string;
  watermarkedPreview: string;
  downloadCount?: number;
}

export interface Resolution {
  width: number;
  height: number;
  label: string; // e.g., "4K", "1080p", "Mobile"
  downloadUrl: string;
  fileSize: string;
}

export interface PhysicalData {
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  material?: string;
  shippingInfo?: string;
}

export enum ProductCategory {
  // Digital Categories
  WALLPAPERS = 'wallpapers',
  DIGITAL_ART = 'digital-art',
  TEMPLATES = 'templates',
  FONTS = 'fonts',
  
  // Physical Categories
  CLOTHING = 'clothing',
  ACCESSORIES = 'accessories',
  HOME_DECOR = 'home-decor',
  STATIONERY = 'stationery',
  
  // Services
  CONSULTING = 'consulting',
  DESIGN_SERVICES = 'design-services',
}

export enum ProductType {
  DIGITAL = 'digital',
  PHYSICAL = 'physical',
  SERVICE = 'service',
}

export enum WallpaperStyle {
  ABSTRACT = 'abstract',
  NATURE = 'nature',
  MINIMALIST = 'minimalist',
  GAMING = 'gaming',
  ANIME = 'anime',
  PHOTOGRAPHY = 'photography',
  GRADIENT = 'gradient',
  PATTERN = 'pattern',
  DARK = 'dark',
  LIGHT = 'light',
  NEON = 'neon',
  VINTAGE = 'vintage',
  MODERN = 'modern',
  ARTISTIC = 'artistic',
}

export enum DeviceType {
  DESKTOP = 'desktop',
  LAPTOP = 'laptop',
  TABLET = 'tablet',
  MOBILE = 'mobile',
  ULTRAWIDE = 'ultrawide',
  DUAL_MONITOR = 'dual-monitor',
}

export interface CartItem {
  product: Product;
  quantity: number;
  selectedResolution?: Resolution; // For wallpapers
  customOptions?: Record<string, any>;
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingAddress?: Address;
  billingAddress?: Address;
  downloadLinks?: DownloadLink[];
  createdAt: string;
  updatedAt: string;
}

export interface DownloadLink {
  productId: string;
  url: string;
  expiresAt: string;
  downloadCount: number;
  maxDownloads: number;
}

export interface Address {
  name: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

// Utility functions
export const getProductCategoryLabel = (category: ProductCategory): string => {
  const labels: Record<ProductCategory, string> = {
    [ProductCategory.WALLPAPERS]: 'Wallpapers',
    [ProductCategory.DIGITAL_ART]: 'Digital Art',
    [ProductCategory.TEMPLATES]: 'Templates',
    [ProductCategory.FONTS]: 'Fonts',
    [ProductCategory.CLOTHING]: 'Clothing',
    [ProductCategory.ACCESSORIES]: 'Accessories',
    [ProductCategory.HOME_DECOR]: 'Home Decor',
    [ProductCategory.STATIONERY]: 'Stationery',
    [ProductCategory.CONSULTING]: 'Consulting',
    [ProductCategory.DESIGN_SERVICES]: 'Design Services',
  };
  return labels[category];
};

export const getWallpaperStyleLabel = (style: WallpaperStyle): string => {
  const labels: Record<WallpaperStyle, string> = {
    [WallpaperStyle.ABSTRACT]: 'Abstract',
    [WallpaperStyle.NATURE]: 'Nature',
    [WallpaperStyle.MINIMALIST]: 'Minimalist',
    [WallpaperStyle.GAMING]: 'Gaming',
    [WallpaperStyle.ANIME]: 'Anime',
    [WallpaperStyle.PHOTOGRAPHY]: 'Photography',
    [WallpaperStyle.GRADIENT]: 'Gradient',
    [WallpaperStyle.PATTERN]: 'Pattern',
    [WallpaperStyle.DARK]: 'Dark',
    [WallpaperStyle.LIGHT]: 'Light',
    [WallpaperStyle.NEON]: 'Neon',
    [WallpaperStyle.VINTAGE]: 'Vintage',
    [WallpaperStyle.MODERN]: 'Modern',
    [WallpaperStyle.ARTISTIC]: 'Artistic',
  };
  return labels[style];
};

export const getDeviceTypeLabel = (device: DeviceType): string => {
  const labels: Record<DeviceType, string> = {
    [DeviceType.DESKTOP]: 'Desktop',
    [DeviceType.LAPTOP]: 'Laptop',
    [DeviceType.TABLET]: 'Tablet',
    [DeviceType.MOBILE]: 'Mobile',
    [DeviceType.ULTRAWIDE]: 'Ultrawide',
    [DeviceType.DUAL_MONITOR]: 'Dual Monitor',
  };
  return labels[device];
};
