import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const data = await request.formData();
    const files = data.getAll('files') as File[];
    const wallpaperData = JSON.parse(data.get('wallpaperData') as string);

    if (!files || files.length === 0) {
      return NextResponse.json({ success: false, error: 'No files uploaded' }, { status: 400 });
    }

    // Create wallpapers directory if it doesn't exist
    const wallpapersDir = path.join(process.cwd(), 'public', 'wallpapers');
    try {
      await mkdir(wallpapersDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    const uploadedFiles: { [key: string]: string } = {};
    const timestamp = Date.now();

    // Process each uploaded file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        return NextResponse.json({ 
          success: false, 
          error: `File ${file.name} is not an image` 
        }, { status: 400 });
      }

      // Validate file size (10MB max for wallpapers)
      if (file.size > 10 * 1024 * 1024) {
        return NextResponse.json({ 
          success: false, 
          error: `File ${file.name} is too large (max 10MB)` 
        }, { status: 400 });
      }

      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);

      // Generate filename based on resolution or use original name
      const extension = path.extname(file.name);
      const baseName = wallpaperData.title.toLowerCase().replace(/[^a-z0-9]/g, '-');
      
      // Determine resolution type based on file name or dimensions
      let resolutionType = 'desktop';
      if (file.name.includes('mobile') || file.name.includes('phone')) {
        resolutionType = 'mobile';
      } else if (file.name.includes('tablet') || file.name.includes('ipad')) {
        resolutionType = 'tablet';
      }

      const filename = `${baseName}-${resolutionType}-${timestamp}${extension}`;
      const filepath = path.join(wallpapersDir, filename);

      // Write file
      await writeFile(filepath, buffer);

      // Store the public URL
      uploadedFiles[resolutionType] = `/wallpapers/${filename}`;
    }

    // Create wallpaper object with uploaded file URLs
    const wallpaper = {
      id: `wallpaper-${timestamp}`,
      title: wallpaperData.title,
      description: wallpaperData.description,
      category: wallpaperData.category,
      tags: wallpaperData.tags || [],
      price: parseFloat(wallpaperData.price) || 0,
      originalPrice: wallpaperData.originalPrice ? parseFloat(wallpaperData.originalPrice) : undefined,
      featured: wallpaperData.featured || false,
      downloads: 0,
      imageUrl: uploadedFiles.desktop || uploadedFiles.mobile || uploadedFiles.tablet,
      thumbnailUrl: uploadedFiles.desktop || uploadedFiles.mobile || uploadedFiles.tablet, // In real app, generate thumbnail
      resolutions: {
        desktop: uploadedFiles.desktop ? {
          width: 1920,
          height: 1080,
          url: uploadedFiles.desktop
        } : undefined,
        mobile: uploadedFiles.mobile ? {
          width: 1080,
          height: 1920,
          url: uploadedFiles.mobile
        } : undefined,
        tablet: uploadedFiles.tablet ? {
          width: 1536,
          height: 2048,
          url: uploadedFiles.tablet
        } : undefined
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // In a real app, you would save this to your database (Firebase, etc.)
    // For now, we'll just return the wallpaper object

    return NextResponse.json({ 
      success: true, 
      wallpaper,
      message: 'Wallpaper uploaded successfully',
      uploadedFiles
    });

  } catch (error) {
    console.error('Wallpaper upload error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to upload wallpaper' 
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Wallpaper upload endpoint - use POST to upload wallpapers',
    supportedFormats: ['JPG', 'PNG', 'WEBP'],
    maxFileSize: '10MB',
    requiredResolutions: ['desktop (1920x1080)', 'mobile (1080x1920)', 'tablet (1536x2048)']
  });
}
