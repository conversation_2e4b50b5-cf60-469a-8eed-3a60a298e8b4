'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  PaintBrushIcon, 
  ComputerDesktopIcon, 
  CameraIcon, 
  VideoCameraIcon,
  SparklesIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const About: React.FC = () => {
  const skills = [
    {
      icon: PaintBrushIcon,
      title: 'Graphic Design',
      description: 'Creating visually stunning designs that communicate your brand message effectively',
      tools: ['Adobe Illustrator', 'Photoshop', 'InDesign', 'Figma'],
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: ComputerDesktopIcon,
      title: 'Web Design',
      description: 'Building responsive, user-friendly websites that deliver exceptional user experiences',
      tools: ['React', 'Next.js', 'Tailwind CSS', 'WordPress'],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: CameraIcon,
      title: 'Photography',
      description: 'Capturing compelling images that tell stories and showcase products beautifully',
      tools: ['Canon EOS', 'Lightroom', 'Photoshop', 'Studio Lighting'],
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: VideoCameraIcon,
      title: 'Video Editing',
      description: 'Creating engaging video content with professional editing and motion graphics',
      tools: ['After Effects', 'Premiere Pro', 'DaVinci Resolve', 'Final Cut Pro'],
      color: 'from-orange-500 to-red-500'
    }
  ];

  const achievements = [
    'Over 50+ successful projects completed',
    '5+ years of creative industry experience',
    'Specialized in brand identity and digital marketing',
    'Expert in both print and digital media',
    'Collaborative approach with clients',
    'Fast turnaround times without compromising quality'
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-primary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="text-center mb-16"
        >
          <motion.div variants={itemVariants} className="inline-flex items-center space-x-2 mb-4">
            <SparklesIcon className="w-6 h-6 text-primary-600" />
            <span className="text-primary-600 font-semibold text-sm uppercase tracking-wider">About Me</span>
          </motion.div>
          
          <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Hi, I'm <span
              className="inline-block"
              style={{
                background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >Elias Mwangi</span>
          </motion.h2>
          
          <motion.p variants={itemVariants} className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            I am a creative professional specializing in graphic design, web design, photography, and video editing. 
            I bring ideas to life through visually appealing and functional designs that capture attention and tell compelling stories.
          </motion.p>
        </motion.div>

        {/* Skills Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {skills.map((skill, index) => (
            <motion.div
              key={skill.title}
              variants={itemVariants}
              whileHover={{ y: -10, scale: 1.02 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${skill.color} flex items-center justify-center mb-6`}>
                <skill.icon className="w-8 h-8 text-white" />
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 mb-3">{skill.title}</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">{skill.description}</p>
              
              <div className="flex flex-wrap gap-2">
                {skill.tools.map((tool, toolIndex) => (
                  <span
                    key={toolIndex}
                    className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                  >
                    {tool}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Achievements Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div variants={itemVariants}>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Why Choose Me?
              </h3>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                With a passion for creativity and attention to detail, I deliver high-quality work that exceeds expectations. 
                My diverse skill set allows me to handle complete projects from concept to completion.
              </p>
              
              <div className="space-y-4">
                {achievements.map((achievement, index) => (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="flex items-center space-x-3"
                  >
                    <CheckCircleIcon className="w-6 h-6 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{achievement}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="relative">
              <div className="relative z-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl p-8 text-white">
                <h4 className="text-2xl font-bold mb-4">Let's Work Together</h4>
                <p className="text-primary-100 mb-6">
                  Ready to bring your creative vision to life? Let's discuss your project and create something amazing together.
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200"
                >
                  Get In Touch
                </motion.button>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-400 rounded-full opacity-20"></div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
