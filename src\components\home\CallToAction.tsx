'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ArrowRightIcon, 
  EnvelopeIcon, 
  PhoneIcon, 
  CalendarDaysIcon,
  SparklesIcon 
} from '@heroicons/react/24/outline';

const CallToAction: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 via-white to-secondary-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          variants={floatingVariants}
          animate="animate"
          className="absolute top-20 left-10 w-20 h-20 bg-primary-200 rounded-full opacity-20 blur-xl"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 1 }}
          className="absolute bottom-20 right-10 w-32 h-32 bg-secondary-200 rounded-full opacity-20 blur-xl"
        />
        <motion.div
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 2 }}
          className="absolute top-1/2 left-1/4 w-16 h-16 bg-primary-300 rounded-full opacity-20 blur-xl"
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center"
        >
          {/* Main CTA Section */}
          <motion.div variants={itemVariants} className="mb-16">
            <div className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm border border-primary-200 rounded-full px-4 py-2 text-sm font-medium text-primary-700 mb-6">
              <SparklesIcon className="w-4 h-4" />
              <span>Ready to Get Started?</span>
            </div>

            <h2 className="text-4xl sm:text-6xl font-bold text-gray-900 mb-6">
              Let's Create Something
              <span className="block bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Amazing Together
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-10">
              Whether you need a stunning portfolio, a powerful e-commerce solution, 
              or custom digital products, I'm here to bring your vision to life.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/contact"
                className="group inline-flex items-center space-x-2 bg-purple-yellow-gradient text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-2xl hover:shadow-primary-500/25 transform hover:scale-105 transition-all duration-300"
              >
                <EnvelopeIcon className="w-6 h-6" />
                <span>Start a Project</span>
                <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
              
              <Link
                href="/portfolio"
                className="group inline-flex items-center space-x-2 bg-white text-primary-600 border-2 border-primary-200 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-primary-50 hover:border-primary-300 transform hover:scale-105 transition-all duration-300"
              >
                <span>View My Work</span>
                <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </div>
          </motion.div>

          {/* Contact Options */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {[
              {
                icon: EnvelopeIcon,
                title: 'Email Me',
                description: 'Get a response within 24 hours',
                action: '<EMAIL>',
                href: 'mailto:<EMAIL>',
                color: 'from-blue-500 to-cyan-500',
              },
              {
                icon: PhoneIcon,
                title: 'Call Me',
                description: 'Let\'s discuss your project',
                action: '+****************',
                href: 'tel:+15551234567',
                color: 'from-green-500 to-emerald-500',
              },
              {
                icon: CalendarDaysIcon,
                title: 'Schedule a Call',
                description: 'Book a free consultation',
                action: 'Book Now',
                href: '/contact',
                color: 'from-purple-500 to-pink-500',
              },
            ].map((contact, index) => (
              <motion.a
                key={index}
                href={contact.href}
                whileHover={{ y: -5, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="group p-6 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-primary-200"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br ${contact.color} rounded-xl mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  <contact.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                  {contact.title}
                </h3>
                <p className="text-gray-600 mb-3">{contact.description}</p>
                <div className="text-primary-600 font-medium group-hover:text-primary-700 transition-colors duration-200">
                  {contact.action}
                </div>
              </motion.a>
            ))}
          </motion.div>

          {/* Newsletter Signup */}
          <motion.div variants={itemVariants} className="mt-16">
            <div className="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-white max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold mb-4">Stay in the Loop</h3>
              <p className="text-lg opacity-90 mb-6">
                Get updates on new projects, products, and exclusive offers.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-1 px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200"
                />
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-3 bg-white text-primary-600 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200 whitespace-nowrap"
                >
                  Subscribe
                </motion.button>
              </div>
              <p className="text-sm opacity-70 mt-3">
                No spam, unsubscribe at any time.
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default CallToAction;
