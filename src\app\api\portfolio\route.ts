import { NextRequest, NextResponse } from 'next/server';
import { getPortfolioProjects, addPortfolioProject } from '@/lib/firestore';

export async function GET() {
  try {
    const projects = await getPortfolioProjects();
    return NextResponse.json(projects);
  } catch (error) {
    console.error('Error fetching portfolio projects:', error);
    return NextResponse.json(
      { error: 'Failed to fetch portfolio projects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const projectId = await addPortfolioProject(body);
    return NextResponse.json({ id: projectId }, { status: 201 });
  } catch (error) {
    console.error('Error creating portfolio project:', error);
    return NextResponse.json(
      { error: 'Failed to create portfolio project' },
      { status: 500 }
    );
  }
}
