"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_home_HeroTest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_ServicesPreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_FeaturedPortfolio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_FeaturedProducts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_Skills__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_Testimonials__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_CallToAction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-16 lg:pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.5\n                },\n                className: \"space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_HeroTest__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_About__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_ServicesPreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Skills__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_FeaturedPortfolio__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_FeaturedProducts__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Testimonials__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_CallToAction__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.button, {\n                initial: {\n                    opacity: 0,\n                    scale: 0\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                whileHover: {\n                    scale: 1.1\n                },\n                whileTap: {\n                    scale: 0.9\n                },\n                onClick: ()=>window.scrollTo({\n                        top: 0,\n                        behavior: \"smooth\"\n                    }),\n                className: \"fixed bottom-8 right-8 w-12 h-12 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40\",\n                style: {\n                    background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-6 h-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ServicesContext */ \"(app-pages-browser)/./src/contexts/ServicesContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Services are now managed through ServicesContext\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6\n        }\n    }\n};\nconst ServicesPreview = ()=>{\n    _s();\n    const { activeServices, loading } = (0,_contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__.useServices)();\n    const getIconComponent = (iconName)=>{\n        switch(iconName){\n            case \"PaintBrushIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"ComputerDesktopIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"CameraIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"VideoCameraIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        }\n    };\n    const openWhatsApp = ()=>{\n        const message = encodeURIComponent(\"Hi Elias! \\uD83D\\uDC4B\\n\\nI'm interested in your creative services. Please let me know about pricing and availability.\\n\\nThank you!\");\n        const whatsappUrl = \"https://wa.me/254703973225?text=\".concat(message);\n        window.open(whatsappUrl, \"_blank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading services...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Creative \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        backgroundClip: \"text\"\n                                    },\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 22\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                            children: \"Professional creative services to bring your ideas to life. From design to development, photography to video editing.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                            onClick: openWhatsApp,\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Get Quote on WhatsApp\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    children: activeServices.slice(0, 4).map((service)=>{\n                        const IconComponent = getIconComponent(service.icon);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            variants: itemVariants,\n                            className: \"\".concat(service.bgColor, \" \").concat(service.borderColor, \" border-2 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r \".concat(service.color, \" p-3 rounded-lg mb-4 w-fit\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                    children: service.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm leading-relaxed\",\n                                    children: service.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm font-medium text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learn more\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, service.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/services\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"bg-white border-2 border-gray-300 text-gray-700 hover:border-purple-500 hover:text-purple-600 px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"View All Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                onClick: openWhatsApp,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Contact via WhatsApp\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServicesPreview, \"KOeUnzKrCvIJ8uFseZamQM7a3qY=\", false, function() {\n    return [\n        _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__.useServices\n    ];\n});\n_c = ServicesPreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesPreview);\nvar _c;\n$RefreshReg$(_c, \"ServicesPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ServicesContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/ServicesContext.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesProvider: function() { return /* binding */ ServicesProvider; },\n/* harmony export */   useServices: function() { return /* binding */ useServices; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ServicesProvider,useServices,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ServicesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst defaultServices = [\n    {\n        id: \"1\",\n        title: \"Graphic Design\",\n        description: \"Creating visually stunning designs that communicate your brand message effectively\",\n        features: [\n            \"Logo Design & Branding\",\n            \"Business Cards & Stationery\",\n            \"Brochures & Flyers\",\n            \"Social Media Graphics\",\n            \"Print Design\",\n            \"Brand Identity Development\"\n        ],\n        tools: [\n            \"Adobe Illustrator\",\n            \"Photoshop\",\n            \"InDesign\",\n            \"Figma\"\n        ],\n        icon: \"PaintBrushIcon\",\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"2\",\n        title: \"Web Design\",\n        description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n        features: [\n            \"Responsive Web Design\",\n            \"UI/UX Design\",\n            \"E-commerce Websites\",\n            \"Landing Pages\",\n            \"Web Applications\",\n            \"Website Maintenance\"\n        ],\n        tools: [\n            \"React\",\n            \"Next.js\",\n            \"Tailwind CSS\",\n            \"WordPress\"\n        ],\n        icon: \"ComputerDesktopIcon\",\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"3\",\n        title: \"Photography\",\n        description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n        features: [\n            \"Product Photography\",\n            \"Portrait Photography\",\n            \"Event Photography\",\n            \"Commercial Photography\",\n            \"Photo Editing & Retouching\",\n            \"Studio & Location Shoots\"\n        ],\n        tools: [\n            \"Canon EOS\",\n            \"Lightroom\",\n            \"Photoshop\",\n            \"Studio Lighting\"\n        ],\n        icon: \"CameraIcon\",\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"4\",\n        title: \"Video Editing\",\n        description: \"Creating engaging video content with professional editing and motion graphics\",\n        features: [\n            \"Video Production\",\n            \"Motion Graphics\",\n            \"Color Correction\",\n            \"Audio Enhancement\",\n            \"Social Media Videos\",\n            \"Commercial Videos\"\n        ],\n        tools: [\n            \"After Effects\",\n            \"Premiere Pro\",\n            \"DaVinci Resolve\",\n            \"Final Cut Pro\"\n        ],\n        icon: \"VideoCameraIcon\",\n        color: \"from-red-500 to-red-600\",\n        bgColor: \"bg-red-50\",\n        borderColor: \"border-red-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\nconst ServicesProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize services from localStorage or use defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadServices = ()=>{\n            try {\n                const savedServices = localStorage.getItem(\"elishop_services\");\n                if (savedServices) {\n                    const parsedServices = JSON.parse(savedServices);\n                    setServices(parsedServices);\n                } else {\n                    setServices(defaultServices);\n                    localStorage.setItem(\"elishop_services\", JSON.stringify(defaultServices));\n                }\n            } catch (error) {\n                console.error(\"Error loading services:\", error);\n                setServices(defaultServices);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadServices();\n    }, []);\n    // Save services to localStorage whenever services change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && services.length > 0) {\n            localStorage.setItem(\"elishop_services\", JSON.stringify(services));\n        }\n    }, [\n        services,\n        loading\n    ]);\n    const activeServices = services.filter((service)=>service.isActive);\n    const addService = (serviceData)=>{\n        const now = new Date().toISOString();\n        const newService = {\n            ...serviceData,\n            id: Date.now().toString(),\n            createdAt: now,\n            updatedAt: now\n        };\n        setServices((prev)=>[\n                ...prev,\n                newService\n            ]);\n    };\n    const updateService = (id, serviceData)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    ...serviceData,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const deleteService = (id)=>{\n        setServices((prev)=>prev.filter((service)=>service.id !== id));\n    };\n    const toggleServiceActive = (id)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    isActive: !service.isActive,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const getService = (id)=>{\n        return services.find((service)=>service.id === id);\n    };\n    const value = {\n        services,\n        activeServices,\n        addService,\n        updateService,\n        deleteService,\n        toggleServiceActive,\n        getService,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\ServicesContext.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServicesProvider, \"b/p1Mw8EtDCdIBIHijS5szgIZQ4=\");\n_c = ServicesProvider;\nconst useServices = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServicesContext);\n    if (context === undefined) {\n        throw new Error(\"useServices must be used within a ServicesProvider\");\n    }\n    return context;\n};\n_s1(useServices, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesContext);\nvar _c;\n$RefreshReg$(_c, \"ServicesProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ServicesContext.tsx\n"));

/***/ })

});