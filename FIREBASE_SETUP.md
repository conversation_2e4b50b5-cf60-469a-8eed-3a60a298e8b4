# Firebase Setup Guide for EliShop

## ✅ Configuration Complete
Your `.env.local` file has been configured with your Firebase project credentials.

## 🔧 Required Firebase Services Setup

To make EliShop work properly, you need to enable these services in your Firebase console:

### 1. Authentication
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: `eliportifolio`
3. Navigate to **Authentication** → **Sign-in method**
4. Enable these providers:
   - ✅ **Email/Password**
   - ✅ **Google** (recommended)

### 2. Firestore Database
1. Navigate to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (for development)
4. Select your preferred location
5. The app will automatically create these collections:
   - `products` - Store shop products
   - `orders` - Store customer orders
   - `portfolio` - Store portfolio projects
   - `users` - Store user profiles

### 3. Storage (Optional)
1. Navigate to **Storage**
2. Click **Get started**
3. Choose **Start in test mode**
4. This will be used for uploading product images and portfolio assets

### 4. Security Rules (Important!)
After testing, update your Firestore rules for production:

```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Products are readable by all, writable by admin only
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
    
    // Orders are readable/writable by owner and admin
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         request.auth.token.email == '<EMAIL>');
    }
    
    // Portfolio is readable by all, writable by admin only
    match /portfolio/{projectId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.email == '<EMAIL>';
    }
  }
}
```

## 🔐 Admin Access Setup

### Change Admin Email
1. Open `.env.local`
2. Change this line to your email:
   ```
   NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>
   ```
3. Save the file
4. Restart your development server

### Access Admin Panel
1. Sign up/Sign in with your admin email
2. Navigate to `/admin` 
3. You'll have full access to:
   - Dashboard overview
   - Product management
   - Order management
   - Portfolio management
   - User management
   - Settings

## 🚀 Running the Application

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Start Development Server:**
   ```bash
   npm run dev
   ```

3. **Open in Browser:**
   - Main site: http://localhost:3000
   - Admin panel: http://localhost:3000/admin

## 📝 Next Steps

1. ✅ Firebase services enabled
2. ✅ Admin email configured
3. ✅ Development server running
4. 🔄 Test authentication (sign up/sign in)
5. 🔄 Test admin panel access
6. 🔄 Add some sample products
7. 🔄 Test shop functionality
8. 🔄 Add portfolio projects

## 🆘 Troubleshooting

### Common Issues:

**"Firebase not initialized" error:**
- Make sure `.env.local` is in the root directory
- Restart your development server after changing `.env.local`

**"Permission denied" in Firestore:**
- Check that Authentication is enabled
- Verify Firestore rules allow your operations
- Make sure you're signed in

**Admin panel shows "Access Denied":**
- Verify `NEXT_PUBLIC_ADMIN_EMAIL` matches your signed-in email
- Check that you're signed in with the correct account

**Build errors:**
- Run `npm install` to ensure all dependencies are installed
- Check that all environment variables are set correctly

## 📞 Support

If you encounter any issues:
1. Check the browser console for error messages
2. Verify Firebase console shows your project is active
3. Ensure all required services are enabled
4. Check that environment variables are correctly formatted

Your EliShop application is now ready to run! 🎉
