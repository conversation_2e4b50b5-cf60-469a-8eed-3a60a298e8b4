# 🚀 Quick Start Guide - EliShop Portfolio

## Step 1: Install Node.js

1. **Download Node.js**:
   - Go to https://nodejs.org/
   - Download the LTS (Long Term Support) version
   - Install it following the setup wizard

2. **Verify Installation**:
   Open Command Prompt (Windows) or Terminal (Mac/Linux) and type:
   ```bash
   node --version
   npm --version
   ```
   You should see version numbers if installed correctly.

## Step 2: Install Project Dependencies

1. **Open Command Prompt/Terminal** in your project folder (`elishop`)
   
2. **Install all dependencies**:
   ```bash
   npm install
   ```
   
   This will install:
   - Next.js (React framework)
   - Framer Motion (animations)
   - Tailwind CSS (styling)
   - TypeScript
   - All other required packages

## Step 3: Start the Development Server

1. **Run the development server**:
   ```bash
   npm run dev
   ```

2. **Wait for the server to start**:
   You should see something like:
   ```
   ▲ Next.js 14.0.4
   - Local:        http://localhost:3000
   - Ready in 2.3s
   ```

3. **Open your browser**:
   - Go to http://localhost:3000
   - Your portfolio website should load!

## 🔧 Troubleshooting

### If Node.js installation fails:
- Make sure you have administrator privileges
- Restart your computer after installation
- Try downloading from the official website again

### If `npm install` fails:
```bash
# Clear npm cache
npm cache clean --force

# Try installing again
npm install
```

### If port 3000 is busy:
```bash
# Use a different port
npm run dev -- -p 3001
```
Then go to http://localhost:3001

### If you see TypeScript errors:
- The project is set up with TypeScript
- Errors will show in the terminal and browser
- Most errors will auto-fix as you save files

## 🎯 What You'll See

Once running, your website will have:
- ✅ Purple & Yellow modern design
- ✅ Smooth animations and transitions
- ✅ Responsive mobile-friendly layout
- ✅ Portfolio showcase section
- ✅ Shopping cart functionality
- ✅ Contact form
- ✅ Beautiful hover effects

## 📝 Next Steps

1. **Customize Content**:
   - Edit portfolio items
   - Add your products
   - Update contact information

2. **Modify Design**:
   - Change colors in `tailwind.config.ts`
   - Update animations
   - Add your own images

3. **Deploy**:
   - Build: `npm run build`
   - Deploy to Vercel, Netlify, etc.

## 🆘 Need Help?

If you're still having issues:
1. Make sure you're in the correct folder (`elishop`)
2. Check that all files are present
3. Ensure Node.js version is 18 or higher
4. Try restarting your computer

**Common Commands:**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm install` - Install dependencies
- `npm run lint` - Check for code issues

Happy coding! 🎉
