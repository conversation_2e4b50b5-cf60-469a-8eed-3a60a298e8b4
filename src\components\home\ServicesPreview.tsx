'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useServices } from '@/contexts/ServicesContext';
import {
  PaintBrushIcon,
  ComputerDesktopIcon,
  CameraIcon,
  VideoCameraIcon,
  ArrowRightIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

// Services are now managed through ServicesContext

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6
    }
  }
};

const ServicesPreview: React.FC = () => {
  const { activeServices, loading } = useServices();

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'PaintBrushIcon':
        return PaintBrushIcon;
      case 'ComputerDesktopIcon':
        return ComputerDesktopIcon;
      case 'CameraIcon':
        return CameraIcon;
      case 'VideoCameraIcon':
        return VideoCameraIcon;
      default:
        return PaintBrushIcon;
    }
  };

  const openWhatsApp = () => {
    const message = encodeURIComponent(
      `Hi Elias! 👋\n\nI'm interested in your creative services. Please let me know about pricing and availability.\n\nThank you!`
    );
    const whatsappUrl = `https://wa.me/254703973225?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="max-w-7xl mx-auto text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading services...</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Creative <span 
              className="inline-block"
              style={{
                background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Professional creative services to bring your ideas to life. From design to development, 
            photography to video editing.
          </p>
          <motion.button
            onClick={openWhatsApp}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto"
          >
            <ChatBubbleLeftRightIcon className="w-5 h-5" />
            <span>Get Quote on WhatsApp</span>
          </motion.button>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          {activeServices.slice(0, 4).map((service) => {
            const IconComponent = getIconComponent(service.icon);
            return (
              <motion.div
                key={service.id}
                variants={itemVariants}
                className={`${service.bgColor} ${service.borderColor} border-2 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer`}
              >
                {/* Service Icon */}
                <div className={`bg-gradient-to-r ${service.color} p-3 rounded-lg mb-4 w-fit`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>

                {/* Service Content */}
                <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{service.description}</p>

                {/* Hover Effect */}
                <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex items-center text-sm font-medium text-gray-700">
                    <span>Learn more</span>
                    <ArrowRightIcon className="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/services">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white border-2 border-gray-300 text-gray-700 hover:border-purple-500 hover:text-purple-600 px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center justify-center space-x-2"
              >
                <span>View All Services</span>
                <ArrowRightIcon className="w-5 h-5" />
              </motion.button>
            </Link>
            <motion.button
              onClick={openWhatsApp}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
            >
              <ChatBubbleLeftRightIcon className="w-5 h-5" />
              <span>Contact via WhatsApp</span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesPreview;
