"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/wallpapers/page",{

/***/ "(app-pages-browser)/./src/app/shop/wallpapers/page.tsx":
/*!******************************************!*\
  !*** ./src/app/shop/wallpapers/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallpaper/WallpaperGrid */ \"(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/product */ \"(app-pages-browser)/./src/types/product.ts\");\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/wallpapers */ \"(app-pages-browser)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst WallpapersPage = ()=>{\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Mock wallpaper data - in real app, this would come from your database\n    const mockWallpapers = [\n        {\n            id: \"1\",\n            title: \"Abstract Neon Waves\",\n            description: \"Vibrant neon waves flowing across a dark background, perfect for modern setups\",\n            price: 4.99,\n            originalPrice: 7.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"abstract\",\n                \"neon\",\n                \"waves\",\n                \"modern\"\n            ],\n            inStock: true,\n            featured: true,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"15-25 MB\",\n            createdAt: \"2024-01-01T00:00:00Z\",\n            updatedAt: \"2024-01-01T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"25 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"18 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"12 MB\"\n                    },\n                    {\n                        width: 1080,\n                        height: 2340,\n                        label: \"Mobile\",\n                        downloadUrl: \"/downloads/mobile\",\n                        fileSize: \"8 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#FF00FF\",\n                    \"#00FFFF\",\n                    \"#FF6B6B\",\n                    \"#4ECDC4\",\n                    \"#1A1A1A\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.ABSTRACT,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.MOBILE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 1250\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Minimalist Mountain Range\",\n            description: \"Clean, minimalist mountain silhouettes with gradient sky\",\n            price: 3.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"minimalist\",\n                \"mountains\",\n                \"nature\",\n                \"gradient\"\n            ],\n            inStock: true,\n            featured: false,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"10-20 MB\",\n            createdAt: \"2024-01-02T00:00:00Z\",\n            updatedAt: \"2024-01-02T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"20 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"15 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"10 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#87CEEB\",\n                    \"#FFB6C1\",\n                    \"#DDA0DD\",\n                    \"#F0F8FF\",\n                    \"#2F4F4F\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.MINIMALIST,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.ULTRAWIDE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 890\n            }\n        },\n        {\n            id: \"3\",\n            title: \"Gaming RGB Setup\",\n            description: \"Dynamic RGB lighting effects perfect for gaming setups\",\n            price: 5.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"gaming\",\n                \"rgb\",\n                \"neon\",\n                \"tech\"\n            ],\n            inStock: true,\n            featured: true,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"20-30 MB\",\n            createdAt: \"2024-01-03T00:00:00Z\",\n            updatedAt: \"2024-01-03T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"30 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"22 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"15 MB\"\n                    },\n                    {\n                        width: 3440,\n                        height: 1440,\n                        label: \"Ultrawide\",\n                        downloadUrl: \"/downloads/ultrawide\",\n                        fileSize: \"25 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#FF0080\",\n                    \"#00FF80\",\n                    \"#8000FF\",\n                    \"#FF8000\",\n                    \"#000000\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.GAMING,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.ULTRAWIDE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 2100\n            }\n        },\n        {\n            id: \"4\",\n            title: \"Nature Forest Path\",\n            description: \"Serene forest path with morning sunlight filtering through trees\",\n            price: 2.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"nature\",\n                \"forest\",\n                \"peaceful\",\n                \"photography\"\n            ],\n            inStock: true,\n            featured: false,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\"\n            ],\n            fileSize: \"12-18 MB\",\n            createdAt: \"2024-01-04T00:00:00Z\",\n            updatedAt: \"2024-01-04T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"18 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"12 MB\"\n                    },\n                    {\n                        width: 1080,\n                        height: 2340,\n                        label: \"Mobile\",\n                        downloadUrl: \"/downloads/mobile\",\n                        fileSize: \"6 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#228B22\",\n                    \"#8FBC8F\",\n                    \"#F5DEB3\",\n                    \"#DEB887\",\n                    \"#654321\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.NATURE,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.MOBILE,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.TABLET\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 756\n            }\n        }\n    ];\n    // Filter and search logic\n    const filteredWallpapers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = _data_wallpapers__WEBPACK_IMPORTED_MODULE_4__.wallpapersData;\n        // Search filter\n        if (searchTerm) {\n            filtered = filtered.filter((product)=>product.title.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()) || product.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n        }\n        // Style filter\n        if (filters.style && filters.style.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.style) && filters.style.includes(product.wallpaperData.style);\n            });\n        }\n        // Device filter\n        if (filters.devices && filters.devices.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return (_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.deviceCompatibility.some((device)=>filters.devices.includes(device));\n            });\n        }\n        // Aspect ratio filter\n        if (filters.aspectRatio && filters.aspectRatio.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.aspectRatio) && filters.aspectRatio.includes(product.wallpaperData.aspectRatio);\n            });\n        }\n        // Color filter\n        if (filters.colors && filters.colors.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return (_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.colorPalette.some((color)=>filters.colors.some((filterColor)=>color.toLowerCase() === filterColor.toLowerCase()));\n            });\n        }\n        // Price range filter\n        if (filters.priceRange) {\n            filtered = filtered.filter((product)=>product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]);\n        }\n        // Sort\n        if (filters.sortBy) {\n            filtered.sort((a, b)=>{\n                switch(filters.sortBy){\n                    case \"price-low\":\n                        return a.price - b.price;\n                    case \"price-high\":\n                        return b.price - a.price;\n                    case \"downloads\":\n                        var _b_wallpaperData, _a_wallpaperData;\n                        return (((_b_wallpaperData = b.wallpaperData) === null || _b_wallpaperData === void 0 ? void 0 : _b_wallpaperData.downloadCount) || 0) - (((_a_wallpaperData = a.wallpaperData) === null || _a_wallpaperData === void 0 ? void 0 : _a_wallpaperData.downloadCount) || 0);\n                    case \"popular\":\n                        var _b_wallpaperData1, _a_wallpaperData1;\n                        return (((_b_wallpaperData1 = b.wallpaperData) === null || _b_wallpaperData1 === void 0 ? void 0 : _b_wallpaperData1.downloadCount) || 0) - (((_a_wallpaperData1 = a.wallpaperData) === null || _a_wallpaperData1 === void 0 ? void 0 : _a_wallpaperData1.downloadCount) || 0);\n                    case \"newest\":\n                    default:\n                        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                }\n            });\n        }\n        return filtered;\n    }, [\n        _data_wallpapers__WEBPACK_IMPORTED_MODULE_4__.wallpapersData,\n        searchTerm,\n        filters\n    ]);\n    const handleAddToCart = (product)=>{\n        console.log(\"Added to cart:\", product.title);\n    // In real app, this would add to cart context\n    };\n    const handleToggleFavorite = (productId)=>{\n        setFavorites((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Premium Wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Transform your devices with our collection of high-quality wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filters.sortBy || \"newest\",\n                                    onChange: (e)=>setFilters({\n                                            ...filters,\n                                            sortBy: e.target.value\n                                        }),\n                                    className: \"px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"newest\",\n                                            children: \"Newest\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"popular\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"downloads\",\n                                            children: \"Most Downloaded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-low\",\n                                            children: \"Price: Low to High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-high\",\n                                            children: \"Price: High to Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex border border-gray-300 rounded-xl overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: \"p-3 \".concat(viewMode === \"grid\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"list\"),\n                                            className: \"p-3 \".concat(viewMode === \"list\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"flex items-center space-x-2 px-4 py-3 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            className: \"w-80 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__.WallpaperFiltersPanel, {\n                                filters: filters,\n                                onFiltersChange: setFilters,\n                                onClearFilters: ()=>setFilters({})\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                filteredWallpapers.length,\n                                                \" wallpaper\",\n                                                filteredWallpapers.length !== 1 ? \"s\" : \"\",\n                                                \" found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        Object.keys(filters).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilters({}),\n                                            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Clear all filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    products: filteredWallpapers,\n                                    onAddToCart: handleAddToCart,\n                                    onToggleFavorite: handleToggleFavorite,\n                                    favorites: favorites\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDDBC️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No wallpapers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-4\",\n                                            children: \"Try adjusting your search or filter criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setSearchTerm(\"\");\n                                                setFilters({});\n                                            },\n                                            className: \"px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200\",\n                                            children: \"Clear Search & Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WallpapersPage, \"g/xvvJjcn/C2Sk/2Q1rbAVHszhQ=\");\n_c = WallpapersPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpapersPage);\nvar _c;\n$RefreshReg$(_c, \"WallpapersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/shop/wallpapers/page.tsx\n"));

/***/ })

});