"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/wallpapers/page",{

/***/ "(app-pages-browser)/./src/app/shop/wallpapers/page.tsx":
/*!******************************************!*\
  !*** ./src/app/shop/wallpapers/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallpaper/WallpaperGrid */ \"(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/product */ \"(app-pages-browser)/./src/types/product.ts\");\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/wallpapers */ \"(app-pages-browser)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst WallpapersPage = ()=>{\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Mock wallpaper data - in real app, this would come from your database\n    const mockWallpapers = [\n        {\n            id: \"1\",\n            title: \"Abstract Neon Waves\",\n            description: \"Vibrant neon waves flowing across a dark background, perfect for modern setups\",\n            price: 4.99,\n            originalPrice: 7.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"abstract\",\n                \"neon\",\n                \"waves\",\n                \"modern\"\n            ],\n            inStock: true,\n            featured: true,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"15-25 MB\",\n            createdAt: \"2024-01-01T00:00:00Z\",\n            updatedAt: \"2024-01-01T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"25 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"18 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"12 MB\"\n                    },\n                    {\n                        width: 1080,\n                        height: 2340,\n                        label: \"Mobile\",\n                        downloadUrl: \"/downloads/mobile\",\n                        fileSize: \"8 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#FF00FF\",\n                    \"#00FFFF\",\n                    \"#FF6B6B\",\n                    \"#4ECDC4\",\n                    \"#1A1A1A\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.ABSTRACT,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.MOBILE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 1250\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Minimalist Mountain Range\",\n            description: \"Clean, minimalist mountain silhouettes with gradient sky\",\n            price: 3.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"minimalist\",\n                \"mountains\",\n                \"nature\",\n                \"gradient\"\n            ],\n            inStock: true,\n            featured: false,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"10-20 MB\",\n            createdAt: \"2024-01-02T00:00:00Z\",\n            updatedAt: \"2024-01-02T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"20 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"15 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"10 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#87CEEB\",\n                    \"#FFB6C1\",\n                    \"#DDA0DD\",\n                    \"#F0F8FF\",\n                    \"#2F4F4F\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.MINIMALIST,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.ULTRAWIDE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 890\n            }\n        },\n        {\n            id: \"3\",\n            title: \"Gaming RGB Setup\",\n            description: \"Dynamic RGB lighting effects perfect for gaming setups\",\n            price: 5.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"gaming\",\n                \"rgb\",\n                \"neon\",\n                \"tech\"\n            ],\n            inStock: true,\n            featured: true,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"20-30 MB\",\n            createdAt: \"2024-01-03T00:00:00Z\",\n            updatedAt: \"2024-01-03T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"30 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"22 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"15 MB\"\n                    },\n                    {\n                        width: 3440,\n                        height: 1440,\n                        label: \"Ultrawide\",\n                        downloadUrl: \"/downloads/ultrawide\",\n                        fileSize: \"25 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#FF0080\",\n                    \"#00FF80\",\n                    \"#8000FF\",\n                    \"#FF8000\",\n                    \"#000000\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.GAMING,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.ULTRAWIDE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 2100\n            }\n        },\n        {\n            id: \"4\",\n            title: \"Nature Forest Path\",\n            description: \"Serene forest path with morning sunlight filtering through trees\",\n            price: 2.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"nature\",\n                \"forest\",\n                \"peaceful\",\n                \"photography\"\n            ],\n            inStock: true,\n            featured: false,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\"\n            ],\n            fileSize: \"12-18 MB\",\n            createdAt: \"2024-01-04T00:00:00Z\",\n            updatedAt: \"2024-01-04T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"18 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"12 MB\"\n                    },\n                    {\n                        width: 1080,\n                        height: 2340,\n                        label: \"Mobile\",\n                        downloadUrl: \"/downloads/mobile\",\n                        fileSize: \"6 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#228B22\",\n                    \"#8FBC8F\",\n                    \"#F5DEB3\",\n                    \"#DEB887\",\n                    \"#654321\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.NATURE,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.MOBILE,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.TABLET\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 756\n            }\n        }\n    ];\n    // Filter and search logic\n    const filteredWallpapers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = mockWallpapers;\n        // Search filter\n        if (searchTerm) {\n            filtered = filtered.filter((product)=>product.title.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()) || product.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n        }\n        // Style filter\n        if (filters.style && filters.style.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.style) && filters.style.includes(product.wallpaperData.style);\n            });\n        }\n        // Device filter\n        if (filters.devices && filters.devices.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return (_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.deviceCompatibility.some((device)=>filters.devices.includes(device));\n            });\n        }\n        // Aspect ratio filter\n        if (filters.aspectRatio && filters.aspectRatio.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.aspectRatio) && filters.aspectRatio.includes(product.wallpaperData.aspectRatio);\n            });\n        }\n        // Color filter\n        if (filters.colors && filters.colors.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return (_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.colorPalette.some((color)=>filters.colors.some((filterColor)=>color.toLowerCase() === filterColor.toLowerCase()));\n            });\n        }\n        // Price range filter\n        if (filters.priceRange) {\n            filtered = filtered.filter((product)=>product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]);\n        }\n        // Sort\n        if (filters.sortBy) {\n            filtered.sort((a, b)=>{\n                switch(filters.sortBy){\n                    case \"price-low\":\n                        return a.price - b.price;\n                    case \"price-high\":\n                        return b.price - a.price;\n                    case \"downloads\":\n                        var _b_wallpaperData, _a_wallpaperData;\n                        return (((_b_wallpaperData = b.wallpaperData) === null || _b_wallpaperData === void 0 ? void 0 : _b_wallpaperData.downloadCount) || 0) - (((_a_wallpaperData = a.wallpaperData) === null || _a_wallpaperData === void 0 ? void 0 : _a_wallpaperData.downloadCount) || 0);\n                    case \"popular\":\n                        var _b_wallpaperData1, _a_wallpaperData1;\n                        return (((_b_wallpaperData1 = b.wallpaperData) === null || _b_wallpaperData1 === void 0 ? void 0 : _b_wallpaperData1.downloadCount) || 0) - (((_a_wallpaperData1 = a.wallpaperData) === null || _a_wallpaperData1 === void 0 ? void 0 : _a_wallpaperData1.downloadCount) || 0);\n                    case \"newest\":\n                    default:\n                        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                }\n            });\n        }\n        return filtered;\n    }, [\n        _data_wallpapers__WEBPACK_IMPORTED_MODULE_4__.wallpapersData,\n        searchTerm,\n        filters\n    ]);\n    const handleAddToCart = (product)=>{\n        console.log(\"Added to cart:\", product.title);\n    // In real app, this would add to cart context\n    };\n    const handleToggleFavorite = (productId)=>{\n        setFavorites((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Premium Wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Transform your devices with our collection of high-quality wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filters.sortBy || \"newest\",\n                                    onChange: (e)=>setFilters({\n                                            ...filters,\n                                            sortBy: e.target.value\n                                        }),\n                                    className: \"px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"newest\",\n                                            children: \"Newest\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"popular\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"downloads\",\n                                            children: \"Most Downloaded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-low\",\n                                            children: \"Price: Low to High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-high\",\n                                            children: \"Price: High to Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex border border-gray-300 rounded-xl overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: \"p-3 \".concat(viewMode === \"grid\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"list\"),\n                                            className: \"p-3 \".concat(viewMode === \"list\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"flex items-center space-x-2 px-4 py-3 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            className: \"w-80 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__.WallpaperFiltersPanel, {\n                                filters: filters,\n                                onFiltersChange: setFilters,\n                                onClearFilters: ()=>setFilters({})\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                filteredWallpapers.length,\n                                                \" wallpaper\",\n                                                filteredWallpapers.length !== 1 ? \"s\" : \"\",\n                                                \" found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        Object.keys(filters).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilters({}),\n                                            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Clear all filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    products: filteredWallpapers,\n                                    onAddToCart: handleAddToCart,\n                                    onToggleFavorite: handleToggleFavorite,\n                                    favorites: favorites\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDDBC️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No wallpapers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-4\",\n                                            children: \"Try adjusting your search or filter criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setSearchTerm(\"\");\n                                                setFilters({});\n                                            },\n                                            className: \"px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200\",\n                                            children: \"Clear Search & Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WallpapersPage, \"g/xvvJjcn/C2Sk/2Q1rbAVHszhQ=\");\n_c = WallpapersPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpapersPage);\nvar _c;\n$RefreshReg$(_c, \"WallpapersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/shop/wallpapers/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/wallpapers.ts":
/*!********************************!*\
  !*** ./src/data/wallpapers.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToAdminWallpaper: function() { return /* binding */ convertToAdminWallpaper; },\n/* harmony export */   getFeaturedWallpapers: function() { return /* binding */ getFeaturedWallpapers; },\n/* harmony export */   getTotalDownloads: function() { return /* binding */ getTotalDownloads; },\n/* harmony export */   getWallpaperById: function() { return /* binding */ getWallpaperById; },\n/* harmony export */   getWallpapersByCategory: function() { return /* binding */ getWallpapersByCategory; },\n/* harmony export */   wallpaperCategories: function() { return /* binding */ wallpaperCategories; },\n/* harmony export */   wallpapersData: function() { return /* binding */ wallpapersData; }\n/* harmony export */ });\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/product */ \"(app-pages-browser)/./src/types/product.ts\");\n\n// Shared wallpapers data that both admin and main site will use\nconst wallpapersData = [\n    {\n        id: \"1\",\n        title: \"Abstract Neon Waves\",\n        description: \"Vibrant neon waves flowing across a dark background, perfect for modern setups\",\n        price: 4.99,\n        originalPrice: 7.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"abstract\",\n            \"neon\",\n            \"waves\",\n            \"modern\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"15-25 MB\",\n        createdAt: \"2024-01-01T00:00:00Z\",\n        updatedAt: \"2024-01-01T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"25 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"18 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"8 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF00FF\",\n                \"#00FFFF\",\n                \"#FF6B6B\",\n                \"#4ECDC4\",\n                \"#1A1A1A\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.ABSTRACT,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 1250\n        }\n    },\n    {\n        id: \"2\",\n        title: \"Minimalist Mountain Range\",\n        description: \"Clean, minimalist mountain silhouettes with gradient sky\",\n        price: 3.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"minimalist\",\n            \"mountains\",\n            \"nature\",\n            \"gradient\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"10-20 MB\",\n        createdAt: \"2024-01-02T00:00:00Z\",\n        updatedAt: \"2024-01-02T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"20 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"10 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#87CEEB\",\n                \"#FFB6C1\",\n                \"#DDA0DD\",\n                \"#F0F8FF\",\n                \"#2F4F4F\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.MINIMALIST,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.ULTRAWIDE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 890\n        }\n    },\n    {\n        id: \"3\",\n        title: \"Gaming RGB Setup\",\n        description: \"Dynamic RGB lighting effects perfect for gaming setups\",\n        price: 5.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"gaming\",\n            \"rgb\",\n            \"neon\",\n            \"tech\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"20-30 MB\",\n        createdAt: \"2024-01-03T00:00:00Z\",\n        updatedAt: \"2024-01-03T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"30 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"22 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 3440,\n                    height: 1440,\n                    label: \"Ultrawide\",\n                    downloadUrl: \"/downloads/ultrawide\",\n                    fileSize: \"25 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF0080\",\n                \"#00FF80\",\n                \"#8000FF\",\n                \"#FF8000\",\n                \"#000000\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.GAMING,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.ULTRAWIDE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 2100\n        }\n    },\n    {\n        id: \"4\",\n        title: \"Nature Forest Path\",\n        description: \"Serene forest path with morning sunlight filtering through trees\",\n        price: 2.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"nature\",\n            \"forest\",\n            \"peaceful\",\n            \"photography\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\"\n        ],\n        fileSize: \"12-18 MB\",\n        createdAt: \"2024-01-04T00:00:00Z\",\n        updatedAt: \"2024-01-04T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"18 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"6 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#228B22\",\n                \"#8FBC8F\",\n                \"#F5DEB3\",\n                \"#DEB887\",\n                \"#654321\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.NATURE,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 756\n        }\n    },\n    {\n        id: \"5\",\n        title: \"Geometric Patterns\",\n        description: \"Modern geometric patterns in vibrant colors\",\n        price: 3.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"geometric\",\n            \"patterns\",\n            \"vibrant\",\n            \"modern\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"15-22 MB\",\n        createdAt: \"2024-01-05T00:00:00Z\",\n        updatedAt: \"2024-01-05T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"22 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"16 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"8 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF6B6B\",\n                \"#4ECDC4\",\n                \"#45B7D1\",\n                \"#96CEB4\",\n                \"#FFEAA7\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.GEOMETRIC,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 1420\n        }\n    },\n    {\n        id: \"6\",\n        title: \"Dark Aesthetic\",\n        description: \"Sleek dark theme wallpaper with subtle textures\",\n        price: 2.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"dark\",\n            \"aesthetic\",\n            \"minimal\",\n            \"texture\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"8-15 MB\",\n        createdAt: \"2024-01-06T00:00:00Z\",\n        updatedAt: \"2024-01-06T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"8 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"5 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#1A1A1A\",\n                \"#2D2D2D\",\n                \"#404040\",\n                \"#666666\",\n                \"#808080\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.DARK,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 980\n        }\n    }\n];\n// Helper functions for wallpaper management\nconst getWallpaperById = (id)=>{\n    return wallpapersData.find((wallpaper)=>wallpaper.id === id);\n};\nconst getFeaturedWallpapers = ()=>{\n    return wallpapersData.filter((wallpaper)=>wallpaper.featured);\n};\nconst getWallpapersByCategory = (style)=>{\n    return wallpapersData.filter((wallpaper)=>{\n        var _wallpaper_wallpaperData;\n        return ((_wallpaper_wallpaperData = wallpaper.wallpaperData) === null || _wallpaper_wallpaperData === void 0 ? void 0 : _wallpaper_wallpaperData.style) === style;\n    });\n};\nconst getTotalDownloads = ()=>{\n    return wallpapersData.reduce((total, wallpaper)=>{\n        var _wallpaper_wallpaperData;\n        return total + (((_wallpaper_wallpaperData = wallpaper.wallpaperData) === null || _wallpaper_wallpaperData === void 0 ? void 0 : _wallpaper_wallpaperData.downloadCount) || 0);\n    }, 0);\n};\n// Categories for filtering\nconst wallpaperCategories = [\n    \"All\",\n    \"Abstract\",\n    \"Minimalist\",\n    \"Gaming\",\n    \"Nature\",\n    \"Geometric\",\n    \"Dark\"\n];\n// Convert Product to admin Wallpaper format\nconst convertToAdminWallpaper = (product)=>{\n    var _product_wallpaperData, _product_wallpaperData1, _product_wallpaperData2, _product_wallpaperData3, _product_wallpaperData4, _product_wallpaperData5;\n    return {\n        id: product.id,\n        title: product.title,\n        description: product.description,\n        category: ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.style) || \"Abstract\",\n        tags: product.tags,\n        price: product.price,\n        originalPrice: product.originalPrice,\n        featured: product.featured,\n        downloads: ((_product_wallpaperData1 = product.wallpaperData) === null || _product_wallpaperData1 === void 0 ? void 0 : _product_wallpaperData1.downloadCount) || 0,\n        imageUrl: product.images[0],\n        thumbnailUrl: ((_product_wallpaperData2 = product.wallpaperData) === null || _product_wallpaperData2 === void 0 ? void 0 : _product_wallpaperData2.watermarkedPreview) || product.images[0],\n        resolutions: {\n            desktop: ((_product_wallpaperData3 = product.wallpaperData) === null || _product_wallpaperData3 === void 0 ? void 0 : _product_wallpaperData3.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\")) ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").downloadUrl\n            } : undefined,\n            mobile: ((_product_wallpaperData4 = product.wallpaperData) === null || _product_wallpaperData4 === void 0 ? void 0 : _product_wallpaperData4.resolutions.find((r)=>r.label === \"Mobile\")) ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").downloadUrl\n            } : undefined,\n            tablet: ((_product_wallpaperData5 = product.wallpaperData) === null || _product_wallpaperData5 === void 0 ? void 0 : _product_wallpaperData5.resolutions.find((r)=>r.label === \"Tablet\")) ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").downloadUrl\n            } : undefined\n        },\n        createdAt: product.createdAt,\n        updatedAt: product.updatedAt\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/wallpapers.ts\n"));

/***/ })

});