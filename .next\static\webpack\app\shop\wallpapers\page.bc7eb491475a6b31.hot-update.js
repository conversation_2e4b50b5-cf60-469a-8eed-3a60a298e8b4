"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/wallpapers/page",{

/***/ "(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx":
/*!****************************************************!*\
  !*** ./src/components/wallpaper/WallpaperGrid.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WallpaperFiltersPanel: function() { return /* binding */ WallpaperFiltersPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/product */ \"(app-pages-browser)/./src/types/product.ts\");\n/* harmony import */ var _WallpaperPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./WallpaperPreview */ \"(app-pages-browser)/./src/components/wallpaper/WallpaperPreview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,WallpaperFiltersPanel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst WallpaperGrid = (param)=>{\n    let { products, onAddToCart, onToggleFavorite, favorites = [] } = param;\n    _s();\n    const [previewProduct, setPreviewProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredProduct, setHoveredProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePreview = (product)=>{\n        setPreviewProduct(product);\n    };\n    const handleDownload = (product)=>{\n        // In a real app, this would handle the purchase/download process\n        console.log(\"Download:\", product.title);\n        onAddToCart === null || onAddToCart === void 0 ? void 0 : onAddToCart(product);\n    };\n    const getStyleColor = (style)=>{\n        const colors = {\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ABSTRACT]: \"bg-purple-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.NATURE]: \"bg-green-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.MINIMALIST]: \"bg-gray-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.GAMING]: \"bg-red-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ANIME]: \"bg-pink-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.PHOTOGRAPHY]: \"bg-blue-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.GRADIENT]: \"bg-gradient-to-r from-purple-500 to-pink-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.PATTERN]: \"bg-indigo-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.DARK]: \"bg-gray-900\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.LIGHT]: \"bg-gray-100\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.NEON]: \"bg-cyan-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.VINTAGE]: \"bg-amber-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.MODERN]: \"bg-slate-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ARTISTIC]: \"bg-orange-500\"\n        };\n        return colors[style] || \"bg-gray-500\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    children: products.map((product)=>{\n                        var _product_wallpaperData, _product_wallpaperData1, _product_wallpaperData2, _product_wallpaperData3, _product_wallpaperData4, _product_wallpaperData5, _product_wallpaperData6;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            layout: true,\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            className: \"group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300\",\n                            onMouseEnter: ()=>setHoveredProduct(product.id),\n                            onMouseLeave: ()=>setHoveredProduct(null),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative aspect-[3/4] overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.watermarkedPreview) || product.images[0],\n                                            alt: product.title,\n                                            className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>handlePreview(product),\n                                                        className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200\",\n                                                        title: \"Preview\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>onToggleFavorite === null || onToggleFavorite === void 0 ? void 0 : onToggleFavorite(product.id),\n                                                        className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200\",\n                                                        title: \"Add to Favorites\",\n                                                        children: favorites.includes(product.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>handleDownload(product),\n                                                        className: \"p-3 bg-purple-yellow-gradient rounded-full text-white hover:shadow-lg transition-all duration-200\",\n                                                        title: \"Add to Cart\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        ((_product_wallpaperData1 = product.wallpaperData) === null || _product_wallpaperData1 === void 0 ? void 0 : _product_wallpaperData1.style) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1 rounded-full text-xs font-medium text-white \".concat(getStyleColor(product.wallpaperData.style)),\n                                                children: product.wallpaperData.style.charAt(0).toUpperCase() + product.wallpaperData.style.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        ((_product_wallpaperData2 = product.wallpaperData) === null || _product_wallpaperData2 === void 0 ? void 0 : _product_wallpaperData2.resolutions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium\",\n                                                children: [\n                                                    product.wallpaperData.resolutions.length,\n                                                    \" resolutions\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-1 line-clamp-1\",\n                                            children: product.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                    children: [\n                                                        ((_product_wallpaperData3 = product.wallpaperData) === null || _product_wallpaperData3 === void 0 ? void 0 : _product_wallpaperData3.aspectRatio) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.wallpaperData.aspectRatio\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        product.fileFormat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: product.fileFormat.join(\", \")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                ((_product_wallpaperData4 = product.wallpaperData) === null || _product_wallpaperData4 === void 0 ? void 0 : _product_wallpaperData4.downloadCount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DownloadIcon, {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.wallpaperData.downloadCount.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        ((_product_wallpaperData5 = product.wallpaperData) === null || _product_wallpaperData5 === void 0 ? void 0 : _product_wallpaperData5.colorPalette) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1 mb-3\",\n                                            children: [\n                                                product.wallpaperData.colorPalette.slice(0, 5).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full border border-gray-200\",\n                                                        style: {\n                                                            backgroundColor: color\n                                                        }\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                product.wallpaperData.colorPalette.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs text-gray-600\",\n                                                    children: [\n                                                        \"+\",\n                                                        product.wallpaperData.colorPalette.length - 5\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        ((_product_wallpaperData6 = product.wallpaperData) === null || _product_wallpaperData6 === void 0 ? void 0 : _product_wallpaperData6.deviceCompatibility) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: [\n                                                product.wallpaperData.deviceCompatibility.slice(0, 3).map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md\",\n                                                        children: device\n                                                    }, device, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                product.wallpaperData.deviceCompatibility.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        product.wallpaperData.deviceCompatibility.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.price.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 line-through\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.originalPrice.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: ()=>handleDownload(product),\n                                                    className: \"px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-200\",\n                                                    children: \"Add to Cart\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            previewProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WallpaperPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                product: previewProduct,\n                isOpen: !!previewProduct,\n                onClose: ()=>setPreviewProduct(null),\n                onDownload: (resolution)=>{\n                    console.log(\"Download resolution:\", resolution);\n                    handleDownload(previewProduct);\n                    setPreviewProduct(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(WallpaperGrid, \"qWycweb6R+Xv+UUVoR7Ea9aO4N8=\");\n_c = WallpaperGrid;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpaperGrid);\nconst WallpaperFiltersPanel = (param)=>{\n    let { filters, onFiltersChange, onClearFilters } = param;\n    const styles = Object.values(_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle);\n    const devices = Object.values(_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType);\n    const aspectRatios = [\n        \"16:9\",\n        \"16:10\",\n        \"21:9\",\n        \"4:3\",\n        \"9:16\",\n        \"3:2\"\n    ];\n    const commonColors = [\n        \"#FF6B6B\",\n        \"#4ECDC4\",\n        \"#45B7D1\",\n        \"#96CEB4\",\n        \"#FFEAA7\",\n        \"#DDA0DD\",\n        \"#98D8C8\",\n        \"#F7DC6F\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClearFilters,\n                        className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Style\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: styles.map((style)=>{\n                            var _filters_style;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filters_style = filters.style) === null || _filters_style === void 0 ? void 0 : _filters_style.includes(style)) || false,\n                                        onChange: (e)=>{\n                                            const newStyles = e.target.checked ? [\n                                                ...filters.style || [],\n                                                style\n                                            ] : (filters.style || []).filter((s)=>s !== style);\n                                            onFiltersChange({\n                                                ...filters,\n                                                style: newStyles\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 capitalize\",\n                                        children: style\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, style, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Device\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: devices.map((device)=>{\n                            var _filters_devices;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filters_devices = filters.devices) === null || _filters_devices === void 0 ? void 0 : _filters_devices.includes(device)) || false,\n                                        onChange: (e)=>{\n                                            const newDevices = e.target.checked ? [\n                                                ...filters.devices || [],\n                                                device\n                                            ] : (filters.devices || []).filter((d)=>d !== device);\n                                            onFiltersChange({\n                                                ...filters,\n                                                devices: newDevices\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 capitalize\",\n                                        children: device.replace(\"_\", \" \")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, device, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Aspect Ratio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: aspectRatios.map((ratio)=>{\n                            var _filters_aspectRatio;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filters_aspectRatio = filters.aspectRatio) === null || _filters_aspectRatio === void 0 ? void 0 : _filters_aspectRatio.includes(ratio)) || false,\n                                        onChange: (e)=>{\n                                            const newRatios = e.target.checked ? [\n                                                ...filters.aspectRatio || [],\n                                                ratio\n                                            ] : (filters.aspectRatio || []).filter((r)=>r !== ratio);\n                                            onFiltersChange({\n                                                ...filters,\n                                                aspectRatio: newRatios\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: ratio\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, ratio, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Colors\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-2\",\n                        children: commonColors.map((color)=>{\n                            var _filters_colors;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    var _filters_colors;\n                                    const newColors = ((_filters_colors = filters.colors) === null || _filters_colors === void 0 ? void 0 : _filters_colors.includes(color)) ? (filters.colors || []).filter((c)=>c !== color) : [\n                                        ...filters.colors || [],\n                                        color\n                                    ];\n                                    onFiltersChange({\n                                        ...filters,\n                                        colors: newColors\n                                    });\n                                },\n                                className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(((_filters_colors = filters.colors) === null || _filters_colors === void 0 ? void 0 : _filters_colors.includes(color)) ? \"border-gray-800 scale-110\" : \"border-gray-300 hover:border-gray-400\"),\n                                style: {\n                                    backgroundColor: color\n                                },\n                                title: color\n                            }, color, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = WallpaperFiltersPanel;\nvar _c, _c1;\n$RefreshReg$(_c, \"WallpaperGrid\");\n$RefreshReg$(_c1, \"WallpaperFiltersPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx\n"));

/***/ })

});