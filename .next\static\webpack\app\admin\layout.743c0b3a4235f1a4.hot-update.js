"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/app/admin/layout.tsx":
/*!**********************************!*\
  !*** ./src/app/admin/layout.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BriefcaseIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,BriefcaseIcon,ChartBarIcon,CogIcon,HomeIcon,MagnifyingGlassIcon,PhotoIcon,ShoppingBagIcon,UsersIcon,WrenchScrewdriverIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// import { useAuth } from '@/contexts/AuthContext';\n// import ProtectedRoute from '@/components/auth/ProtectedRoute';\n\n\n\nconst AdminLayout = (param)=>{\n    let { children } = param;\n    var _user_displayName, _user_email, _navigation_find, _user_displayName1, _user_email1;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const { user, logout } = useAuth();\n    const user = {\n        displayName: \"Admin User\",\n        email: \"<EMAIL>\"\n    }; // Temporary\n    const logout = async ()=>{}; // Temporary\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/admin\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Services\",\n            href: \"/admin/services\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Products\",\n            href: \"/admin/products\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"Wallpapers\",\n            href: \"/admin/wallpapers\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"Orders\",\n            href: \"/admin/orders\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Portfolio\",\n            href: \"/admin/portfolio\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Users\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/admin/settings\",\n            icon: _barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n        } catch (error) {\n            console.error(\"Failed to logout:\", error);\n        }\n    };\n    return(// <ProtectedRoute requireAuth requireAdmin>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                    children: sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                exit: {\n                                    opacity: 0\n                                },\n                                className: \"fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden\",\n                                onClick: ()=>setSidebarOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    x: -300\n                                },\n                                animate: {\n                                    x: 0\n                                },\n                                exit: {\n                                    x: -300\n                                },\n                                transition: {\n                                    type: \"tween\",\n                                    duration: 0.3\n                                },\n                                className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between h-16 px-4 border-b border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-lg flex items-center justify-center\",\n                                                        style: {\n                                                            background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold\",\n                                                            children: \"E\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-gray-900\",\n                                                        children: \"Admin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(false),\n                                                className: \"p-2 rounded-md text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-5 px-2\",\n                                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                onClick: ()=>setSidebarOpen(false),\n                                                className: \"group flex items-center px-2 py-2 text-base font-medium rounded-md mb-1 \".concat(pathname === item.href ? \"text-white\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                                style: pathname === item.href ? {\n                                                    background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                                } : {},\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"mr-4 h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    item.name\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col flex-grow bg-white border-r border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center h-16 px-4 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-lg flex items-center justify-center\",\n                                            style: {\n                                                background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"E\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"mt-5 flex-1 px-2 space-y-1\",\n                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 \".concat(pathname === item.href ? \"bg-purple-yellow-gradient text-white shadow-lg\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"mr-3 h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 p-4 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-purple-yellow-gradient rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-medium text-sm\",\n                                                children: (user === null || user === void 0 ? void 0 : (_user_displayName = user.displayName) === null || _user_displayName === void 0 ? void 0 : _user_displayName.charAt(0)) || (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.charAt(0)) || \"A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: (user === null || user === void 0 ? void 0 : user.displayName) || \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"ml-3 p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                                            title: \"Sign out\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:pl-64 flex flex-col flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSidebarOpen(true),\n                                                className: \"p-2 rounded-md text-gray-400 hover:text-gray-600 lg:hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 lg:ml-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                    children: ((_navigation_find = navigation.find((item)=>item.href === pathname)) === null || _navigation_find === void 0 ? void 0 : _navigation_find.name) || \"Admin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"hidden md:flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Back to Site\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search...\",\n                                                            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-2 text-gray-400 hover:text-gray-600 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_BriefcaseIcon_ChartBarIcon_CogIcon_HomeIcon_MagnifyingGlassIcon_PhotoIcon_ShoppingBagIcon_UsersIcon_WrenchScrewdriverIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-purple-yellow-gradient rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-medium text-sm\",\n                                                        children: (user === null || user === void 0 ? void 0 : (_user_displayName1 = user.displayName) === null || _user_displayName1 === void 0 ? void 0 : _user_displayName1.charAt(0)) || (user === null || user === void 0 ? void 0 : (_user_email1 = user.email) === null || _user_email1 === void 0 ? void 0 : _user_email1.charAt(0)) || \"A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 p-4 sm:p-6 lg:p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined));\n};\n_s(AdminLayout, \"18O0UTC9fcnwNZFwX6j502UeK4k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = AdminLayout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AdminLayout);\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/layout.tsx\n"));

/***/ })

});