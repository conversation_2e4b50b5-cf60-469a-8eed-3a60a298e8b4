/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CLayoutWrapper.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CServicesContext.tsx&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CLayoutWrapper.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CServicesContext.tsx&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/LayoutWrapper.tsx */ \"(app-pages-browser)/./src/components/layout/LayoutWrapper.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/CartContext.tsx */ \"(app-pages-browser)/./src/contexts/CartContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ServicesContext.tsx */ \"(app-pages-browser)/./src/contexts/ServicesContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CLayoutWrapper.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CServicesContext.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"609a5a0afa20\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZGY2NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYwOWE1YTBhZmEyMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ServicesContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/ServicesContext.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesProvider: function() { return /* binding */ ServicesProvider; },\n/* harmony export */   useServices: function() { return /* binding */ useServices; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ServicesProvider,useServices,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ServicesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst defaultServices = [\n    {\n        id: \"1\",\n        title: \"Graphic Design\",\n        description: \"Creating visually stunning designs that communicate your brand message effectively\",\n        features: [\n            \"Logo Design & Branding\",\n            \"Business Cards & Stationery\",\n            \"Brochures & Flyers\",\n            \"Social Media Graphics\",\n            \"Print Design\",\n            \"Brand Identity Development\"\n        ],\n        tools: [\n            \"Adobe Illustrator\",\n            \"Photoshop\",\n            \"InDesign\",\n            \"Figma\"\n        ],\n        icon: \"PaintBrushIcon\",\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"2\",\n        title: \"Web Design\",\n        description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n        features: [\n            \"Responsive Web Design\",\n            \"UI/UX Design\",\n            \"E-commerce Websites\",\n            \"Landing Pages\",\n            \"Web Applications\",\n            \"Website Maintenance\"\n        ],\n        tools: [\n            \"React\",\n            \"Next.js\",\n            \"Tailwind CSS\",\n            \"WordPress\"\n        ],\n        icon: \"ComputerDesktopIcon\",\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"3\",\n        title: \"Photography\",\n        description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n        features: [\n            \"Product Photography\",\n            \"Portrait Photography\",\n            \"Event Photography\",\n            \"Commercial Photography\",\n            \"Photo Editing & Retouching\",\n            \"Studio & Location Shoots\"\n        ],\n        tools: [\n            \"Canon EOS\",\n            \"Lightroom\",\n            \"Photoshop\",\n            \"Studio Lighting\"\n        ],\n        icon: \"CameraIcon\",\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"4\",\n        title: \"Video Editing\",\n        description: \"Creating engaging video content with professional editing and motion graphics\",\n        features: [\n            \"Video Production\",\n            \"Motion Graphics\",\n            \"Color Correction\",\n            \"Audio Enhancement\",\n            \"Social Media Videos\",\n            \"Commercial Videos\"\n        ],\n        tools: [\n            \"After Effects\",\n            \"Premiere Pro\",\n            \"DaVinci Resolve\",\n            \"Final Cut Pro\"\n        ],\n        icon: \"VideoCameraIcon\",\n        color: \"from-red-500 to-red-600\",\n        bgColor: \"bg-red-50\",\n        borderColor: \"border-red-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\nconst ServicesProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize services from localStorage or use defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadServices = ()=>{\n            try {\n                const savedServices = localStorage.getItem(\"elishop_services\");\n                if (savedServices) {\n                    const parsedServices = JSON.parse(savedServices);\n                    setServices(parsedServices);\n                } else {\n                    setServices(defaultServices);\n                    localStorage.setItem(\"elishop_services\", JSON.stringify(defaultServices));\n                }\n            } catch (error) {\n                console.error(\"Error loading services:\", error);\n                setServices(defaultServices);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadServices();\n    }, []);\n    // Save services to localStorage whenever services change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && services.length > 0) {\n            localStorage.setItem(\"elishop_services\", JSON.stringify(services));\n        }\n    }, [\n        services,\n        loading\n    ]);\n    const activeServices = services.filter((service)=>service.isActive);\n    const addService = (serviceData)=>{\n        const now = new Date().toISOString();\n        const newService = {\n            ...serviceData,\n            id: Date.now().toString(),\n            createdAt: now,\n            updatedAt: now\n        };\n        setServices((prev)=>[\n                ...prev,\n                newService\n            ]);\n    };\n    const updateService = (id, serviceData)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    ...serviceData,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const deleteService = (id)=>{\n        setServices((prev)=>prev.filter((service)=>service.id !== id));\n    };\n    const toggleServiceActive = (id)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    isActive: !service.isActive,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const getService = (id)=>{\n        return services.find((service)=>service.id === id);\n    };\n    const value = {\n        services,\n        activeServices,\n        addService,\n        updateService,\n        deleteService,\n        toggleServiceActive,\n        getService,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\ServicesContext.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServicesProvider, \"b/p1Mw8EtDCdIBIHijS5szgIZQ4=\");\n_c = ServicesProvider;\nconst useServices = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServicesContext);\n    if (context === undefined) {\n        throw new Error(\"useServices must be used within a ServicesProvider\");\n    }\n    return context;\n};\n_s1(useServices, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesContext);\nvar _c;\n$RefreshReg$(_c, \"ServicesProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ServicesContext.tsx\n"));

/***/ })

});