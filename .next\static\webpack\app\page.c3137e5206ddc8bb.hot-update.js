"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ServicesContext */ \"(app-pages-browser)/./src/contexts/ServicesContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst services = [\n    {\n        id: \"graphic-design\",\n        title: \"Graphic Design\",\n        icon: _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        description: \"Creating visually stunning designs that communicate your brand message effectively\",\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\"\n    },\n    {\n        id: \"web-design\",\n        title: \"Web Design\",\n        icon: _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\"\n    },\n    {\n        id: \"photography\",\n        title: \"Photography\",\n        icon: _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\"\n    },\n    {\n        id: \"video-editing\",\n        title: \"Video Editing\",\n        icon: _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        description: \"Creating engaging video content with professional editing and motion graphics\",\n        color: \"from-red-500 to-red-600\",\n        bgColor: \"bg-red-50\",\n        borderColor: \"border-red-200\"\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6\n        }\n    }\n};\nconst ServicesPreview = ()=>{\n    _s();\n    const { activeServices, loading } = (0,_contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__.useServices)();\n    const getIconComponent = (iconName)=>{\n        switch(iconName){\n            case \"PaintBrushIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"ComputerDesktopIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"CameraIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"VideoCameraIcon\":\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        }\n    };\n    const openWhatsApp = ()=>{\n        const message = encodeURIComponent(\"Hi Elias! \\uD83D\\uDC4B\\n\\nI'm interested in your creative services. Please let me know about pricing and availability.\\n\\nThank you!\");\n        const whatsappUrl = \"https://wa.me/254703973225?text=\".concat(message);\n        window.open(whatsappUrl, \"_blank\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading services...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Creative \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        backgroundClip: \"text\"\n                                    },\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 22\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                            children: \"Professional creative services to bring your ideas to life. From design to development, photography to video editing.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                            onClick: openWhatsApp,\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Get Quote on WhatsApp\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    children: services.map((service)=>{\n                        const IconComponent = service.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            variants: itemVariants,\n                            className: \"\".concat(service.bgColor, \" \").concat(service.borderColor, \" border-2 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r \".concat(service.color, \" p-3 rounded-lg mb-4 w-fit\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                    children: service.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm leading-relaxed\",\n                                    children: service.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm font-medium text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Learn more\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, service.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/services\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"bg-white border-2 border-gray-300 text-gray-700 hover:border-purple-500 hover:text-purple-600 px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"View All Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                onClick: openWhatsApp,\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Contact via WhatsApp\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServicesPreview, \"KOeUnzKrCvIJ8uFseZamQM7a3qY=\", false, function() {\n    return [\n        _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__.useServices\n    ];\n});\n_c = ServicesPreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesPreview);\nvar _c;\n$RefreshReg$(_c, \"ServicesPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});