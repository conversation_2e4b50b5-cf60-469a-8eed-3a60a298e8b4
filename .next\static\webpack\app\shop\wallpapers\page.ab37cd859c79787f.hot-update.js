"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/wallpapers/page",{

/***/ "(app-pages-browser)/./src/app/shop/wallpapers/page.tsx":
/*!******************************************!*\
  !*** ./src/app/shop/wallpapers/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallpaper/WallpaperGrid */ \"(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx\");\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/wallpapers */ \"(app-pages-browser)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst WallpapersPage = ()=>{\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Filter and search logic\n    const filteredWallpapers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = _data_wallpapers__WEBPACK_IMPORTED_MODULE_3__.wallpapersData;\n        // Search filter\n        if (searchTerm) {\n            filtered = filtered.filter((product)=>product.title.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()) || product.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n        }\n        // Style filter\n        if (filters.style && filters.style.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.style) && filters.style.includes(product.wallpaperData.style);\n            });\n        }\n        // Device filter\n        if (filters.devices && filters.devices.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return (_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.deviceCompatibility.some((device)=>filters.devices.includes(device));\n            });\n        }\n        // Aspect ratio filter\n        if (filters.aspectRatio && filters.aspectRatio.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.aspectRatio) && filters.aspectRatio.includes(product.wallpaperData.aspectRatio);\n            });\n        }\n        // Color filter\n        if (filters.colors && filters.colors.length > 0) {\n            filtered = filtered.filter((product)=>{\n                var _product_wallpaperData;\n                return (_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.colorPalette.some((color)=>filters.colors.some((filterColor)=>color.toLowerCase() === filterColor.toLowerCase()));\n            });\n        }\n        // Price range filter\n        if (filters.priceRange) {\n            filtered = filtered.filter((product)=>product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]);\n        }\n        // Sort\n        if (filters.sortBy) {\n            filtered.sort((a, b)=>{\n                switch(filters.sortBy){\n                    case \"price-low\":\n                        return a.price - b.price;\n                    case \"price-high\":\n                        return b.price - a.price;\n                    case \"downloads\":\n                        var _b_wallpaperData, _a_wallpaperData;\n                        return (((_b_wallpaperData = b.wallpaperData) === null || _b_wallpaperData === void 0 ? void 0 : _b_wallpaperData.downloadCount) || 0) - (((_a_wallpaperData = a.wallpaperData) === null || _a_wallpaperData === void 0 ? void 0 : _a_wallpaperData.downloadCount) || 0);\n                    case \"popular\":\n                        var _b_wallpaperData1, _a_wallpaperData1;\n                        return (((_b_wallpaperData1 = b.wallpaperData) === null || _b_wallpaperData1 === void 0 ? void 0 : _b_wallpaperData1.downloadCount) || 0) - (((_a_wallpaperData1 = a.wallpaperData) === null || _a_wallpaperData1 === void 0 ? void 0 : _a_wallpaperData1.downloadCount) || 0);\n                    case \"newest\":\n                    default:\n                        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                }\n            });\n        }\n        return filtered;\n    }, [\n        _data_wallpapers__WEBPACK_IMPORTED_MODULE_3__.wallpapersData,\n        searchTerm,\n        filters\n    ]);\n    const handleAddToCart = (product)=>{\n        console.log(\"Added to cart:\", product.title);\n    // In real app, this would add to cart context\n    };\n    const handleToggleFavorite = (productId)=>{\n        setFavorites((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Premium Wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Transform your devices with our collection of high-quality wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filters.sortBy || \"newest\",\n                                    onChange: (e)=>setFilters({\n                                            ...filters,\n                                            sortBy: e.target.value\n                                        }),\n                                    className: \"px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"newest\",\n                                            children: \"Newest\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"popular\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"downloads\",\n                                            children: \"Most Downloaded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-low\",\n                                            children: \"Price: Low to High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-high\",\n                                            children: \"Price: High to Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex border border-gray-300 rounded-xl overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: \"p-3 \".concat(viewMode === \"grid\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"list\"),\n                                            className: \"p-3 \".concat(viewMode === \"list\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"flex items-center space-x-2 px-4 py-3 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            className: \"w-80 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__.WallpaperFiltersPanel, {\n                                filters: filters,\n                                onFiltersChange: setFilters,\n                                onClearFilters: ()=>setFilters({})\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                filteredWallpapers.length,\n                                                \" wallpaper\",\n                                                filteredWallpapers.length !== 1 ? \"s\" : \"\",\n                                                \" found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        Object.keys(filters).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilters({}),\n                                            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Clear all filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    products: filteredWallpapers,\n                                    onAddToCart: handleAddToCart,\n                                    onToggleFavorite: handleToggleFavorite,\n                                    favorites: favorites\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDDBC️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No wallpapers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-4\",\n                                            children: \"Try adjusting your search or filter criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setSearchTerm(\"\");\n                                                setFilters({});\n                                            },\n                                            className: \"px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200\",\n                                            children: \"Clear Search & Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WallpapersPage, \"g/xvvJjcn/C2Sk/2Q1rbAVHszhQ=\");\n_c = WallpapersPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpapersPage);\nvar _c;\n$RefreshReg$(_c, \"WallpapersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/shop/wallpapers/page.tsx\n"));

/***/ })

});