'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PhotoIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  XCircleIcon,
  XMarkIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  DeviceTabletIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { wallpapersData, convertToAdminWallpaper, getTotalDownloads, wallpaperCategories } from '@/data/wallpapers';

interface Wallpaper {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  price: number;
  originalPrice?: number;
  featured: boolean;
  downloads: number;
  imageUrl: string;
  thumbnailUrl: string;
  resolutions: {
    desktop: { width: number; height: number; url: string; };
    mobile: { width: number; height: number; url: string; };
    tablet: { width: number; height: number; url: string; };
  };
  createdAt: string;
  updatedAt: string;
}

const WallpapersManagement: React.FC = () => {
  const [wallpapers, setWallpapers] = useState<Wallpaper[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [wallpaperToDelete, setWallpaperToDelete] = useState<string | null>(null);
  const [wallpaperToEdit, setWallpaperToEdit] = useState<Wallpaper | null>(null);
  const [wallpaperToView, setWallpaperToView] = useState<Wallpaper | null>(null);



  const categories = wallpaperCategories.map(cat => cat.toLowerCase());

  useEffect(() => {
    // Convert shared wallpapers data to admin format
    const adminWallpapers = wallpapersData.map(convertToAdminWallpaper);
    setWallpapers(adminWallpapers);
    setLoading(false);
  }, []);

  const filteredWallpapers = wallpapers.filter(wallpaper => {
    const matchesSearch = wallpaper.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         wallpaper.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         wallpaper.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || wallpaper.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleDeleteWallpaper = (id: string) => {
    setWallpaperToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (wallpaperToDelete) {
      setWallpapers(prev => prev.filter(w => w.id !== wallpaperToDelete));
      setWallpaperToDelete(null);
      setShowDeleteModal(false);
    }
  };

  const toggleFeatured = (id: string) => {
    setWallpapers(prev => prev.map(w =>
      w.id === id ? { ...w, featured: !w.featured } : w
    ));
  };

  const handleViewWallpaper = (wallpaper: Wallpaper) => {
    setWallpaperToView(wallpaper);
    setShowViewModal(true);
  };

  const handleEditWallpaper = (wallpaper: Wallpaper) => {
    setWallpaperToEdit(wallpaper);
    setShowEditModal(true);
  };

  const handleUpdateWallpaper = (updatedWallpaper: Wallpaper) => {
    setWallpapers(prev => prev.map(w =>
      w.id === updatedWallpaper.id ? updatedWallpaper : w
    ));
    setShowEditModal(false);
    setWallpaperToEdit(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Wallpapers Management</h1>
          <p className="text-gray-600 mt-1">Manage your wallpaper collection</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowUploadModal(true)}
            className="text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center space-x-2"
            style={{
              background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)'
            }}
          >
            <PlusIcon className="w-5 h-5" />
            <span>Add Wallpaper</span>
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <PhotoIcon className="w-8 h-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Wallpapers</p>
              <p className="text-2xl font-bold text-gray-900">{wallpapers.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <EyeIcon className="w-8 h-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Downloads</p>
              <p className="text-2xl font-bold text-gray-900">
                {wallpapers.reduce((sum, w) => sum + w.downloads, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <CheckCircleIcon className="w-8 h-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Featured</p>
              <p className="text-2xl font-bold text-gray-900">
                {wallpapers.filter(w => w.featured).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <FunnelIcon className="w-8 h-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Categories</p>
              <p className="text-2xl font-bold text-gray-900">{categories.length - 1}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search wallpapers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  selectedCategory === category
                    ? 'text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                style={selectedCategory === category ? {
                  background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)'
                } : {}}
              >
                {category === 'all' ? 'All Categories' : category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Wallpapers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AnimatePresence>
          {filteredWallpapers.map((wallpaper) => (
            <motion.div
              key={wallpaper.id}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200"
            >
              {/* Image */}
              <div className="relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100">
                <div className="absolute inset-0 flex items-center justify-center">
                  <PhotoIcon className="w-16 h-16 text-primary-300" />
                </div>
                
                {/* Featured Badge */}
                {wallpaper.featured && (
                  <div className="absolute top-2 left-2">
                    <span
                      className="text-white px-2 py-1 rounded-full text-xs font-medium"
                      style={{
                        background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)'
                      }}
                    >
                      Featured
                    </span>
                  </div>
                )}

                {/* Actions */}
                <div className="absolute top-2 right-2 flex space-x-1">
                  <button
                    onClick={() => toggleFeatured(wallpaper.id)}
                    className="bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200"
                    title={wallpaper.featured ? 'Remove from featured' : 'Add to featured'}
                  >
                    <CheckCircleIcon className={`w-4 h-4 ${wallpaper.featured ? 'text-purple-600' : 'text-gray-400'}`} />
                  </button>
                  <button
                    onClick={() => handleViewWallpaper(wallpaper)}
                    className="bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200"
                    title="View wallpaper"
                  >
                    <EyeIcon className="w-4 h-4 text-gray-600" />
                  </button>
                  <button
                    onClick={() => handleEditWallpaper(wallpaper)}
                    className="bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200"
                    title="Edit wallpaper"
                  >
                    <PencilIcon className="w-4 h-4 text-blue-600" />
                  </button>
                  <button
                    onClick={() => handleDeleteWallpaper(wallpaper.id)}
                    className="bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200"
                  >
                    <TrashIcon className="w-4 h-4 text-red-600" />
                  </button>
                </div>

                {/* Resolution Icons */}
                <div className="absolute bottom-2 left-2 flex space-x-1">
                  <div className="bg-white/90 backdrop-blur-sm p-1 rounded">
                    <ComputerDesktopIcon className="w-3 h-3 text-gray-600" />
                  </div>
                  <div className="bg-white/90 backdrop-blur-sm p-1 rounded">
                    <DeviceTabletIcon className="w-3 h-3 text-gray-600" />
                  </div>
                  <div className="bg-white/90 backdrop-blur-sm p-1 rounded">
                    <DevicePhoneMobileIcon className="w-3 h-3 text-gray-600" />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">{wallpaper.title}</h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{wallpaper.description}</p>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {wallpaper.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                  {wallpaper.tags.length > 3 && (
                    <span className="text-xs text-gray-500">+{wallpaper.tags.length - 3}</span>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-600">{wallpaper.downloads} downloads</span>
                    <span className="font-semibold text-gray-900">${wallpaper.price}</span>
                  </div>
                  <span className="text-xs text-gray-500">{wallpaper.category}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* No Results */}
      {filteredWallpapers.length === 0 && (
        <div className="text-center py-12">
          <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No wallpapers found</h3>
          <p className="text-gray-600">Try adjusting your search or filter criteria</p>
        </div>
      )}

      {/* Upload Modal */}
      <WallpaperUploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={(wallpaper) => {
          setWallpapers(prev => [wallpaper, ...prev]);
          setShowUploadModal(false);
        }}
      />

      {/* Edit Modal */}
      {wallpaperToEdit && (
        <WallpaperEditModal
          isOpen={showEditModal}
          wallpaper={wallpaperToEdit}
          onClose={() => {
            setShowEditModal(false);
            setWallpaperToEdit(null);
          }}
          onUpdate={handleUpdateWallpaper}
        />
      )}

      {/* View Modal */}
      {wallpaperToView && (
        <WallpaperViewModal
          isOpen={showViewModal}
          wallpaper={wallpaperToView}
          onClose={() => {
            setShowViewModal(false);
            setWallpaperToView(null);
          }}
        />
      )}

      {/* Delete Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Wallpaper</h3>
            <p className="text-gray-600 mb-6">Are you sure you want to delete this wallpaper? This action cannot be undone.</p>
            <div className="flex space-x-3">
              <button
                onClick={confirmDelete}
                className="flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors duration-200"
              >
                Delete
              </button>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-200"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Wallpaper Upload Modal Component
const WallpaperUploadModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onUpload: (wallpaper: Wallpaper) => void;
}> = ({ isOpen, onClose, onUpload }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'Abstract',
    tags: '',
    price: '',
    originalPrice: '',
    featured: false
  });
  const [uploading, setUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUploading(true);
    setUploadStatus('idle');

    try {
      // Simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));

      const newWallpaper: Wallpaper = {
        id: Date.now().toString(),
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        price: parseFloat(formData.price) || 0,
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        featured: formData.featured,
        downloads: 0,
        imageUrl: '/api/placeholder/1920/1080',
        thumbnailUrl: '/api/placeholder/400/300',
        resolutions: {
          desktop: { width: 1920, height: 1080, url: '/api/placeholder/1920/1080' },
          mobile: { width: 1080, height: 1920, url: '/api/placeholder/1080/1920' },
          tablet: { width: 1536, height: 2048, url: '/api/placeholder/1536/2048' }
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onUpload(newWallpaper);
      setUploadStatus('success');

      // Reset form
      setFormData({
        title: '',
        description: '',
        category: 'Abstract',
        tags: '',
        price: '',
        originalPrice: '',
        featured: false
      });

    } catch (error) {
      setUploadStatus('error');
    } finally {
      setUploading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Upload New Wallpaper</h3>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Wallpaper Image</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200">
              <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                Choose Image Files
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                className="hidden"
              />
              <p className="text-sm text-gray-500 mt-2">
                Upload desktop, tablet, and mobile versions (JPG, PNG)
              </p>
            </div>
          </div>

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter wallpaper title"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
              <select
                required
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="Abstract">Abstract</option>
                <option value="Nature">Nature</option>
                <option value="Geometric">Geometric</option>
                <option value="Minimal">Minimal</option>
                <option value="Dark">Dark</option>
                <option value="Light">Light</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Describe your wallpaper"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter tags separated by commas"
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
              <input
                type="number"
                step="0.01"
                required
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="0.00"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Original Price ($)</label>
              <input
                type="number"
                step="0.01"
                value={formData.originalPrice}
                onChange={(e) => setFormData(prev => ({ ...prev, originalPrice: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Featured */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="featured"
              checked={formData.featured}
              onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
              Mark as featured wallpaper
            </label>
          </div>

          {/* Status Messages */}
          {uploadStatus === 'success' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2">
              <CheckCircleIcon className="w-5 h-5 text-green-600" />
              <span className="text-green-800">Wallpaper uploaded successfully!</span>
            </div>
          )}

          {uploadStatus === 'error' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
              <XCircleIcon className="w-5 h-5 text-red-600" />
              <span className="text-red-800">Upload failed. Please try again.</span>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={uploading}
              className="flex-1 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              style={{
                background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)'
              }}
            >
              {uploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <>
                  <CloudArrowUpIcon className="w-5 h-5" />
                  <span>Upload Wallpaper</span>
                </>
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Wallpaper View Modal Component
const WallpaperViewModal: React.FC<{
  isOpen: boolean;
  wallpaper: Wallpaper;
  onClose: () => void;
}> = ({ isOpen, wallpaper, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">View Wallpaper</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Image Preview */}
          <div className="space-y-4">
            <div className="aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center">
              <PhotoIcon className="w-16 h-16 text-primary-300" />
            </div>

            {/* Resolution Options */}
            <div className="flex space-x-2">
              <div className="flex-1 bg-gray-50 p-3 rounded-lg text-center">
                <ComputerDesktopIcon className="w-6 h-6 mx-auto mb-1 text-gray-600" />
                <p className="text-xs text-gray-600">Desktop</p>
                <p className="text-xs font-medium">{wallpaper.resolutions.desktop.width}x{wallpaper.resolutions.desktop.height}</p>
              </div>
              <div className="flex-1 bg-gray-50 p-3 rounded-lg text-center">
                <DeviceTabletIcon className="w-6 h-6 mx-auto mb-1 text-gray-600" />
                <p className="text-xs text-gray-600">Tablet</p>
                <p className="text-xs font-medium">{wallpaper.resolutions.tablet.width}x{wallpaper.resolutions.tablet.height}</p>
              </div>
              <div className="flex-1 bg-gray-50 p-3 rounded-lg text-center">
                <DevicePhoneMobileIcon className="w-6 h-6 mx-auto mb-1 text-gray-600" />
                <p className="text-xs text-gray-600">Mobile</p>
                <p className="text-xs font-medium">{wallpaper.resolutions.mobile.width}x{wallpaper.resolutions.mobile.height}</p>
              </div>
            </div>
          </div>

          {/* Details */}
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">{wallpaper.title}</h4>
              <p className="text-gray-600">{wallpaper.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700">Category</p>
                <p className="text-sm text-gray-600">{wallpaper.category}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Price</p>
                <p className="text-sm text-gray-600">${wallpaper.price}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Downloads</p>
                <p className="text-sm text-gray-600">{wallpaper.downloads.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Featured</p>
                <p className="text-sm text-gray-600">{wallpaper.featured ? 'Yes' : 'No'}</p>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-700 mb-2">Tags</p>
              <div className="flex flex-wrap gap-1">
                {wallpaper.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-xs text-gray-500">
              <div>
                <p>Created: {new Date(wallpaper.createdAt).toLocaleDateString()}</p>
              </div>
              <div>
                <p>Updated: {new Date(wallpaper.updatedAt).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

// Wallpaper Edit Modal Component
const WallpaperEditModal: React.FC<{
  isOpen: boolean;
  wallpaper: Wallpaper;
  onClose: () => void;
  onUpdate: (wallpaper: Wallpaper) => void;
}> = ({ isOpen, wallpaper, onClose, onUpdate }) => {
  const [formData, setFormData] = useState({
    title: wallpaper.title,
    description: wallpaper.description,
    category: wallpaper.category,
    tags: wallpaper.tags.join(', '),
    price: wallpaper.price.toString(),
    originalPrice: wallpaper.originalPrice?.toString() || '',
    featured: wallpaper.featured
  });
  const [updating, setUpdating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUpdating(true);

    try {
      // Simulate update
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedWallpaper: Wallpaper = {
        ...wallpaper,
        title: formData.title,
        description: formData.description,
        category: formData.category,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        price: parseFloat(formData.price) || 0,
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        featured: formData.featured,
        updatedAt: new Date().toISOString()
      };

      onUpdate(updatedWallpaper);
    } catch (error) {
      console.error('Error updating wallpaper:', error);
    } finally {
      setUpdating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Edit Wallpaper</h3>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
              <select
                required
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="Abstract">Abstract</option>
                <option value="Nature">Nature</option>
                <option value="Geometric">Geometric</option>
                <option value="Minimal">Minimal</option>
                <option value="Dark">Dark</option>
                <option value="Light">Light</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
            <textarea
              required
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter tags separated by commas"
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price ($) *</label>
              <input
                type="number"
                step="0.01"
                required
                value={formData.price}
                onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Original Price ($)</label>
              <input
                type="number"
                step="0.01"
                value={formData.originalPrice}
                onChange={(e) => setFormData(prev => ({ ...prev, originalPrice: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Featured */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="editFeatured"
              checked={formData.featured}
              onChange={(e) => setFormData(prev => ({ ...prev, featured: e.target.checked }))}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="editFeatured" className="ml-2 block text-sm text-gray-700">
              Mark as featured wallpaper
            </label>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={updating}
              className="flex-1 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              style={{
                background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)'
              }}
            >
              {updating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Updating...</span>
                </>
              ) : (
                <>
                  <CheckCircleIcon className="w-5 h-5" />
                  <span>Update Wallpaper</span>
                </>
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WallpapersManagement;
