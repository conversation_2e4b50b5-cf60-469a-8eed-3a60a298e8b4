'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedTextProps {
  text: string;
  variant?: 'fadeIn' | 'slideUp' | 'typewriter' | 'wave' | 'blur' | 'scale';
  delay?: number;
  duration?: number;
  staggerDelay?: number;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  variant = 'fadeIn',
  delay = 0,
  duration = 0.6,
  staggerDelay = 0.05,
  className = '',
  as: Component = 'div',
}) => {
  const words = text.split(' ');
  const letters = text.split('');

  const getContainerVariants = () => {
    return {
      hidden: {},
      visible: {
        transition: {
          staggerChildren: staggerDelay,
          delayChildren: delay,
        },
      },
    };
  };

  const getItemVariants = () => {
    switch (variant) {
      case 'fadeIn':
        return {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: { duration },
          },
        };

      case 'slideUp':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: {
            opacity: 1,
            y: 0,
            transition: { duration },
          },
        };

      case 'typewriter':
        return {
          hidden: { opacity: 0, width: 0 },
          visible: {
            opacity: 1,
            width: 'auto',
            transition: { duration: duration * 0.5 },
          },
        };

      case 'wave':
        return {
          hidden: { opacity: 0, y: 20 },
          visible: {
            opacity: 1,
            y: [20, -5, 0],
            transition: {
              duration,
              ease: 'easeOut',
            },
          },
        };

      case 'blur':
        return {
          hidden: { opacity: 0, filter: 'blur(10px)' },
          visible: {
            opacity: 1,
            filter: 'blur(0px)',
            transition: { duration },
          },
        };

      case 'scale':
        return {
          hidden: { opacity: 0, scale: 0.8 },
          visible: {
            opacity: 1,
            scale: 1,
            transition: { duration },
          },
        };

      default:
        return {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: { duration },
          },
        };
    }
  };

  const renderByWords = () => (
    <motion.div
      variants={getContainerVariants()}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      className={className}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={getItemVariants()}
          className="inline-block mr-2"
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  );

  const renderByLetters = () => (
    <motion.div
      variants={getContainerVariants()}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      className={className}
    >
      {letters.map((letter, index) => (
        <motion.span
          key={index}
          variants={getItemVariants()}
          className="inline-block"
        >
          {letter === ' ' ? '\u00A0' : letter}
        </motion.span>
      ))}
    </motion.div>
  );

  const renderTypewriter = () => (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      className={`overflow-hidden ${className}`}
    >
      <motion.span
        variants={{
          hidden: { width: 0 },
          visible: {
            width: '100%',
            transition: {
              duration: duration * text.length * 0.05,
              ease: 'linear',
              delay,
            },
          },
        }}
        className="inline-block whitespace-nowrap"
      >
        {text}
      </motion.span>
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{
          duration: 0.8,
          repeat: Infinity,
          repeatType: 'reverse',
          delay: delay + duration * text.length * 0.05,
        }}
        className="inline-block w-0.5 h-6 bg-current ml-1"
      />
    </motion.div>
  );

  if (variant === 'typewriter') {
    return React.createElement(Component, {}, renderTypewriter());
  }

  if (variant === 'wave' || variant === 'blur') {
    return React.createElement(Component, {}, renderByLetters());
  }

  return React.createElement(Component, {}, renderByWords());
};

// Gradient text animation component
interface GradientTextProps {
  text: string;
  gradient?: string;
  animationType?: 'wave' | 'shimmer' | 'pulse';
  className?: string;
  as?: AnimatedTextProps['as'];
}

export const GradientText: React.FC<GradientTextProps> = ({
  text,
  gradient = 'from-primary-600 to-secondary-600',
  animationType = 'shimmer',
  className = '',
  as: Component = 'span',
}) => {
  const getAnimationStyles = () => {
    switch (animationType) {
      case 'wave':
        return {
          backgroundSize: '200% 200%',
          animation: 'gradient-wave 3s ease-in-out infinite',
        };
      case 'shimmer':
        return {
          backgroundSize: '200% 200%',
          animation: 'gradient-shimmer 2s linear infinite',
        };
      case 'pulse':
        return {
          animation: 'gradient-pulse 2s ease-in-out infinite',
        };
      default:
        return {};
    }
  };

  return React.createElement(
    Component,
    {
      className: `bg-gradient-to-r ${gradient} bg-clip-text text-transparent ${className}`,
      style: getAnimationStyles(),
    },
    text
  );
};

export default AnimatedText;
