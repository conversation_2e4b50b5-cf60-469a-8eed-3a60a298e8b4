"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftEllipsisIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChatBubbleLeftEllipsisIcon,ChatBubbleLeftRightIcon,CheckCircleIcon,ClipboardDocumentListIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// My Creative Process\nconst processSteps = [\n    {\n        id: 1,\n        title: \"Consultation\",\n        description: \"Understanding your vision, requirements, and goals through detailed discussion and planning.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\",\n        step: \"01\"\n    },\n    {\n        id: 2,\n        title: \"Planning\",\n        description: \"Creating a strategic roadmap, timeline, and detailed project plan tailored to your needs.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\",\n        step: \"02\"\n    },\n    {\n        id: 3,\n        title: \"Creation\",\n        description: \"Bringing your ideas to life with professional design, development, and creative execution.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"from-yellow-500 to-orange-500\",\n        bgColor: \"bg-yellow-50\",\n        borderColor: \"border-yellow-200\",\n        step: \"03\"\n    },\n    {\n        id: 4,\n        title: \"Delivery\",\n        description: \"Final review, refinements, and seamless handover of your completed project.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\",\n        step: \"04\"\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6\n        }\n    }\n};\nconst ProcessPreview = ()=>{\n    const openWhatsApp = ()=>{\n        const message = encodeURIComponent(\"Hi Elias! \\uD83D\\uDC4B\\n\\nI'm interested in working with you. Let's discuss my project requirements and your creative process.\\n\\nThank you!\");\n        const whatsappUrl = \"https://wa.me/254703973225?text=\".concat(message);\n        window.open(whatsappUrl, \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"My Creative \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        backgroundClip: \"text\"\n                                    },\n                                    children: \"Process\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                            children: \"A streamlined approach to bringing your creative vision to life. From initial consultation to final delivery, every step is designed for excellence.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                            onClick: openWhatsApp,\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Start Your Project\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    children: processSteps.map((step, index)=>{\n                        const IconComponent = step.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            variants: itemVariants,\n                            className: \"\".concat(step.bgColor, \" \").concat(step.borderColor, \" border-2 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer relative overflow-hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-4 right-4 text-2xl font-bold text-gray-300 group-hover:text-gray-400 transition-colors duration-300\",\n                                    children: step.step\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r \".concat(step.color, \" p-3 rounded-lg mb-4 w-fit\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                    children: step.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm leading-relaxed\",\n                                    children: step.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, undefined),\n                                index < processSteps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gradient-to-r from-gray-300 to-transparent transform -translate-y-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm font-medium text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Step \",\n                                                    step.step\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, step.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-200 max-w-2xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"Ready to Start Your Project?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Let's discuss your vision and how my creative process can bring it to life. Every great project starts with a conversation.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/services\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"bg-white border-2 border-gray-300 text-gray-700 hover:border-purple-500 hover:text-purple-600 px-8 py-3 rounded-full font-semibold transition-all duration-300 flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"View My Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        onClick: openWhatsApp,\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        className: \"bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChatBubbleLeftEllipsisIcon_ChatBubbleLeftRightIcon_CheckCircleIcon_ClipboardDocumentListIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Let's Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\ServicesPreview.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProcessPreview;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProcessPreview);\nvar _c;\n$RefreshReg$(_c, \"ProcessPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});