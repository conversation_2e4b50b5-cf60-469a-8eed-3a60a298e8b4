"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!******************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,PaintBrushIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,PaintBrushIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,PaintBrushIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CameraIcon,PaintBrushIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Typing Animation Component\nconst TypingAnimation = ()=>{\n    _s();\n    const [displayText, setDisplayText] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    const [currentIndex, setCurrentIndex] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [isDeleting, setIsDeleting] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const [typingTexts, setTypingTexts] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([\n        \"Elias Mwangi\",\n        \"Web Developer\"\n    ]);\n    // Load typing texts from localStorage (admin configurable)\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const savedTexts = localStorage.getItem(\"heroTypingTexts\");\n        if (savedTexts) {\n            try {\n                const parsed = JSON.parse(savedTexts);\n                if (Array.isArray(parsed) && parsed.length > 0) {\n                    setTypingTexts(parsed);\n                }\n            } catch (error) {\n                console.log(\"Using default typing texts\");\n            }\n        }\n    }, []);\n    // Start typing immediately\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (displayText === \"\" && typingTexts.length > 0) {\n            const timer = setTimeout(()=>{\n                setDisplayText(typingTexts[0].charAt(0));\n            }, 500);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        typingTexts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const currentText = typingTexts[currentIndex];\n        const timeout = setTimeout(()=>{\n            if (!isDeleting) {\n                // Typing\n                if (displayText.length < currentText.length) {\n                    setDisplayText(currentText.slice(0, displayText.length + 1));\n                } else {\n                    // Finished typing, wait then start deleting\n                    setTimeout(()=>setIsDeleting(true), 2000);\n                }\n            } else {\n                // Deleting\n                if (displayText.length > 0) {\n                    setDisplayText(currentText.slice(0, displayText.length - 1));\n                } else {\n                    // Finished deleting, move to next text\n                    setIsDeleting(false);\n                    setCurrentIndex((prev)=>(prev + 1) % typingTexts.length);\n                }\n            }\n        }, isDeleting ? 50 : 100);\n        return ()=>clearTimeout(timeout);\n    }, [\n        displayText,\n        isDeleting,\n        currentIndex,\n        typingTexts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"relative inline-block\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-block min-w-0 font-bold\",\n                style: {\n                    background: \"linear-gradient(135deg, #a855f7, #eab308)\",\n                    WebkitBackgroundClip: \"text\",\n                    WebkitTextFillColor: \"transparent\",\n                    backgroundClip: \"text\",\n                    color: \"#a855f7\" // Fallback color\n                },\n                children: displayText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"animate-pulse text-purple-600 ml-1\",\n                children: \"|\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TypingAnimation, \"RmcxHsl48Bn0hzLEMysdUpVzinY=\");\n_c = TypingAnimation;\nconst HeroTest = ()=>{\n    _s1();\n    const [profileImage, setProfileImage] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    // Load profile image from localStorage\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const savedImage = localStorage.getItem(\"profileImage\");\n        if (savedImage) {\n            setProfileImage(savedImage);\n        }\n    }, []);\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        animate: {\n                            y: [\n                                -10,\n                                10,\n                                -10\n                            ],\n                            rotate: [\n                                0,\n                                5,\n                                -5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 left-10 w-20 h-20 bg-purple-200 rounded-full opacity-60\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        animate: {\n                            y: [\n                                10,\n                                -10,\n                                10\n                            ],\n                            rotate: [\n                                0,\n                                -5,\n                                5,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-40 right-20 w-32 h-32 bg-yellow-200 rounded-full opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        animate: {\n                            y: [\n                                -15,\n                                15,\n                                -15\n                            ],\n                            rotate: [\n                                0,\n                                10,\n                                -10,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 7,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-20 left-20 w-24 h-24 bg-primary-200 rounded-full opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    className: \"grid grid-cols-1 lg:grid-cols-5 gap-8 items-center min-h-[80vh]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            className: \"lg:col-span-3 text-center lg:text-left order-1 lg:order-1 lg:pr-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg mb-8 lg:mx-0 mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-600 font-semibold text-sm\",\n                                            children: \"Creative Professional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                    variants: itemVariants,\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block\",\n                                            children: \"Hi, I'm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-block whitespace-nowrap min-w-[280px] md:min-w-[380px] lg:min-w-[480px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TypingAnimation, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                    variants: itemVariants,\n                                    className: \"text-lg md:text-xl text-gray-600 mb-8 max-w-2xl lg:max-w-none leading-relaxed\",\n                                    children: \"I bring ideas to life through visually appealing and functional designs that capture attention and tell compelling stories.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"flex flex-wrap justify-center lg:justify-start gap-4 mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Graphic Design\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Photography\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 text-yellow-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Video Editing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/portfolio\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"group bg-purple-yellow-gradient text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"View My Work\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/contact\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"bg-white/80 backdrop-blur-sm text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-primary-300\",\n                                                children: \"Get In Touch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    variants: itemVariants,\n                                    className: \"mt-16 grid grid-cols-2 gap-6 max-w-md lg:max-w-none\",\n                                    children: [\n                                        {\n                                            number: \"50+\",\n                                            label: \"Projects Completed\"\n                                        },\n                                        {\n                                            number: \"5+\",\n                                            label: \"Years Experience\"\n                                        },\n                                        {\n                                            number: \"4\",\n                                            label: \"Specializations\"\n                                        },\n                                        {\n                                            number: \"100%\",\n                                            label: \"Client Satisfaction\"\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            variants: itemVariants,\n                                            className: \"text-center lg:text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl md:text-3xl font-bold bg-purple-yellow-gradient bg-clip-text text-transparent\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: itemVariants,\n                            className: \"lg:col-span-2 flex justify-center lg:justify-end order-2 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        className: \"w-64 h-64 md:w-80 md:h-80 lg:w-72 lg:h-72 rounded-full overflow-hidden shadow-2xl border-8 border-white/20 backdrop-blur-sm bg-gradient-to-br from-primary-100 to-secondary-100\",\n                                        children: profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: profileImage,\n                                            alt: \"Elias Mwangi - Creative Professional\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-200 to-secondary-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl md:text-8xl font-bold text-primary-600 mb-4\",\n                                                        children: \"EM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-primary-700 font-medium\",\n                                                        children: \"Your Photo Here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        animate: {\n                                            rotate: [\n                                                0,\n                                                360\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 20,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        className: \"absolute -top-4 -right-4 w-20 h-20 border-4 border-yellow-400 rounded-full opacity-60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        animate: {\n                                            rotate: [\n                                                360,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 15,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        className: \"absolute -bottom-6 -left-6 w-16 h-16 border-4 border-purple-400 rounded-full opacity-60\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                -10,\n                                                10,\n                                                -10\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 3,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\"\n                                        },\n                                        className: \"absolute top-8 -left-6 bg-white/90 backdrop-blur-sm p-2.5 rounded-full shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                10,\n                                                -10,\n                                                10\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 4,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\",\n                                            delay: 1\n                                        },\n                                        className: \"absolute top-16 -right-8 bg-white/90 backdrop-blur-sm p-2.5 rounded-full shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        animate: {\n                                            y: [\n                                                -8,\n                                                8,\n                                                -8\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 3.5,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\",\n                                            delay: 2\n                                        },\n                                        className: \"absolute bottom-12 -right-6 bg-white/90 backdrop-blur-sm p-2.5 rounded-full shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CameraIcon_PaintBrushIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                animate: {\n                    y: [\n                        0,\n                        10,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 2,\n                    repeat: Infinity\n                },\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-3 bg-gray-400 rounded-full mt-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\HeroTest.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(HeroTest, \"OmAiYdzRGmYMpNP5dhzwPE827fA=\");\n_c1 = HeroTest;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HeroTest);\nvar _c, _c1;\n$RefreshReg$(_c, \"TypingAnimation\");\n$RefreshReg$(_c1, \"HeroTest\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});