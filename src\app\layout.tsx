import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
// import { AuthProvider } from '@/contexts/AuthContext'
import { CartProvider } from '@/contexts/CartContext'
import { ServicesProvider } from '@/contexts/ServicesContext'
import LayoutWrapper from '@/components/layout/LayoutWrapper'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '<PERSON> - Creative Professional | Portfolio & Shop',
  description: 'Creative professional specializing in graphic design, web design, photography, and video editing. Bringing ideas to life through visually appealing and functional designs.',
  keywords: ['graphic design', 'web design', 'photography', 'video editing', 'creative professional', '<PERSON>', 'portfolio'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {/* <AuthProvider> */}
          <CartProvider>
            <ServicesProvider>
              <LayoutWrapper>
                {children}
              </LayoutWrapper>
            </ServicesProvider>
          </CartProvider>
        {/* </AuthProvider> */}
      </body>
    </html>
  )
}
