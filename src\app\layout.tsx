import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
// import { AuthProvider } from '@/contexts/AuthContext'
import { CartProvider } from '@/contexts/CartContext'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'EliShop - Portfolio & Store',
  description: 'Modern portfolio website with integrated shop featuring stunning animations and transitions',
  keywords: ['portfolio', 'shop', 'ecommerce', 'modern', 'animations'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {/* <AuthProvider> */}
          <CartProvider>
            <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
              <Header />
              <main className="min-h-screen">
                {children}
              </main>
              <Footer />
            </div>
          </CartProvider>
        {/* </AuthProvider> */}
      </body>
    </html>
  )
}
