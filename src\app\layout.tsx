import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'EliShop - Portfolio & Store',
  description: 'Modern portfolio website with integrated shop featuring stunning animations and transitions',
  keywords: ['portfolio', 'shop', 'ecommerce', 'modern', 'animations'],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
          {children}
        </div>
      </body>
    </html>
  )
}
