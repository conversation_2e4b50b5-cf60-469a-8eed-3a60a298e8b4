'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface FloatingElementProps {
  children: React.ReactNode;
  intensity?: 'subtle' | 'normal' | 'strong';
  direction?: 'vertical' | 'horizontal' | 'circular' | 'random';
  duration?: number;
  delay?: number;
  className?: string;
}

const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  intensity = 'normal',
  direction = 'vertical',
  duration = 3,
  delay = 0,
  className = '',
}) => {
  const getIntensityValues = () => {
    switch (intensity) {
      case 'subtle':
        return { distance: 5, rotation: 2 };
      case 'normal':
        return { distance: 10, rotation: 5 };
      case 'strong':
        return { distance: 20, rotation: 10 };
      default:
        return { distance: 10, rotation: 5 };
    }
  };

  const { distance, rotation } = getIntensityValues();

  const getAnimationVariants = () => {
    switch (direction) {
      case 'vertical':
        return {
          animate: {
            y: [-distance, distance, -distance],
            transition: {
              duration,
              repeat: Infinity,
              ease: 'easeInOut',
              delay,
            },
          },
        };
      case 'horizontal':
        return {
          animate: {
            x: [-distance, distance, -distance],
            transition: {
              duration,
              repeat: Infinity,
              ease: 'easeInOut',
              delay,
            },
          },
        };
      case 'circular':
        return {
          animate: {
            x: [0, distance, 0, -distance, 0],
            y: [-distance, 0, distance, 0, -distance],
            transition: {
              duration: duration * 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay,
            },
          },
        };
      case 'random':
        return {
          animate: {
            x: [-distance, distance, -distance / 2, distance / 2, 0],
            y: [distance / 2, -distance, distance, -distance / 2, 0],
            rotate: [-rotation, rotation, -rotation / 2, rotation / 2, 0],
            transition: {
              duration: duration * 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
              delay,
            },
          },
        };
      default:
        return {
          animate: {
            y: [-distance, distance, -distance],
            transition: {
              duration,
              repeat: Infinity,
              ease: 'easeInOut',
              delay,
            },
          },
        };
    }
  };

  return (
    <motion.div
      {...getAnimationVariants()}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default FloatingElement;
