'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DownloadIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Product, Resolution, DownloadLink } from '@/types/product';

interface DownloadManagerProps {
  product: Product;
  purchasedResolutions?: Resolution[];
  onDownload?: (resolution: Resolution) => Promise<string>; // Returns download URL
}

interface DownloadStatus {
  resolutionId: string;
  status: 'idle' | 'downloading' | 'completed' | 'error';
  progress?: number;
  downloadUrl?: string;
  error?: string;
}

const DownloadManager: React.FC<DownloadManagerProps> = ({
  product,
  purchasedResolutions = [],
  onDownload,
}) => {
  const [downloadStatuses, setDownloadStatuses] = useState<Record<string, DownloadStatus>>({});

  const handleDownload = async (resolution: Resolution) => {
    const statusKey = `${product.id}-${resolution.label}`;
    
    setDownloadStatuses(prev => ({
      ...prev,
      [statusKey]: { resolutionId: resolution.label, status: 'downloading', progress: 0 }
    }));

    try {
      // Simulate download progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setDownloadStatuses(prev => ({
          ...prev,
          [statusKey]: { ...prev[statusKey], progress }
        }));
      }

      // Get download URL
      const downloadUrl = onDownload ? await onDownload(resolution) : resolution.downloadUrl;
      
      setDownloadStatuses(prev => ({
        ...prev,
        [statusKey]: { 
          resolutionId: resolution.label, 
          status: 'completed', 
          downloadUrl,
          progress: 100 
        }
      }));

      // Trigger actual download
      if (downloadUrl) {
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `${product.title}-${resolution.label}.${product.fileFormat?.[0]?.toLowerCase() || 'jpg'}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

    } catch (error) {
      setDownloadStatuses(prev => ({
        ...prev,
        [statusKey]: { 
          resolutionId: resolution.label, 
          status: 'error', 
          error: 'Download failed. Please try again.' 
        }
      }));
    }
  };

  const getStatusIcon = (status: DownloadStatus) => {
    switch (status.status) {
      case 'downloading':
        return <ClockIcon className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
      default:
        return <DownloadIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: DownloadStatus) => {
    switch (status.status) {
      case 'downloading':
        return `Downloading... ${status.progress}%`;
      case 'completed':
        return 'Downloaded';
      case 'error':
        return status.error || 'Error';
      default:
        return 'Download';
    }
  };

  if (!product.wallpaperData?.resolutions) {
    return null;
  }

  const availableResolutions = purchasedResolutions.length > 0 
    ? purchasedResolutions 
    : product.wallpaperData.resolutions;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center space-x-3 mb-6">
        <DocumentArrowDownIcon className="w-6 h-6 text-primary-600" />
        <h3 className="text-lg font-semibold text-gray-900">Download Files</h3>
      </div>

      <div className="space-y-4">
        {availableResolutions.map((resolution) => {
          const statusKey = `${product.id}-${resolution.label}`;
          const status = downloadStatuses[statusKey];

          return (
            <motion.div
              key={resolution.label}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center">
                      <span className="text-sm font-bold text-primary-700">
                        {resolution.label}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {resolution.width} × {resolution.height}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {resolution.fileSize} • {product.fileFormat?.join(', ')}
                      </p>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  {status?.status === 'downloading' && (
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <motion.div
                          className="bg-primary-600 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${status.progress}%` }}
                          transition={{ duration: 0.3 }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-3">
                  {status && (
                    <div className="flex items-center space-x-2 text-sm">
                      {getStatusIcon(status)}
                      <span className={`font-medium ${
                        status.status === 'completed' ? 'text-green-600' :
                        status.status === 'error' ? 'text-red-600' :
                        status.status === 'downloading' ? 'text-blue-600' :
                        'text-gray-600'
                      }`}>
                        {getStatusText(status)}
                      </span>
                    </div>
                  )}

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleDownload(resolution)}
                    disabled={status?.status === 'downloading'}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
                      status?.status === 'completed'
                        ? 'bg-green-100 text-green-700 hover:bg-green-200'
                        : status?.status === 'downloading'
                        ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                        : status?.status === 'error'
                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                        : 'bg-primary-600 text-white hover:bg-primary-700 hover:shadow-lg'
                    }`}
                  >
                    {getStatusIcon(status || { resolutionId: resolution.label, status: 'idle' })}
                    <span>
                      {status?.status === 'completed' ? 'Download Again' :
                       status?.status === 'error' ? 'Retry' :
                       status?.status === 'downloading' ? 'Downloading...' :
                       'Download'}
                    </span>
                  </motion.button>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Download Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Download Instructions</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Files will be downloaded to your default download folder</li>
          <li>• Downloads are available for 30 days after purchase</li>
          <li>• You can re-download files anytime from your account</li>
          <li>• For support, contact <NAME_EMAIL></li>
        </ul>
      </div>

      {/* Usage Rights */}
      <div className="mt-4 p-4 bg-green-50 rounded-lg">
        <h4 className="font-medium text-green-900 mb-2">Usage Rights</h4>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• Personal and commercial use allowed</li>
          <li>• No attribution required</li>
          <li>• Cannot be resold or redistributed</li>
          <li>• Unlimited projects and clients</li>
        </ul>
      </div>
    </div>
  );
};

export default DownloadManager;

// Download History Component
interface DownloadHistoryProps {
  downloads: DownloadLink[];
  onRedownload?: (download: DownloadLink) => void;
}

export const DownloadHistory: React.FC<DownloadHistoryProps> = ({
  downloads,
  onRedownload,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Download History</h3>
      
      {downloads.length === 0 ? (
        <div className="text-center py-8">
          <DocumentArrowDownIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No downloads yet</p>
        </div>
      ) : (
        <div className="space-y-4">
          {downloads.map((download) => {
            const isExpired = new Date(download.expiresAt) < new Date();
            const canDownload = download.downloadCount < download.maxDownloads && !isExpired;

            return (
              <div
                key={download.productId}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
              >
                <div>
                  <h4 className="font-medium text-gray-900">Product #{download.productId}</h4>
                  <p className="text-sm text-gray-600">
                    Downloaded {download.downloadCount}/{download.maxDownloads} times
                  </p>
                  <p className="text-xs text-gray-500">
                    Expires: {new Date(download.expiresAt).toLocaleDateString()}
                  </p>
                </div>
                
                <button
                  onClick={() => onRedownload?.(download)}
                  disabled={!canDownload}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                    canDownload
                      ? 'bg-primary-600 text-white hover:bg-primary-700'
                      : 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {isExpired ? 'Expired' : canDownload ? 'Download' : 'Limit Reached'}
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
