'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useServices, Service } from '@/contexts/ServicesContext';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PaintBrushIcon,
  ComputerDesktopIcon,
  CameraIcon,
  VideoCameraIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

// Service interface is now imported from ServicesContext

const iconOptions = [
  { value: 'PaintBrushIcon', label: 'Paint Brush', component: PaintBrushIcon },
  { value: 'ComputerDesktopIcon', label: 'Computer Desktop', component: ComputerDesktopIcon },
  { value: 'CameraIcon', label: 'Camera', component: CameraIcon },
  { value: 'VideoCameraIcon', label: 'Video Camera', component: VideoCameraIcon },
];

const colorOptions = [
  { value: 'from-purple-500 to-purple-600', label: 'Purple', bg: 'bg-purple-50', border: 'border-purple-200' },
  { value: 'from-blue-500 to-blue-600', label: 'Blue', bg: 'bg-blue-50', border: 'border-blue-200' },
  { value: 'from-green-500 to-green-600', label: 'Green', bg: 'bg-green-50', border: 'border-green-200' },
  { value: 'from-red-500 to-red-600', label: 'Red', bg: 'bg-red-50', border: 'border-red-200' },
  { value: 'from-yellow-500 to-yellow-600', label: 'Yellow', bg: 'bg-yellow-50', border: 'border-yellow-200' },
  { value: 'from-indigo-500 to-indigo-600', label: 'Indigo', bg: 'bg-indigo-50', border: 'border-indigo-200' },
];

// Default services are now managed in ServicesContext

const ServicesAdmin: React.FC = () => {
  const {
    services,
    addService,
    updateService,
    deleteService,
    toggleServiceActive,
    loading
  } = useServices();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [formData, setFormData] = useState<Partial<Service>>({
    title: '',
    description: '',
    features: [''],
    tools: [''],
    icon: 'PaintBrushIcon',
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    isActive: true
  });

  const handleOpenModal = (service?: Service) => {
    if (service) {
      setEditingService(service);
      setFormData(service);
    } else {
      setEditingService(null);
      setFormData({
        title: '',
        description: '',
        features: [''],
        tools: [''],
        icon: 'PaintBrushIcon',
        color: 'from-purple-500 to-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
        isActive: true
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingService(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const serviceData = {
      title: formData.title || '',
      description: formData.description || '',
      features: formData.features || [],
      tools: formData.tools || [],
      icon: formData.icon || 'PaintBrushIcon',
      color: formData.color || 'from-purple-500 to-purple-600',
      bgColor: formData.bgColor || 'bg-purple-50',
      borderColor: formData.borderColor || 'border-purple-200',
      isActive: formData.isActive ?? true,
    };

    if (editingService) {
      updateService(editingService.id, serviceData);
    } else {
      addService(serviceData);
    }

    handleCloseModal();
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      deleteService(id);
    }
  };

  const handleToggleActive = (id: string) => {
    toggleServiceActive(id);
  };

  const addFeature = () => {
    setFormData({
      ...formData,
      features: [...(formData.features || []), '']
    });
  };

  const removeFeature = (index: number) => {
    setFormData({
      ...formData,
      features: formData.features?.filter((_, i) => i !== index) || []
    });
  };

  const updateFeature = (index: number, value: string) => {
    const newFeatures = [...(formData.features || [])];
    newFeatures[index] = value;
    setFormData({
      ...formData,
      features: newFeatures
    });
  };

  const addTool = () => {
    setFormData({
      ...formData,
      tools: [...(formData.tools || []), '']
    });
  };

  const removeTool = (index: number) => {
    setFormData({
      ...formData,
      tools: formData.tools?.filter((_, i) => i !== index) || []
    });
  };

  const updateTool = (index: number, value: string) => {
    const newTools = [...(formData.tools || [])];
    newTools[index] = value;
    setFormData({
      ...formData,
      tools: newTools
    });
  };

  const handleColorChange = (colorOption: typeof colorOptions[0]) => {
    setFormData({
      ...formData,
      color: colorOption.value,
      bgColor: colorOption.bg,
      borderColor: colorOption.border
    });
  };

  const getIconComponent = (iconName: string) => {
    const iconOption = iconOptions.find(option => option.value === iconName);
    return iconOption?.component || PaintBrushIcon;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Services Management</h1>
          <p className="text-gray-600">Manage your creative services and offerings</p>
        </div>
        <motion.button
          onClick={() => handleOpenModal()}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-gradient-to-r from-purple-500 to-yellow-500 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2"
        >
          <PlusIcon className="w-5 h-5" />
          <span>Add Service</span>
        </motion.button>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => {
          const IconComponent = getIconComponent(service.icon);
          return (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`${service.bgColor} ${service.borderColor} border-2 rounded-xl p-6 relative group`}
            >
              {/* Status Badge */}
              <div className="absolute top-4 right-4">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  service.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {service.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              {/* Service Icon */}
              <div className={`bg-gradient-to-r ${service.color} p-3 rounded-lg mb-4 w-fit`}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>

              {/* Service Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-2">{service.title}</h3>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{service.description}</p>

              {/* Features Preview */}
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Features ({service.features.length})</p>
                <div className="space-y-1">
                  {service.features.slice(0, 3).map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckIcon className="w-3 h-3 text-green-500 flex-shrink-0" />
                      <span className="text-xs text-gray-600 truncate">{feature}</span>
                    </div>
                  ))}
                  {service.features.length > 3 && (
                    <p className="text-xs text-gray-500">+{service.features.length - 3} more</p>
                  )}
                </div>
              </div>

              {/* Tools Preview */}
              <div className="mb-6">
                <p className="text-sm font-medium text-gray-700 mb-2">Tools</p>
                <div className="flex flex-wrap gap-1">
                  {service.tools.slice(0, 3).map((tool, index) => (
                    <span
                      key={index}
                      className="bg-white px-2 py-1 rounded text-xs font-medium text-gray-700 border border-gray-200"
                    >
                      {tool}
                    </span>
                  ))}
                  {service.tools.length > 3 && (
                    <span className="text-xs text-gray-500">+{service.tools.length - 3}</span>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <motion.button
                    onClick={() => handleOpenModal(service)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors duration-200"
                    title="Edit"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    onClick={() => handleToggleActive(service.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`p-2 rounded-lg transition-colors duration-200 ${
                      service.isActive 
                        ? 'text-red-600 hover:bg-red-100' 
                        : 'text-green-600 hover:bg-green-100'
                    }`}
                    title={service.isActive ? 'Deactivate' : 'Activate'}
                  >
                    <EyeIcon className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    onClick={() => handleDelete(service.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                    title="Delete"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </motion.button>
                </div>
                <div className="text-xs text-gray-500">
                  Updated {new Date(service.updatedAt).toLocaleDateString()}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Add/Edit Service Modal */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={handleCloseModal}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            >
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-purple-500 to-yellow-500 text-white p-6 rounded-t-2xl">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">
                    {editingService ? 'Edit Service' : 'Add New Service'}
                  </h2>
                  <button
                    onClick={handleCloseModal}
                    className="text-white hover:bg-white/20 p-2 rounded-full transition-colors duration-200"
                  >
                    <XMarkIcon className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Basic Info */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Service Title *
                      </label>
                      <input
                        type="text"
                        value={formData.title || ''}
                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="e.g., Graphic Design"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description *
                      </label>
                      <textarea
                        value={formData.description || ''}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                        placeholder="Brief description of the service..."
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Icon
                      </label>
                      <select
                        value={formData.icon || ''}
                        onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        {iconOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Color Theme
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {colorOptions.map((option) => (
                          <button
                            key={option.value}
                            type="button"
                            onClick={() => handleColorChange(option)}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                              formData.color === option.value
                                ? 'border-purple-500 ring-2 ring-purple-200'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                          >
                            <div className={`w-full h-4 bg-gradient-to-r ${option.value} rounded mb-1`} />
                            <span className="text-xs font-medium text-gray-700">{option.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={formData.isActive ?? true}
                        onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                      />
                      <label htmlFor="isActive" className="ml-2 text-sm font-medium text-gray-700">
                        Active Service
                      </label>
                    </div>
                  </div>

                  {/* Features and Tools */}
                  <div className="space-y-4">
                    {/* Features */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Features
                        </label>
                        <button
                          type="button"
                          onClick={addFeature}
                          className="text-purple-600 hover:text-purple-700 text-sm font-medium"
                        >
                          + Add Feature
                        </button>
                      </div>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {formData.features?.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={feature}
                              onChange={(e) => updateFeature(index, e.target.value)}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                              placeholder="Feature description..."
                            />
                            <button
                              type="button"
                              onClick={() => removeFeature(index)}
                              className="text-red-600 hover:text-red-700 p-1"
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tools */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Tools & Technologies
                        </label>
                        <button
                          type="button"
                          onClick={addTool}
                          className="text-purple-600 hover:text-purple-700 text-sm font-medium"
                        >
                          + Add Tool
                        </button>
                      </div>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {formData.tools?.map((tool, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={tool}
                              onChange={(e) => updateTool(index, e.target.value)}
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                              placeholder="Tool name..."
                            />
                            <button
                              type="button"
                              onClick={() => removeTool(index)}
                              className="text-red-600 hover:text-red-700 p-1"
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={handleCloseModal}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <motion.button
                    type="submit"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-purple-500 to-yellow-500 text-white px-6 py-2 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    {editingService ? 'Update Service' : 'Create Service'}
                  </motion.button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ServicesAdmin;
