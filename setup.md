# Setup Instructions for EliShop Portfolio Website

## Prerequisites

Before running this project, you need to have Node.js installed on your system.

### Installing Node.js

1. **Windows/Mac/Linux**:
   - Go to [https://nodejs.org/](https://nodejs.org/)
   - Download the LTS (Long Term Support) version
   - Run the installer and follow the instructions
   - Verify installation by opening terminal/command prompt and running:
     ```bash
     node --version
     npm --version
     ```

## Quick Start

1. **Open Terminal/Command Prompt** in the project directory (`elishop`)

2. **Install Dependencies**:
   ```bash
   npm install
   ```
   This will install all required packages including:
   - Next.js
   - React
   - Framer Motion
   - Tailwind CSS
   - TypeScript
   - And other dependencies

3. **Start Development Server**:
   ```bash
   npm run dev
   ```

4. **Open Your Browser**:
   - Navigate to `http://localhost:3000`
   - You should see your portfolio website!

## Troubleshooting

### If `npm install` fails:
- Try clearing npm cache: `npm cache clean --force`
- Delete `node_modules` folder and `package-lock.json` file, then run `npm install` again

### If the development server doesn't start:
- Make sure port 3000 is not being used by another application
- Try running on a different port: `npm run dev -- -p 3001`

### If you see TypeScript errors:
- The project includes TypeScript configuration
- Most editors will show inline errors and suggestions
- Run `npm run build` to check for build errors

## Project Features

✅ **Modern Design**: Purple and yellow color scheme
✅ **Smooth Animations**: Framer Motion powered transitions
✅ **Responsive Layout**: Works on all devices
✅ **Portfolio Section**: Showcase your work
✅ **Shop Integration**: E-commerce functionality
✅ **Contact Form**: Get in touch section
✅ **Performance Optimized**: Next.js optimization

## Next Steps

1. **Customize Content**:
   - Update portfolio items in `src/components/Portfolio.tsx`
   - Modify products in `src/components/Shop.tsx`
   - Change contact information in `src/components/Contact.tsx`

2. **Customize Design**:
   - Modify colors in `tailwind.config.ts`
   - Update animations in `src/lib/utils.ts`
   - Customize styles in `src/app/globals.css`

3. **Deploy**:
   - Build for production: `npm run build`
   - Deploy to Vercel, Netlify, or your preferred hosting platform

## Support

If you encounter any issues:
1. Check the console for error messages
2. Ensure all dependencies are installed correctly
3. Verify Node.js version is 18 or higher
4. Check that all files are in the correct locations

Happy coding! 🚀
