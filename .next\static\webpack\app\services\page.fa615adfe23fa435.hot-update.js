"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/services/page",{

/***/ "(app-pages-browser)/./src/app/services/page.tsx":
/*!***********************************!*\
  !*** ./src/app/services/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ServicesContext */ \"(app-pages-browser)/./src/contexts/ServicesContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,CheckIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,CheckIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,CheckIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,CheckIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,CheckIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,CheckIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst services = [\n    {\n        id: \"graphic-design\",\n        title: \"Graphic Design\",\n        icon: _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        description: \"Creating visually stunning designs that communicate your brand message effectively\",\n        features: [\n            \"Logo Design & Branding\",\n            \"Business Cards & Stationery\",\n            \"Brochures & Flyers\",\n            \"Social Media Graphics\",\n            \"Print Design\",\n            \"Brand Identity Development\"\n        ],\n        tools: [\n            \"Adobe Illustrator\",\n            \"Photoshop\",\n            \"InDesign\",\n            \"Figma\"\n        ],\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\"\n    },\n    {\n        id: \"web-design\",\n        title: \"Web Design\",\n        icon: _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n        features: [\n            \"Responsive Web Design\",\n            \"UI/UX Design\",\n            \"E-commerce Websites\",\n            \"Landing Pages\",\n            \"Web Applications\",\n            \"Website Maintenance\"\n        ],\n        tools: [\n            \"React\",\n            \"Next.js\",\n            \"Tailwind CSS\",\n            \"WordPress\"\n        ],\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\"\n    },\n    {\n        id: \"photography\",\n        title: \"Photography\",\n        icon: _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n        features: [\n            \"Product Photography\",\n            \"Portrait Photography\",\n            \"Event Photography\",\n            \"Commercial Photography\",\n            \"Photo Editing & Retouching\",\n            \"Studio & Location Shoots\"\n        ],\n        tools: [\n            \"Canon EOS\",\n            \"Lightroom\",\n            \"Photoshop\",\n            \"Studio Lighting\"\n        ],\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\"\n    },\n    {\n        id: \"video-editing\",\n        title: \"Video Editing\",\n        icon: _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        description: \"Creating engaging video content with professional editing and motion graphics\",\n        features: [\n            \"Video Production\",\n            \"Motion Graphics\",\n            \"Color Correction\",\n            \"Audio Enhancement\",\n            \"Social Media Videos\",\n            \"Commercial Videos\"\n        ],\n        tools: [\n            \"After Effects\",\n            \"Premiere Pro\",\n            \"DaVinci Resolve\",\n            \"Final Cut Pro\"\n        ],\n        color: \"from-red-500 to-red-600\",\n        bgColor: \"bg-red-50\",\n        borderColor: \"border-red-200\"\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6\n        }\n    }\n};\nconst ServicesPage = ()=>{\n    _s();\n    const { activeServices, loading } = (0,_contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_2__.useServices)();\n    const getIconComponent = (iconName)=>{\n        switch(iconName){\n            case \"PaintBrushIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"ComputerDesktopIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"CameraIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"VideoCameraIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            default:\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        }\n    };\n    const openWhatsApp = (serviceName)=>{\n        const message = encodeURIComponent(\"Hi Elias! \\uD83D\\uDC4B\\n\\nI'm interested in your \".concat(serviceName, \" services. Please let me know about pricing and availability.\\n\\nThank you!\"));\n        const whatsappUrl = \"https://wa.me/254703973225?text=\".concat(message);\n        window.open(whatsappUrl, \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-32 pb-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                children: [\n                                    \"Creative \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block\",\n                                        style: {\n                                            background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\",\n                                            WebkitBackgroundClip: \"text\",\n                                            WebkitTextFillColor: \"transparent\",\n                                            backgroundClip: \"text\"\n                                        },\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 24\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Professional creative services to bring your ideas to life. From design to development, photography to video editing - I've got you covered.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                onClick: ()=>openWhatsApp(\"general\"),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Get Started on WhatsApp\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"visible\",\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: services.map((service)=>{\n                            const IconComponent = service.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: itemVariants,\n                                className: \"\".concat(service.bgColor, \" \").concat(service.borderColor, \" border-2 rounded-2xl p-8 hover:shadow-xl transition-all duration-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r \".concat(service.color, \" p-3 rounded-xl\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: service.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: service.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                children: \"What I Offer:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n                                                children: service.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-5 h-5 text-green-500 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: feature\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                children: \"Tools & Technologies:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: service.tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-white px-3 py-1 rounded-full text-sm font-medium text-gray-700 border border-gray-200\",\n                                                        children: tool\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                        onClick: ()=>openWhatsApp(service.title),\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        className: \"w-full bg-gradient-to-r \".concat(service.color, \" text-white py-3 px-6 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Get Quote via WhatsApp\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, service.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 sm:px-6 lg:px-8 bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                                children: \"Ready to Start Your Project?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 mb-8\",\n                                children: \"Let's discuss your requirements and bring your vision to life. Contact me on WhatsApp for a quick response!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                    onClick: ()=>openWhatsApp(\"project consultation\"),\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    className: \"bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_CheckIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"WhatsApp: +254 703 973 225\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\services\\\\page.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServicesPage, \"KOeUnzKrCvIJ8uFseZamQM7a3qY=\", false, function() {\n    return [\n        _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_2__.useServices\n    ];\n});\n_c = ServicesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesPage);\nvar _c;\n$RefreshReg$(_c, \"ServicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ServicesContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/ServicesContext.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesProvider: function() { return /* binding */ ServicesProvider; },\n/* harmony export */   useServices: function() { return /* binding */ useServices; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ServicesProvider,useServices,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ServicesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst defaultServices = [\n    {\n        id: \"1\",\n        title: \"Graphic Design\",\n        description: \"Creating visually stunning designs that communicate your brand message effectively\",\n        features: [\n            \"Logo Design & Branding\",\n            \"Business Cards & Stationery\",\n            \"Brochures & Flyers\",\n            \"Social Media Graphics\",\n            \"Print Design\",\n            \"Brand Identity Development\"\n        ],\n        tools: [\n            \"Adobe Illustrator\",\n            \"Photoshop\",\n            \"InDesign\",\n            \"Figma\"\n        ],\n        icon: \"PaintBrushIcon\",\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"2\",\n        title: \"Web Design\",\n        description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n        features: [\n            \"Responsive Web Design\",\n            \"UI/UX Design\",\n            \"E-commerce Websites\",\n            \"Landing Pages\",\n            \"Web Applications\",\n            \"Website Maintenance\"\n        ],\n        tools: [\n            \"React\",\n            \"Next.js\",\n            \"Tailwind CSS\",\n            \"WordPress\"\n        ],\n        icon: \"ComputerDesktopIcon\",\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"3\",\n        title: \"Photography\",\n        description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n        features: [\n            \"Product Photography\",\n            \"Portrait Photography\",\n            \"Event Photography\",\n            \"Commercial Photography\",\n            \"Photo Editing & Retouching\",\n            \"Studio & Location Shoots\"\n        ],\n        tools: [\n            \"Canon EOS\",\n            \"Lightroom\",\n            \"Photoshop\",\n            \"Studio Lighting\"\n        ],\n        icon: \"CameraIcon\",\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"4\",\n        title: \"Video Editing\",\n        description: \"Creating engaging video content with professional editing and motion graphics\",\n        features: [\n            \"Video Production\",\n            \"Motion Graphics\",\n            \"Color Correction\",\n            \"Audio Enhancement\",\n            \"Social Media Videos\",\n            \"Commercial Videos\"\n        ],\n        tools: [\n            \"After Effects\",\n            \"Premiere Pro\",\n            \"DaVinci Resolve\",\n            \"Final Cut Pro\"\n        ],\n        icon: \"VideoCameraIcon\",\n        color: \"from-red-500 to-red-600\",\n        bgColor: \"bg-red-50\",\n        borderColor: \"border-red-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\nconst ServicesProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize services from localStorage or use defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadServices = ()=>{\n            try {\n                const savedServices = localStorage.getItem(\"elishop_services\");\n                if (savedServices) {\n                    const parsedServices = JSON.parse(savedServices);\n                    setServices(parsedServices);\n                } else {\n                    setServices(defaultServices);\n                    localStorage.setItem(\"elishop_services\", JSON.stringify(defaultServices));\n                }\n            } catch (error) {\n                console.error(\"Error loading services:\", error);\n                setServices(defaultServices);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadServices();\n    }, []);\n    // Save services to localStorage whenever services change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && services.length > 0) {\n            localStorage.setItem(\"elishop_services\", JSON.stringify(services));\n        }\n    }, [\n        services,\n        loading\n    ]);\n    const activeServices = services.filter((service)=>service.isActive);\n    const addService = (serviceData)=>{\n        const now = new Date().toISOString();\n        const newService = {\n            ...serviceData,\n            id: Date.now().toString(),\n            createdAt: now,\n            updatedAt: now\n        };\n        setServices((prev)=>[\n                ...prev,\n                newService\n            ]);\n    };\n    const updateService = (id, serviceData)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    ...serviceData,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const deleteService = (id)=>{\n        setServices((prev)=>prev.filter((service)=>service.id !== id));\n    };\n    const toggleServiceActive = (id)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    isActive: !service.isActive,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const getService = (id)=>{\n        return services.find((service)=>service.id === id);\n    };\n    const value = {\n        services,\n        activeServices,\n        addService,\n        updateService,\n        deleteService,\n        toggleServiceActive,\n        getService,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\ServicesContext.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ServicesProvider, \"b/p1Mw8EtDCdIBIHijS5szgIZQ4=\");\n_c = ServicesProvider;\nconst useServices = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServicesContext);\n    if (context === undefined) {\n        throw new Error(\"useServices must be used within a ServicesProvider\");\n    }\n    return context;\n};\n_s1(useServices, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (ServicesContext);\nvar _c;\n$RefreshReg$(_c, \"ServicesProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ServicesContext.tsx\n"));

/***/ })

});