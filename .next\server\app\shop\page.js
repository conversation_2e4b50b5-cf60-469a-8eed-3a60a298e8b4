/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/shop/page";
exports.ids = ["app/shop/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fpage&page=%2Fshop%2Fpage&appPaths=%2Fshop%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fpage&page=%2Fshop%2Fpage&appPaths=%2Fshop%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'shop',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shop/page.tsx */ \"(rsc)/./src/app/shop/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/shop/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/shop/page\",\n        pathname: \"/shop\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fpage&page=%2Fshop%2Fpage&appPaths=%2Fshop%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CLayoutWrapper.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CServicesContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CWallpaperContext.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CLayoutWrapper.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CServicesContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CWallpaperContext.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/LayoutWrapper.tsx */ \"(ssr)/./src/components/layout/LayoutWrapper.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/CartContext.tsx */ \"(ssr)/./src/contexts/CartContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ServicesContext.tsx */ \"(ssr)/./src/contexts/ServicesContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/WallpaperContext.tsx */ \"(ssr)/./src/contexts/WallpaperContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CLayoutWrapper.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CServicesContext.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CWallpaperContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cshop%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cshop%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shop/page.tsx */ \"(ssr)/./src/app/shop/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQURNTkklNUNEb2N1bWVudHMlNUNlbGlzaG9wJTVDc3JjJTVDYXBwJTVDc2hvcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2VsaXNob3AtcG9ydGZvbGlvLz9jYThkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQURNTklcXFxcRG9jdW1lbnRzXFxcXGVsaXNob3BcXFxcc3JjXFxcXGFwcFxcXFxzaG9wXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cshop%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/shop/page.tsx":
/*!*******************************!*\
  !*** ./src/app/shop/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon,ListBulletIcon,MagnifyingGlassIcon,ShoppingCartIcon,Squares2X2Icon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/CartContext */ \"(ssr)/./src/contexts/CartContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst ShopPage = ()=>{\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"featured\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        1000\n    ]);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { addItem } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    // Mock data - in real app, this would come from Firebase\n    const mockProducts = [\n        {\n            id: \"1\",\n            name: \"Premium UI Kit Pro\",\n            description: \"Complete design system with 300+ components, dark mode support, and Figma files included.\",\n            price: 89.99,\n            originalPrice: 129.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"UI Kits\",\n            rating: 4.9,\n            reviews: 234,\n            inStock: true,\n            featured: true,\n            isOnSale: true,\n            tags: [\n                \"ui\",\n                \"design\",\n                \"components\",\n                \"figma\"\n            ]\n        },\n        {\n            id: \"2\",\n            name: \"React Dashboard Template\",\n            description: \"Modern admin dashboard with charts, tables, and responsive design. Built with React and TypeScript.\",\n            price: 59.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Templates\",\n            rating: 4.8,\n            reviews: 156,\n            inStock: true,\n            featured: true,\n            isOnSale: false,\n            tags: [\n                \"react\",\n                \"dashboard\",\n                \"admin\",\n                \"typescript\"\n            ]\n        },\n        {\n            id: \"3\",\n            name: \"Icon Pack Collection\",\n            description: \"2000+ premium icons in multiple formats (SVG, PNG, AI). Perfect for web and mobile apps.\",\n            price: 29.99,\n            originalPrice: 49.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Icons\",\n            rating: 4.7,\n            reviews: 89,\n            inStock: true,\n            featured: false,\n            isOnSale: true,\n            tags: [\n                \"icons\",\n                \"svg\",\n                \"design\",\n                \"mobile\"\n            ]\n        },\n        {\n            id: \"4\",\n            name: \"Animation Library\",\n            description: \"CSS and JavaScript animations for modern web applications. Easy to implement and customize.\",\n            price: 39.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Code\",\n            rating: 4.6,\n            reviews: 67,\n            inStock: true,\n            featured: false,\n            isOnSale: false,\n            tags: [\n                \"animation\",\n                \"css\",\n                \"javascript\",\n                \"web\"\n            ]\n        },\n        {\n            id: \"5\",\n            name: \"E-commerce Template Bundle\",\n            description: \"5 complete e-commerce templates with shopping cart, checkout, and admin panel.\",\n            price: 149.99,\n            originalPrice: 199.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"Templates\",\n            rating: 4.9,\n            reviews: 123,\n            inStock: true,\n            featured: true,\n            isOnSale: true,\n            tags: [\n                \"ecommerce\",\n                \"shopping\",\n                \"template\",\n                \"bundle\"\n            ]\n        },\n        {\n            id: \"6\",\n            name: \"Mobile App UI Kit\",\n            description: \"Complete mobile app design system with 150+ screens and components for iOS and Android.\",\n            price: 79.99,\n            image: \"/api/placeholder/400/400\",\n            category: \"UI Kits\",\n            rating: 4.8,\n            reviews: 198,\n            inStock: true,\n            featured: false,\n            isOnSale: false,\n            tags: [\n                \"mobile\",\n                \"app\",\n                \"ui\",\n                \"ios\",\n                \"android\"\n            ]\n        }\n    ];\n    const categories = [\n        \"all\",\n        \"Wallpapers\",\n        \"UI Kits\",\n        \"Templates\",\n        \"Icons\",\n        \"Code\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate API call\n        setTimeout(()=>{\n            setProducts(mockProducts);\n            setFilteredProducts(mockProducts);\n            setLoading(false);\n        }, 1000);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = products;\n        // Filter by search term\n        if (searchTerm) {\n            filtered = filtered.filter((product)=>product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()) || product.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n        }\n        // Filter by category\n        if (selectedCategory !== \"all\") {\n            filtered = filtered.filter((product)=>product.category === selectedCategory);\n        }\n        // Filter by price range\n        filtered = filtered.filter((product)=>product.price >= priceRange[0] && product.price <= priceRange[1]);\n        // Sort products\n        switch(sortBy){\n            case \"price-low\":\n                filtered.sort((a, b)=>a.price - b.price);\n                break;\n            case \"price-high\":\n                filtered.sort((a, b)=>b.price - a.price);\n                break;\n            case \"rating\":\n                filtered.sort((a, b)=>b.rating - a.rating);\n                break;\n            case \"newest\":\n                break;\n            default:\n                filtered.sort((a, b)=>(b.featured ? 1 : 0) - (a.featured ? 1 : 0));\n        }\n        setFilteredProducts(filtered);\n    }, [\n        products,\n        searchTerm,\n        selectedCategory,\n        sortBy,\n        priceRange\n    ]);\n    const handleAddToCart = (product)=>{\n        addItem({\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            image: product.image\n        });\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-20 min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading products...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-20 min-h-screen bg-gradient-to-br from-gray-50 to-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl sm:text-5xl font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Our \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent\",\n                                    children: \"Shop\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Discover premium digital products, wallpapers, and resources to accelerate your projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.2\n                    },\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-600 to-yellow-500 rounded-2xl p-8 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-1/2 mb-6 lg:mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-4\",\n                                            children: \"Premium Wallpapers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg opacity-90 mb-6\",\n                                            children: \"Transform your devices with our stunning collection of high-quality wallpapers. Available in multiple resolutions including 4K, perfect for any device.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"4K Quality\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Multiple Formats\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Instant Download\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/shop/wallpapers\",\n                                            className: \"inline-flex items-center space-x-2 bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Browse Wallpapers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 5l7 7-7 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:w-1/2 lg:pl-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-32 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\uD83C\\uDF05\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-24 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl\",\n                                                            children: \"\\uD83C\\uDFAE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4 mt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-24 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl\",\n                                                            children: \"\\uD83C\\uDF0A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 backdrop-blur-sm rounded-lg p-4 h-32 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"✨\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.1\n                    },\n                    className: \"bg-white rounded-2xl shadow-lg p-6 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row gap-4 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1 max-w-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search products...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedCategory,\n                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: category,\n                                        children: category === \"all\" ? \"All Categories\" : category\n                                    }, category, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: sortBy,\n                                onChange: (e)=>setSortBy(e.target.value),\n                                className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"featured\",\n                                        children: \"Featured\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"newest\",\n                                        children: \"Newest\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price-low\",\n                                        children: \"Price: Low to High\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"price-high\",\n                                        children: \"Price: High to Low\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"rating\",\n                                        children: \"Highest Rated\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-gray-100 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"grid\"),\n                                        className: `p-2 rounded-md transition-colors ${viewMode === \"grid\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"list\"),\n                                        className: `p-2 rounded-md transition-colors ${viewMode === \"list\" ? \"bg-white text-primary-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.2\n                    },\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Showing \",\n                            filteredProducts.length,\n                            \" of \",\n                            products.length,\n                            \" products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    className: `grid gap-6 ${viewMode === \"grid\" ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\" : \"grid-cols-1\"}`,\n                    children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                y: -5\n                            },\n                            className: `group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100 ${viewMode === \"list\" ? \"flex\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative overflow-hidden bg-gradient-to-br from-primary-100 to-secondary-100 ${viewMode === \"list\" ? \"w-48 h-48\" : \"h-48\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl font-bold text-primary-400\",\n                                                children: product.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3 flex flex-col space-y-2\",\n                                            children: [\n                                                product.isOnSale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-md\",\n                                                    children: \"SALE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-secondary-500 text-white text-xs font-medium px-2 py-1 rounded-md\",\n                                                    children: \"FEATURED\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            onClick: ()=>toggleFavorite(product.id),\n                                            className: \"absolute top-3 right-3 w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-200\",\n                                            children: favorites.includes(product.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                onClick: ()=>handleAddToCart(product),\n                                                className: \"bg-white text-primary-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Add to Cart\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-4 ${viewMode === \"list\" ? \"flex-1\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-primary-600 font-medium\",\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_ListBulletIcon_MagnifyingGlassIcon_ShoppingCartIcon_Squares2X2Icon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 text-yellow-400 fill-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                product.rating,\n                                                                \" (\",\n                                                                product.reviews,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: product.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        product.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 line-through\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.originalPrice\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: `/shop/product/${product.id}`,\n                                                    className: \"text-primary-600 hover:text-primary-700 font-medium text-sm\",\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, undefined),\n                filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDD0D\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"No products found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Try adjusting your search or filter criteria\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setSearchTerm(\"\");\n                                setSelectedCategory(\"all\");\n                                setPriceRange([\n                                    0,\n                                    1000\n                                ]);\n                            },\n                            className: \"bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200\",\n                            children: \"Clear Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\page.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShopPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/shop/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/contact/WhatsAppContact.tsx":
/*!****************************************************!*\
  !*** ./src/components/contact/WhatsAppContact.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ServicesContext */ \"(ssr)/./src/contexts/ServicesContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaintBrushIcon,VideoCameraIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Services are now managed through ServicesContext\nconst WhatsAppContact = ()=>{\n    const { activeServices } = (0,_contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_2__.useServices)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedService, setSelectedService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [customMessage, setCustomMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const whatsappNumber = \"254703973225\"; // Your WhatsApp number\n    const getIconComponent = (iconName)=>{\n        switch(iconName){\n            case \"PaintBrushIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"ComputerDesktopIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"CameraIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"VideoCameraIcon\":\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            default:\n                return _barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        }\n    };\n    const generateWhatsAppMessage = (service)=>{\n        let message = `Hi Elias! 👋\\n\\nI'm interested in your services.\\n\\n`;\n        if (service) {\n            message += `Service: ${service.title}\\n`;\n            message += `Details: ${service.description}\\n\\n`;\n        }\n        if (customMessage.trim()) {\n            message += `Additional Details:\\n${customMessage}\\n\\n`;\n        }\n        message += `Please let me know about pricing and availability.\\n\\nThank you!`;\n        return encodeURIComponent(message);\n    };\n    const openWhatsApp = (service)=>{\n        const message = generateWhatsAppMessage(service);\n        const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;\n        window.open(whatsappUrl, \"_blank\");\n        setIsOpen(false);\n    };\n    const handleQuickContact = ()=>{\n        const message = encodeURIComponent(`Hi Elias! 👋\\n\\nI'd like to discuss your creative services. Please let me know when you're available to chat.\\n\\nThank you!`);\n        const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;\n        window.open(whatsappUrl, \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                className: \"fixed bottom-6 right-6 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                    onClick: ()=>setIsOpen(true),\n                    whileHover: {\n                        scale: 1.1\n                    },\n                    whileTap: {\n                        scale: 0.9\n                    },\n                    className: \"bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:block font-medium\",\n                            children: \"Get Services\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n                    onClick: ()=>setIsOpen(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        animate: {\n                            scale: 1,\n                            opacity: 1\n                        },\n                        exit: {\n                            scale: 0.9,\n                            opacity: 0\n                        },\n                        onClick: (e)=>e.stopPropagation(),\n                        className: \"bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-500 text-white p-6 rounded-t-2xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold\",\n                                                            children: \"WhatsApp Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-green-100\",\n                                                            children: \"Get in touch for services\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsOpen(false),\n                                            className: \"text-white hover:bg-green-600 p-2 rounded-full transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleQuickContact,\n                                                className: \"w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-xl font-semibold transition-colors duration-200 flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaintBrushIcon_VideoCameraIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Quick Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 text-center mt-2\",\n                                                children: \"Start a general conversation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full border-t border-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex justify-center text-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 bg-white text-gray-500\",\n                                                    children: \"Or choose a service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-6\",\n                                        children: activeServices.map((service)=>{\n                                            const IconComponent = getIconComponent(service.icon);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                                onClick: ()=>openWhatsApp(service),\n                                                whileHover: {\n                                                    scale: 1.02\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                className: \"w-full p-4 border border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all duration-200 text-left\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg\",\n                                                            style: {\n                                                                background: `linear-gradient(135deg, ${service.color.includes(\"purple\") ? \"#8B5CF6\" : service.color.includes(\"blue\") ? \"#3B82F6\" : service.color.includes(\"green\") ? \"#10B981\" : \"#EF4444\"} 0%, ${service.color.includes(\"purple\") ? \"#A855F7\" : service.color.includes(\"blue\") ? \"#1D4ED8\" : service.color.includes(\"green\") ? \"#059669\" : \"#DC2626\"} 100%)`\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-5 h-5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-gray-900\",\n                                                                    children: service.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                                    children: service.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, service.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Additional Details (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: customMessage,\n                                                onChange: (e)=>setCustomMessage(e.target.value),\n                                                placeholder: \"Tell me more about your project requirements...\",\n                                                rows: 3,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-2\",\n                                                    children: \"WhatsApp Number\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"+254 703 973 225\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Available Monday - Friday, 9 AM - 6 PM EAT\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\contact\\\\WhatsAppContact.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhatsAppContact);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jb250YWN0L1doYXRzQXBwQ29udGFjdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ2dCO0FBQ0M7QUFRcEI7QUFFckMsbURBQW1EO0FBRW5ELE1BQU1XLGtCQUE0QjtJQUNoQyxNQUFNLEVBQUVDLGNBQWMsRUFBRSxHQUFHUixzRUFBV0E7SUFDdEMsTUFBTSxDQUFDUyxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ2MsaUJBQWlCQyxtQkFBbUIsR0FBR2YsK0NBQVFBLENBQVM7SUFDL0QsTUFBTSxDQUFDZ0IsZUFBZUMsaUJBQWlCLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUVuRCxNQUFNa0IsaUJBQWlCLGdCQUFnQix1QkFBdUI7SUFFOUQsTUFBTUMsbUJBQW1CLENBQUNDO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPZCx5TEFBY0E7WUFDdkIsS0FBSztnQkFDSCxPQUFPQyx5TEFBbUJBO1lBQzVCLEtBQUs7Z0JBQ0gsT0FBT0MseUxBQVVBO1lBQ25CLEtBQUs7Z0JBQ0gsT0FBT0MseUxBQWVBO1lBQ3hCO2dCQUNFLE9BQU9ILHlMQUFjQTtRQUN6QjtJQUNGO0lBRUEsTUFBTWUsMEJBQTBCLENBQUNDO1FBQy9CLElBQUlDLFVBQVUsQ0FBQyxvREFBb0QsQ0FBQztRQUVwRSxJQUFJRCxTQUFTO1lBQ1hDLFdBQVcsQ0FBQyxTQUFTLEVBQUVELFFBQVFFLEtBQUssQ0FBQyxFQUFFLENBQUM7WUFDeENELFdBQVcsQ0FBQyxTQUFTLEVBQUVELFFBQVFHLFdBQVcsQ0FBQyxJQUFJLENBQUM7UUFDbEQ7UUFFQSxJQUFJVCxjQUFjVSxJQUFJLElBQUk7WUFDeEJILFdBQVcsQ0FBQyxxQkFBcUIsRUFBRVAsY0FBYyxJQUFJLENBQUM7UUFDeEQ7UUFFQU8sV0FBVyxDQUFDLGdFQUFnRSxDQUFDO1FBRTdFLE9BQU9JLG1CQUFtQko7SUFDNUI7SUFFQSxNQUFNSyxlQUFlLENBQUNOO1FBQ3BCLE1BQU1DLFVBQVVGLHdCQUF3QkM7UUFDeEMsTUFBTU8sY0FBYyxDQUFDLGNBQWMsRUFBRVgsZUFBZSxNQUFNLEVBQUVLLFFBQVEsQ0FBQztRQUNyRU8sT0FBT0MsSUFBSSxDQUFDRixhQUFhO1FBQ3pCaEIsVUFBVTtJQUNaO0lBRUEsTUFBTW1CLHFCQUFxQjtRQUN6QixNQUFNVCxVQUFVSSxtQkFDZCxDQUFDLDJIQUEySCxDQUFDO1FBRS9ILE1BQU1FLGNBQWMsQ0FBQyxjQUFjLEVBQUVYLGVBQWUsTUFBTSxFQUFFSyxRQUFRLENBQUM7UUFDckVPLE9BQU9DLElBQUksQ0FBQ0YsYUFBYTtJQUMzQjtJQUVBLHFCQUNFOzswQkFFRSw4REFBQzVCLGlEQUFNQSxDQUFDZ0MsR0FBRztnQkFDVEMsU0FBUztvQkFBRUMsT0FBTztnQkFBRTtnQkFDcEJDLFNBQVM7b0JBQUVELE9BQU87Z0JBQUU7Z0JBQ3BCRSxXQUFVOzBCQUVWLDRFQUFDcEMsaURBQU1BLENBQUNxQyxNQUFNO29CQUNaQyxTQUFTLElBQU0xQixVQUFVO29CQUN6QjJCLFlBQVk7d0JBQUVMLE9BQU87b0JBQUk7b0JBQ3pCTSxVQUFVO3dCQUFFTixPQUFPO29CQUFJO29CQUN2QkUsV0FBVTs7c0NBRVYsOERBQUNqQyx5TEFBdUJBOzRCQUFDaUMsV0FBVTs7Ozs7O3NDQUNuQyw4REFBQ0s7NEJBQUtMLFdBQVU7c0NBQThCOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLbEQsOERBQUNuQywwREFBZUE7MEJBQ2JVLHdCQUNDLDhEQUFDWCxpREFBTUEsQ0FBQ2dDLEdBQUc7b0JBQ1RDLFNBQVM7d0JBQUVTLFNBQVM7b0JBQUU7b0JBQ3RCUCxTQUFTO3dCQUFFTyxTQUFTO29CQUFFO29CQUN0QkMsTUFBTTt3QkFBRUQsU0FBUztvQkFBRTtvQkFDbkJOLFdBQVU7b0JBQ1ZFLFNBQVMsSUFBTTFCLFVBQVU7OEJBRXpCLDRFQUFDWixpREFBTUEsQ0FBQ2dDLEdBQUc7d0JBQ1RDLFNBQVM7NEJBQUVDLE9BQU87NEJBQUtRLFNBQVM7d0JBQUU7d0JBQ2xDUCxTQUFTOzRCQUFFRCxPQUFPOzRCQUFHUSxTQUFTO3dCQUFFO3dCQUNoQ0MsTUFBTTs0QkFBRVQsT0FBTzs0QkFBS1EsU0FBUzt3QkFBRTt3QkFDL0JKLFNBQVMsQ0FBQ00sSUFBTUEsRUFBRUMsZUFBZTt3QkFDakNULFdBQVU7OzBDQUdWLDhEQUFDSjtnQ0FBSUksV0FBVTswQ0FDYiw0RUFBQ0o7b0NBQUlJLFdBQVU7O3NEQUNiLDhEQUFDSjs0Q0FBSUksV0FBVTs7OERBQ2IsOERBQUNqQyx5TEFBdUJBO29EQUFDaUMsV0FBVTs7Ozs7OzhEQUNuQyw4REFBQ0o7O3NFQUNDLDhEQUFDYzs0REFBR1YsV0FBVTtzRUFBb0I7Ozs7OztzRUFDbEMsOERBQUNXOzREQUFFWCxXQUFVO3NFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUdsQyw4REFBQ0M7NENBQ0NDLFNBQVMsSUFBTTFCLFVBQVU7NENBQ3pCd0IsV0FBVTtzREFFViw0RUFBQ2hDLDBMQUFTQTtnREFBQ2dDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTNCLDhEQUFDSjtnQ0FBSUksV0FBVTs7a0RBRWIsOERBQUNKO3dDQUFJSSxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQ0NDLFNBQVNQO2dEQUNUSyxXQUFVOztrRUFFViw4REFBQ2pDLHlMQUF1QkE7d0RBQUNpQyxXQUFVOzs7Ozs7a0VBQ25DLDhEQUFDSztrRUFBSzs7Ozs7Ozs7Ozs7OzBEQUVSLDhEQUFDTTtnREFBRVgsV0FBVTswREFBeUM7Ozs7Ozs7Ozs7OztrREFLeEQsOERBQUNKO3dDQUFJSSxXQUFVOzswREFDYiw4REFBQ0o7Z0RBQUlJLFdBQVU7MERBQ2IsNEVBQUNKO29EQUFJSSxXQUFVOzs7Ozs7Ozs7OzswREFFakIsOERBQUNKO2dEQUFJSSxXQUFVOzBEQUNiLDRFQUFDSztvREFBS0wsV0FBVTs4REFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtsRCw4REFBQ0o7d0NBQUlJLFdBQVU7a0RBQ1oxQixlQUFlc0MsR0FBRyxDQUFDLENBQUMzQjs0Q0FDbkIsTUFBTTRCLGdCQUFnQi9CLGlCQUFpQkcsUUFBUTZCLElBQUk7NENBQ25ELHFCQUNFLDhEQUFDbEQsaURBQU1BLENBQUNxQyxNQUFNO2dEQUVaQyxTQUFTLElBQU1YLGFBQWFOO2dEQUM1QmtCLFlBQVk7b0RBQUVMLE9BQU87Z0RBQUs7Z0RBQzFCTSxVQUFVO29EQUFFTixPQUFPO2dEQUFLO2dEQUN4QkUsV0FBVTswREFFViw0RUFBQ0o7b0RBQUlJLFdBQVU7O3NFQUNiLDhEQUFDSjs0REFDQ0ksV0FBVTs0REFDVmUsT0FBTztnRUFDTEMsWUFBWSxDQUFDLHdCQUF3QixFQUFFL0IsUUFBUWdDLEtBQUssQ0FBQ0MsUUFBUSxDQUFDLFlBQVksWUFBWWpDLFFBQVFnQyxLQUFLLENBQUNDLFFBQVEsQ0FBQyxVQUFVLFlBQVlqQyxRQUFRZ0MsS0FBSyxDQUFDQyxRQUFRLENBQUMsV0FBVyxZQUFZLFVBQVUsS0FBSyxFQUFFakMsUUFBUWdDLEtBQUssQ0FBQ0MsUUFBUSxDQUFDLFlBQVksWUFBWWpDLFFBQVFnQyxLQUFLLENBQUNDLFFBQVEsQ0FBQyxVQUFVLFlBQVlqQyxRQUFRZ0MsS0FBSyxDQUFDQyxRQUFRLENBQUMsV0FBVyxZQUFZLFVBQVUsTUFBTSxDQUFDOzREQUMvVjtzRUFFQSw0RUFBQ0w7Z0VBQWNiLFdBQVU7Ozs7Ozs7Ozs7O3NFQUUzQiw4REFBQ0o7NERBQUlJLFdBQVU7OzhFQUNiLDhEQUFDbUI7b0VBQUduQixXQUFVOzhFQUErQmYsUUFBUUUsS0FBSzs7Ozs7OzhFQUMxRCw4REFBQ3dCO29FQUFFWCxXQUFVOzhFQUE4QmYsUUFBUUcsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQWpCN0RILFFBQVFtQyxFQUFFOzs7Ozt3Q0FzQnJCOzs7Ozs7a0RBSUYsOERBQUN4Qjt3Q0FBSUksV0FBVTs7MERBQ2IsOERBQUNxQjtnREFBTXJCLFdBQVU7MERBQStDOzs7Ozs7MERBR2hFLDhEQUFDc0I7Z0RBQ0NDLE9BQU81QztnREFDUDZDLFVBQVUsQ0FBQ2hCLElBQU01QixpQkFBaUI0QixFQUFFaUIsTUFBTSxDQUFDRixLQUFLO2dEQUNoREcsYUFBWTtnREFDWkMsTUFBTTtnREFDTjNCLFdBQVU7Ozs7Ozs7Ozs7OztrREFLZCw4REFBQ0o7d0NBQUlJLFdBQVU7a0RBQ2IsNEVBQUNKOzRDQUFJSSxXQUFVOzs4REFDYiw4REFBQ1c7b0RBQUVYLFdBQVU7OERBQTZCOzs7Ozs7OERBQzFDLDhEQUFDVztvREFBRVgsV0FBVTs4REFBOEI7Ozs7Ozs4REFDM0MsOERBQUNXO29EQUFFWCxXQUFVOzhEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFZOUQ7QUFFQSxpRUFBZTNCLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGlzaG9wLXBvcnRmb2xpby8uL3NyYy9jb21wb25lbnRzL2NvbnRhY3QvV2hhdHNBcHBDb250YWN0LnRzeD9jNzZhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IHVzZVNlcnZpY2VzIH0gZnJvbSAnQC9jb250ZXh0cy9TZXJ2aWNlc0NvbnRleHQnO1xuaW1wb3J0IHtcbiAgQ2hhdEJ1YmJsZUxlZnRSaWdodEljb24sXG4gIFhNYXJrSWNvbixcbiAgUGFpbnRCcnVzaEljb24sXG4gIENvbXB1dGVyRGVza3RvcEljb24sXG4gIENhbWVyYUljb24sXG4gIFZpZGVvQ2FtZXJhSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG4vLyBTZXJ2aWNlcyBhcmUgbm93IG1hbmFnZWQgdGhyb3VnaCBTZXJ2aWNlc0NvbnRleHRcblxuY29uc3QgV2hhdHNBcHBDb250YWN0OiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgeyBhY3RpdmVTZXJ2aWNlcyB9ID0gdXNlU2VydmljZXMoKTtcbiAgY29uc3QgW2lzT3Blbiwgc2V0SXNPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkU2VydmljZSwgc2V0U2VsZWN0ZWRTZXJ2aWNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbY3VzdG9tTWVzc2FnZSwgc2V0Q3VzdG9tTWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgY29uc3Qgd2hhdHNhcHBOdW1iZXIgPSAnMjU0NzAzOTczMjI1JzsgLy8gWW91ciBXaGF0c0FwcCBudW1iZXJcblxuICBjb25zdCBnZXRJY29uQ29tcG9uZW50ID0gKGljb25OYW1lOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKGljb25OYW1lKSB7XG4gICAgICBjYXNlICdQYWludEJydXNoSWNvbic6XG4gICAgICAgIHJldHVybiBQYWludEJydXNoSWNvbjtcbiAgICAgIGNhc2UgJ0NvbXB1dGVyRGVza3RvcEljb24nOlxuICAgICAgICByZXR1cm4gQ29tcHV0ZXJEZXNrdG9wSWNvbjtcbiAgICAgIGNhc2UgJ0NhbWVyYUljb24nOlxuICAgICAgICByZXR1cm4gQ2FtZXJhSWNvbjtcbiAgICAgIGNhc2UgJ1ZpZGVvQ2FtZXJhSWNvbic6XG4gICAgICAgIHJldHVybiBWaWRlb0NhbWVyYUljb247XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gUGFpbnRCcnVzaEljb247XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdlbmVyYXRlV2hhdHNBcHBNZXNzYWdlID0gKHNlcnZpY2U/OiBhbnkpID0+IHtcbiAgICBsZXQgbWVzc2FnZSA9IGBIaSBFbGlhcyEg8J+Ri1xcblxcbkknbSBpbnRlcmVzdGVkIGluIHlvdXIgc2VydmljZXMuXFxuXFxuYDtcbiAgICBcbiAgICBpZiAoc2VydmljZSkge1xuICAgICAgbWVzc2FnZSArPSBgU2VydmljZTogJHtzZXJ2aWNlLnRpdGxlfVxcbmA7XG4gICAgICBtZXNzYWdlICs9IGBEZXRhaWxzOiAke3NlcnZpY2UuZGVzY3JpcHRpb259XFxuXFxuYDtcbiAgICB9XG4gICAgXG4gICAgaWYgKGN1c3RvbU1lc3NhZ2UudHJpbSgpKSB7XG4gICAgICBtZXNzYWdlICs9IGBBZGRpdGlvbmFsIERldGFpbHM6XFxuJHtjdXN0b21NZXNzYWdlfVxcblxcbmA7XG4gICAgfVxuICAgIFxuICAgIG1lc3NhZ2UgKz0gYFBsZWFzZSBsZXQgbWUga25vdyBhYm91dCBwcmljaW5nIGFuZCBhdmFpbGFiaWxpdHkuXFxuXFxuVGhhbmsgeW91IWA7XG4gICAgXG4gICAgcmV0dXJuIGVuY29kZVVSSUNvbXBvbmVudChtZXNzYWdlKTtcbiAgfTtcblxuICBjb25zdCBvcGVuV2hhdHNBcHAgPSAoc2VydmljZT86IGFueSkgPT4ge1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBnZW5lcmF0ZVdoYXRzQXBwTWVzc2FnZShzZXJ2aWNlKTtcbiAgICBjb25zdCB3aGF0c2FwcFVybCA9IGBodHRwczovL3dhLm1lLyR7d2hhdHNhcHBOdW1iZXJ9P3RleHQ9JHttZXNzYWdlfWA7XG4gICAgd2luZG93Lm9wZW4od2hhdHNhcHBVcmwsICdfYmxhbmsnKTtcbiAgICBzZXRJc09wZW4oZmFsc2UpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVF1aWNrQ29udGFjdCA9ICgpID0+IHtcbiAgICBjb25zdCBtZXNzYWdlID0gZW5jb2RlVVJJQ29tcG9uZW50KFxuICAgICAgYEhpIEVsaWFzISDwn5GLXFxuXFxuSSdkIGxpa2UgdG8gZGlzY3VzcyB5b3VyIGNyZWF0aXZlIHNlcnZpY2VzLiBQbGVhc2UgbGV0IG1lIGtub3cgd2hlbiB5b3UncmUgYXZhaWxhYmxlIHRvIGNoYXQuXFxuXFxuVGhhbmsgeW91IWBcbiAgICApO1xuICAgIGNvbnN0IHdoYXRzYXBwVXJsID0gYGh0dHBzOi8vd2EubWUvJHt3aGF0c2FwcE51bWJlcn0/dGV4dD0ke21lc3NhZ2V9YDtcbiAgICB3aW5kb3cub3Blbih3aGF0c2FwcFVybCwgJ19ibGFuaycpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBGbG9hdGluZyBXaGF0c0FwcCBCdXR0b24gKi99XG4gICAgICA8bW90aW9uLmRpdlxuICAgICAgICBpbml0aWFsPXt7IHNjYWxlOiAwIH19XG4gICAgICAgIGFuaW1hdGU9e3sgc2NhbGU6IDEgfX1cbiAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgYm90dG9tLTYgcmlnaHQtNiB6LTUwXCJcbiAgICAgID5cbiAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4odHJ1ZSl9XG4gICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xIH19XG4gICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMCBob3ZlcjpiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSBwLTQgcm91bmRlZC1mdWxsIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiXG4gICAgICAgID5cbiAgICAgICAgICA8Q2hhdEJ1YmJsZUxlZnRSaWdodEljb24gY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmJsb2NrIGZvbnQtbWVkaXVtXCI+R2V0IFNlcnZpY2VzPC9zcGFuPlxuICAgICAgICA8L21vdGlvbi5idXR0b24+XG4gICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgIHsvKiBXaGF0c0FwcCBDb250YWN0IE1vZGFsICovfVxuICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAge2lzT3BlbiAmJiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2svNTAgYmFja2Ryb3AtYmx1ci1zbSB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNFwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oZmFsc2UpfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgc2NhbGU6IDAuOSwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IHNjYWxlOiAxLCBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgc2NhbGU6IDAuOSwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LTJ4bCBtYXgtdy1tZCB3LWZ1bGwgbWF4LWgtWzkwdmhdIG92ZXJmbG93LXktYXV0b1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgcC02IHJvdW5kZWQtdC0yeGxcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIGNsYXNzTmFtZT1cInctOCBoLThcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPldoYXRzQXBwIENvbnRhY3Q8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMTAwXCI+R2V0IGluIHRvdWNoIGZvciBzZXJ2aWNlczwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3ZlcjpiZy1ncmVlbi02MDAgcC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8WE1hcmtJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgIHsvKiBRdWljayBDb250YWN0ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVRdWlja0NvbnRhY3R9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmVlbi01MDAgaG92ZXI6YmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgcHktMyBweC00IHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPENoYXRCdWJibGVMZWZ0UmlnaHRJY29uIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5RdWljayBDb250YWN0PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICBTdGFydCBhIGdlbmVyYWwgY29udmVyc2F0aW9uXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItdCBib3JkZXItZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgganVzdGlmeS1jZW50ZXIgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0yIGJnLXdoaXRlIHRleHQtZ3JheS01MDBcIj5PciBjaG9vc2UgYSBzZXJ2aWNlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogU2VydmljZXMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAge2FjdGl2ZVNlcnZpY2VzLm1hcCgoc2VydmljZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gZ2V0SWNvbkNvbXBvbmVudChzZXJ2aWNlLmljb24pO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uYnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NlcnZpY2UuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuV2hhdHNBcHAoc2VydmljZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjAyIH19XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45OCB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtNCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQteGwgaG92ZXI6Ym9yZGVyLWdyZWVuLTMwMCBob3ZlcjpiZy1ncmVlbi01MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdGV4dC1sZWZ0XCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke3NlcnZpY2UuY29sb3IuaW5jbHVkZXMoJ3B1cnBsZScpID8gJyM4QjVDRjYnIDogc2VydmljZS5jb2xvci5pbmNsdWRlcygnYmx1ZScpID8gJyMzQjgyRjYnIDogc2VydmljZS5jb2xvci5pbmNsdWRlcygnZ3JlZW4nKSA/ICcjMTBCOTgxJyA6ICcjRUY0NDQ0J30gMCUsICR7c2VydmljZS5jb2xvci5pbmNsdWRlcygncHVycGxlJykgPyAnI0E4NTVGNycgOiBzZXJ2aWNlLmNvbG9yLmluY2x1ZGVzKCdibHVlJykgPyAnIzFENEVEOCcgOiBzZXJ2aWNlLmNvbG9yLmluY2x1ZGVzKCdncmVlbicpID8gJyMwNTk2NjknIDogJyNEQzI2MjYnfSAxMDAlKWBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25Db21wb25lbnQgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPntzZXJ2aWNlLnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTFcIj57c2VydmljZS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ3VzdG9tIE1lc3NhZ2UgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgQWRkaXRpb25hbCBEZXRhaWxzIChPcHRpb25hbClcbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2N1c3RvbU1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q3VzdG9tTWVzc2FnZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVGVsbCBtZSBtb3JlIGFib3V0IHlvdXIgcHJvamVjdCByZXF1aXJlbWVudHMuLi5cIlxuICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ncmVlbi01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ29udGFjdCBJbmZvICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItMlwiPldoYXRzQXBwIE51bWJlcjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+KzI1NCA3MDMgOTczIDIyNTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBBdmFpbGFibGUgTW9uZGF5IC0gRnJpZGF5LCA5IEFNIC0gNiBQTSBFQVRcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgIDwvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgV2hhdHNBcHBDb250YWN0O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJ1c2VTZXJ2aWNlcyIsIkNoYXRCdWJibGVMZWZ0UmlnaHRJY29uIiwiWE1hcmtJY29uIiwiUGFpbnRCcnVzaEljb24iLCJDb21wdXRlckRlc2t0b3BJY29uIiwiQ2FtZXJhSWNvbiIsIlZpZGVvQ2FtZXJhSWNvbiIsIldoYXRzQXBwQ29udGFjdCIsImFjdGl2ZVNlcnZpY2VzIiwiaXNPcGVuIiwic2V0SXNPcGVuIiwic2VsZWN0ZWRTZXJ2aWNlIiwic2V0U2VsZWN0ZWRTZXJ2aWNlIiwiY3VzdG9tTWVzc2FnZSIsInNldEN1c3RvbU1lc3NhZ2UiLCJ3aGF0c2FwcE51bWJlciIsImdldEljb25Db21wb25lbnQiLCJpY29uTmFtZSIsImdlbmVyYXRlV2hhdHNBcHBNZXNzYWdlIiwic2VydmljZSIsIm1lc3NhZ2UiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwidHJpbSIsImVuY29kZVVSSUNvbXBvbmVudCIsIm9wZW5XaGF0c0FwcCIsIndoYXRzYXBwVXJsIiwid2luZG93Iiwib3BlbiIsImhhbmRsZVF1aWNrQ29udGFjdCIsImRpdiIsImluaXRpYWwiLCJzY2FsZSIsImFuaW1hdGUiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwid2hpbGVIb3ZlciIsIndoaWxlVGFwIiwic3BhbiIsIm9wYWNpdHkiLCJleGl0IiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsImgzIiwicCIsIm1hcCIsIkljb25Db21wb25lbnQiLCJpY29uIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiY29sb3IiLCJpbmNsdWRlcyIsImg0IiwiaWQiLCJsYWJlbCIsInRleHRhcmVhIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicm93cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/contact/WhatsAppContact.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaDribbble,FaGithub,FaInstagram,FaLinkedin,FaTwitter!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        portfolio: [\n            {\n                name: \"Projects\",\n                href: \"/portfolio\"\n            },\n            {\n                name: \"Skills\",\n                href: \"/portfolio#skills\"\n            },\n            {\n                name: \"Experience\",\n                href: \"/portfolio#experience\"\n            },\n            {\n                name: \"Testimonials\",\n                href: \"/portfolio#testimonials\"\n            }\n        ],\n        shop: [\n            {\n                name: \"All Products\",\n                href: \"/shop\"\n            },\n            {\n                name: \"Featured\",\n                href: \"/shop/featured\"\n            },\n            {\n                name: \"Categories\",\n                href: \"/shop/categories\"\n            },\n            {\n                name: \"Sale\",\n                href: \"/shop/sale\"\n            }\n        ],\n        support: [\n            {\n                name: \"Contact\",\n                href: \"/contact\"\n            },\n            {\n                name: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                name: \"Shipping\",\n                href: \"/shipping\"\n            },\n            {\n                name: \"Returns\",\n                href: \"/returns\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                name: \"Cookie Policy\",\n                href: \"/cookies\"\n            },\n            {\n                name: \"Refund Policy\",\n                href: \"/refunds\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"GitHub\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGithub,\n            href: \"https://github.com\",\n            color: \"hover:text-gray-900\"\n        },\n        {\n            name: \"LinkedIn\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLinkedin,\n            href: \"https://linkedin.com\",\n            color: \"hover:text-blue-600\"\n        },\n        {\n            name: \"Twitter\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTwitter,\n            href: \"https://twitter.com\",\n            color: \"hover:text-blue-400\"\n        },\n        {\n            name: \"Instagram\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaInstagram,\n            href: \"https://instagram.com\",\n            color: \"hover:text-pink-600\"\n        },\n        {\n            name: \"Dribbble\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDribbble,\n            href: \"https://dribbble.com\",\n            color: \"hover:text-pink-500\"\n        }\n    ];\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: \"<EMAIL>\",\n            href: \"mailto:<EMAIL>\"\n        },\n        {\n            icon: _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: \"+****************\",\n            href: \"tel:+15551234567\"\n        },\n        {\n            icon: _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            text: \"New York, NY\",\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gradient-to-br from-gray-900 via-primary-900 to-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-yellow-gradient rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-2xl\",\n                                                        children: \"E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                                    children: \"EliShop\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.1\n                                        },\n                                        className: \"text-gray-300 text-lg leading-relaxed max-w-md\",\n                                        children: \"A modern portfolio and shop experience featuring stunning animations, premium products, and exceptional user experience.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.2\n                                        },\n                                        className: \"space-y-3\",\n                                        children: contactInfo.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                href: item.href,\n                                                whileHover: {\n                                                    x: 5\n                                                },\n                                                className: \"flex items-center space-x-3 text-gray-300 hover:text-secondary-400 transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.text\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                href: social.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                whileHover: {\n                                                    scale: 1.2,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                className: `p-3 bg-white/10 rounded-lg text-gray-300 ${social.color} transition-all duration-200 hover:bg-white/20`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, social.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            Object.entries(footerLinks).map(([category, links], categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1 * (categoryIndex + 1)\n                                    },\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white capitalize\",\n                                            children: category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: links.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-gray-300 hover:text-secondary-400 transition-colors duration-200 hover:translate-x-1 transform inline-block\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, category, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        className: \"mt-16 pt-8 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-4\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6\",\n                                    children: \"Subscribe to get notified about new projects and products.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent transition-all duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.5\n                },\n                className: \"border-t border-gray-700 bg-gray-900/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" EliShop. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Made with ❤️ and lots of ☕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Powered by\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-secondary-400 font-medium\",\n                                                children: \"Next.js\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"&\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary-400 font-medium\",\n                                                children: \"Firebase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/CartContext */ \"(ssr)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// import { useAuth } from '@/contexts/AuthContext';\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const { user, logout } = useAuth();\n    const user = null; // Temporary for testing\n    const { itemCount } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Services\",\n            href: \"/services\"\n        },\n        {\n            name: \"Portfolio\",\n            href: \"/portfolio\"\n        },\n        {\n            name: \"Shop\",\n            href: \"/shop\",\n            submenu: [\n                {\n                    name: \"All Products\",\n                    href: \"/shop\"\n                },\n                {\n                    name: \"Wallpapers\",\n                    href: \"/shop/wallpapers\"\n                },\n                {\n                    name: \"Templates\",\n                    href: \"/shop/templates\"\n                },\n                {\n                    name: \"UI Kits\",\n                    href: \"/shop/ui-kits\"\n                }\n            ]\n        },\n        {\n            name: \"About\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        },\n        {\n            name: \"Admin\",\n            href: \"/admin\",\n            isAdmin: true\n        }\n    ];\n    const headerVariants = {\n        initial: {\n            y: -100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        exit: {\n            y: -100,\n            opacity: 0\n        }\n    };\n    const mobileMenuVariants = {\n        closed: {\n            opacity: 0,\n            x: \"100%\"\n        },\n        open: {\n            opacity: 1,\n            x: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.header, {\n        variants: headerVariants,\n        initial: \"initial\",\n        animate: \"animate\",\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/90 backdrop-blur-md shadow-lg border-b border-primary-100\" : \"bg-transparent\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-purple-yellow-gradient rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: \"E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent\",\n                                        children: \"EliShop\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: `relative transition-colors duration-200 font-medium group ${item.isAdmin ? \"text-purple-600 hover:text-purple-700 text-sm px-3 py-1 bg-purple-50 rounded-full border border-purple-200 hover:bg-purple-100\" : \"text-gray-700 hover:text-primary-600\"}`,\n                                        children: [\n                                            item.name,\n                                            !item.isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cart\",\n                                        className: \"p-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"absolute -top-1 -right-1 bg-secondary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium\",\n                                                children: itemCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"flex items-center space-x-2 p-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: user.displayName || \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/profile\",\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Orders\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Sign Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signup\",\n                                            className: \"px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            className: \"lg:hidden p-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: mobileMenuVariants,\n                    initial: \"closed\",\n                    animate: \"open\",\n                    exit: \"closed\",\n                    transition: {\n                        type: \"tween\",\n                        duration: 0.3\n                    },\n                    className: \"lg:hidden fixed top-16 right-0 bottom-0 w-80 bg-white shadow-xl border-l border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-4\",\n                                children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            className: \"block text-lg font-medium text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cart\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"flex items-center space-x-3 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Cart (\",\n                                                    itemCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.displayName || \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-9 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/profile\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        className: \"block text-gray-600 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        className: \"block text-gray-600 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Orders\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            // logout();\n                                                            setIsMobileMenuOpen(false);\n                                                        },\n                                                        className: \"block text-gray-600 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Sign Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                className: \"block w-full text-center px-4 py-2 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors duration-200\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                className: \"block w-full text-center px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200\",\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/LayoutWrapper.tsx":
/*!*************************************************!*\
  !*** ./src/components/layout/LayoutWrapper.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_contact_WhatsAppContact__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/contact/WhatsAppContact */ \"(ssr)/./src/components/contact/WhatsAppContact.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst LayoutWrapper = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isAdminPage = pathname?.startsWith(\"/admin\");\n    if (isAdminPage) {\n        // For admin pages, render children without header/footer\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // For regular pages, render with header and footer\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_contact_WhatsAppContact__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LayoutWrapper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/LayoutWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useCart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useCart */ \"(ssr)/./src/hooks/useCart.ts\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider auto */ \n\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\nconst CartProvider = ({ children })=>{\n    const cartState = (0,_hooks_useCart__WEBPACK_IMPORTED_MODULE_2__.useCartState)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: cartState,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ServicesContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/ServicesContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesProvider: () => (/* binding */ ServicesProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useServices: () => (/* binding */ useServices)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ServicesProvider,useServices,default auto */ \n\nconst ServicesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst defaultServices = [\n    {\n        id: \"1\",\n        title: \"Graphic Design\",\n        description: \"Creating visually stunning designs that communicate your brand message effectively\",\n        features: [\n            \"Logo Design & Branding\",\n            \"Business Cards & Stationery\",\n            \"Brochures & Flyers\",\n            \"Social Media Graphics\",\n            \"Print Design\",\n            \"Brand Identity Development\"\n        ],\n        tools: [\n            \"Adobe Illustrator\",\n            \"Photoshop\",\n            \"InDesign\",\n            \"Figma\"\n        ],\n        icon: \"PaintBrushIcon\",\n        color: \"from-purple-500 to-purple-600\",\n        bgColor: \"bg-purple-50\",\n        borderColor: \"border-purple-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"2\",\n        title: \"Web Design\",\n        description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n        features: [\n            \"Responsive Web Design\",\n            \"UI/UX Design\",\n            \"E-commerce Websites\",\n            \"Landing Pages\",\n            \"Web Applications\",\n            \"Website Maintenance\"\n        ],\n        tools: [\n            \"React\",\n            \"Next.js\",\n            \"Tailwind CSS\",\n            \"WordPress\"\n        ],\n        icon: \"ComputerDesktopIcon\",\n        color: \"from-blue-500 to-blue-600\",\n        bgColor: \"bg-blue-50\",\n        borderColor: \"border-blue-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"3\",\n        title: \"Photography\",\n        description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n        features: [\n            \"Product Photography\",\n            \"Portrait Photography\",\n            \"Event Photography\",\n            \"Commercial Photography\",\n            \"Photo Editing & Retouching\",\n            \"Studio & Location Shoots\"\n        ],\n        tools: [\n            \"Canon EOS\",\n            \"Lightroom\",\n            \"Photoshop\",\n            \"Studio Lighting\"\n        ],\n        icon: \"CameraIcon\",\n        color: \"from-green-500 to-green-600\",\n        bgColor: \"bg-green-50\",\n        borderColor: \"border-green-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: \"4\",\n        title: \"Video Editing\",\n        description: \"Creating engaging video content with professional editing and motion graphics\",\n        features: [\n            \"Video Production\",\n            \"Motion Graphics\",\n            \"Color Correction\",\n            \"Audio Enhancement\",\n            \"Social Media Videos\",\n            \"Commercial Videos\"\n        ],\n        tools: [\n            \"After Effects\",\n            \"Premiere Pro\",\n            \"DaVinci Resolve\",\n            \"Final Cut Pro\"\n        ],\n        icon: \"VideoCameraIcon\",\n        color: \"from-red-500 to-red-600\",\n        bgColor: \"bg-red-50\",\n        borderColor: \"border-red-200\",\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\nconst ServicesProvider = ({ children })=>{\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize services from localStorage or use defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadServices = ()=>{\n            try {\n                const savedServices = localStorage.getItem(\"elishop_services\");\n                if (savedServices) {\n                    const parsedServices = JSON.parse(savedServices);\n                    setServices(parsedServices);\n                } else {\n                    setServices(defaultServices);\n                    localStorage.setItem(\"elishop_services\", JSON.stringify(defaultServices));\n                }\n            } catch (error) {\n                console.error(\"Error loading services:\", error);\n                setServices(defaultServices);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadServices();\n    }, []);\n    // Save services to localStorage whenever services change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && services.length > 0) {\n            localStorage.setItem(\"elishop_services\", JSON.stringify(services));\n        }\n    }, [\n        services,\n        loading\n    ]);\n    const activeServices = services.filter((service)=>service.isActive);\n    const addService = (serviceData)=>{\n        const now = new Date().toISOString();\n        const newService = {\n            ...serviceData,\n            id: Date.now().toString(),\n            createdAt: now,\n            updatedAt: now\n        };\n        setServices((prev)=>[\n                ...prev,\n                newService\n            ]);\n    };\n    const updateService = (id, serviceData)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    ...serviceData,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const deleteService = (id)=>{\n        setServices((prev)=>prev.filter((service)=>service.id !== id));\n    };\n    const toggleServiceActive = (id)=>{\n        setServices((prev)=>prev.map((service)=>service.id === id ? {\n                    ...service,\n                    isActive: !service.isActive,\n                    updatedAt: new Date().toISOString()\n                } : service));\n    };\n    const getService = (id)=>{\n        return services.find((service)=>service.id === id);\n    };\n    const value = {\n        services,\n        activeServices,\n        addService,\n        updateService,\n        deleteService,\n        toggleServiceActive,\n        getService,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ServicesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\ServicesContext.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\nconst useServices = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServicesContext);\n    if (context === undefined) {\n        throw new Error(\"useServices must be used within a ServicesProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ServicesContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ServicesContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/WallpaperContext.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/WallpaperContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WallpaperProvider: () => (/* binding */ WallpaperProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useWallpapers: () => (/* binding */ useWallpapers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/wallpapers */ \"(ssr)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ WallpaperProvider,useWallpapers,default auto */ \n\n\nconst WallpaperContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Convert Product to AdminWallpaper format\nconst convertToAdminWallpaper = (product)=>{\n    return {\n        id: product.id,\n        title: product.title,\n        description: product.description,\n        category: product.wallpaperData?.style || \"Abstract\",\n        tags: product.tags,\n        price: product.price,\n        originalPrice: product.originalPrice,\n        featured: product.featured,\n        downloads: product.wallpaperData?.downloadCount || 0,\n        imageUrl: product.images[0],\n        thumbnailUrl: product.wallpaperData?.watermarkedPreview || product.images[0],\n        resolutions: {\n            desktop: product.wallpaperData?.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\") ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").downloadUrl\n            } : {\n                width: 1920,\n                height: 1080,\n                url: \"/api/placeholder/1920/1080\"\n            },\n            mobile: product.wallpaperData?.resolutions.find((r)=>r.label === \"Mobile\") ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").downloadUrl\n            } : {\n                width: 1080,\n                height: 1920,\n                url: \"/api/placeholder/1080/1920\"\n            },\n            tablet: product.wallpaperData?.resolutions.find((r)=>r.label === \"Tablet\") ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").downloadUrl\n            } : {\n                width: 1536,\n                height: 2048,\n                url: \"/api/placeholder/1536/2048\"\n            }\n        },\n        createdAt: product.createdAt,\n        updatedAt: product.updatedAt\n    };\n};\nconst WallpaperProvider = ({ children })=>{\n    const [wallpapers, setWallpapers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize wallpapers from localStorage or use defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadWallpapers = ()=>{\n            try {\n                const savedWallpapers = localStorage.getItem(\"elishop_wallpapers\");\n                if (savedWallpapers) {\n                    const parsedWallpapers = JSON.parse(savedWallpapers);\n                    setWallpapers(parsedWallpapers);\n                } else {\n                    // Convert default wallpapers data to admin format\n                    const adminWallpapers = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpapersData.map(convertToAdminWallpaper);\n                    setWallpapers(adminWallpapers);\n                    localStorage.setItem(\"elishop_wallpapers\", JSON.stringify(adminWallpapers));\n                }\n            } catch (error) {\n                console.error(\"Error loading wallpapers:\", error);\n                const adminWallpapers = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpapersData.map(convertToAdminWallpaper);\n                setWallpapers(adminWallpapers);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadWallpapers();\n    }, []);\n    // Save wallpapers to localStorage whenever wallpapers change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && wallpapers.length > 0) {\n            localStorage.setItem(\"elishop_wallpapers\", JSON.stringify(wallpapers));\n        }\n    }, [\n        wallpapers,\n        loading\n    ]);\n    const addWallpaper = (wallpaperData)=>{\n        const now = new Date().toISOString();\n        const newWallpaper = {\n            ...wallpaperData,\n            id: Date.now().toString(),\n            downloads: 0,\n            createdAt: now,\n            updatedAt: now\n        };\n        setWallpapers((prev)=>[\n                newWallpaper,\n                ...prev\n            ]);\n    };\n    const updateWallpaper = (id, wallpaperData)=>{\n        setWallpapers((prev)=>prev.map((wallpaper)=>wallpaper.id === id ? {\n                    ...wallpaper,\n                    ...wallpaperData,\n                    updatedAt: new Date().toISOString()\n                } : wallpaper));\n    };\n    const deleteWallpaper = (id)=>{\n        setWallpapers((prev)=>prev.filter((wallpaper)=>wallpaper.id !== id));\n    };\n    const toggleFeatured = (id)=>{\n        setWallpapers((prev)=>prev.map((wallpaper)=>wallpaper.id === id ? {\n                    ...wallpaper,\n                    featured: !wallpaper.featured,\n                    updatedAt: new Date().toISOString()\n                } : wallpaper));\n    };\n    const getWallpaper = (id)=>{\n        return wallpapers.find((wallpaper)=>wallpaper.id === id);\n    };\n    const value = {\n        wallpapers,\n        addWallpaper,\n        updateWallpaper,\n        deleteWallpaper,\n        toggleFeatured,\n        getWallpaper,\n        loading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\WallpaperContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, undefined);\n};\nconst useWallpapers = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WallpaperContext);\n    if (context === undefined) {\n        throw new Error(\"useWallpapers must be used within a WallpaperProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WallpaperContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/WallpaperContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/wallpapers.ts":
/*!********************************!*\
  !*** ./src/data/wallpapers.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertToAdminWallpaper: () => (/* binding */ convertToAdminWallpaper),\n/* harmony export */   getFeaturedWallpapers: () => (/* binding */ getFeaturedWallpapers),\n/* harmony export */   getTotalDownloads: () => (/* binding */ getTotalDownloads),\n/* harmony export */   getWallpaperById: () => (/* binding */ getWallpaperById),\n/* harmony export */   getWallpapersByCategory: () => (/* binding */ getWallpapersByCategory),\n/* harmony export */   wallpaperCategories: () => (/* binding */ wallpaperCategories),\n/* harmony export */   wallpapersData: () => (/* binding */ wallpapersData)\n/* harmony export */ });\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/product */ \"(ssr)/./src/types/product.ts\");\n\n// Shared wallpapers data that both admin and main site will use\nconst wallpapersData = [\n    {\n        id: \"1\",\n        title: \"Abstract Neon Waves\",\n        description: \"Vibrant neon waves flowing across a dark background, perfect for modern setups\",\n        price: 4.99,\n        originalPrice: 7.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"abstract\",\n            \"neon\",\n            \"waves\",\n            \"modern\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"15-25 MB\",\n        createdAt: \"2024-01-01T00:00:00Z\",\n        updatedAt: \"2024-01-01T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"25 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"18 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1536,\n                    height: 2048,\n                    label: \"Tablet\",\n                    downloadUrl: \"/downloads/tablet\",\n                    fileSize: \"10 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"8 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF00FF\",\n                \"#00FFFF\",\n                \"#FF6B6B\",\n                \"#4ECDC4\",\n                \"#1A1A1A\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.ABSTRACT,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 1250\n        }\n    },\n    {\n        id: \"2\",\n        title: \"Minimalist Mountain Range\",\n        description: \"Clean, minimalist mountain silhouettes with gradient sky\",\n        price: 3.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"minimalist\",\n            \"mountains\",\n            \"nature\",\n            \"gradient\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"10-20 MB\",\n        createdAt: \"2024-01-02T00:00:00Z\",\n        updatedAt: \"2024-01-02T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"20 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"10 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#87CEEB\",\n                \"#FFB6C1\",\n                \"#DDA0DD\",\n                \"#F0F8FF\",\n                \"#2F4F4F\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.MINIMALIST,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.ULTRAWIDE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 890\n        }\n    },\n    {\n        id: \"3\",\n        title: \"Gaming RGB Setup\",\n        description: \"Dynamic RGB lighting effects perfect for gaming setups\",\n        price: 5.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"gaming\",\n            \"rgb\",\n            \"neon\",\n            \"tech\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"20-30 MB\",\n        createdAt: \"2024-01-03T00:00:00Z\",\n        updatedAt: \"2024-01-03T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"30 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"22 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 3440,\n                    height: 1440,\n                    label: \"Ultrawide\",\n                    downloadUrl: \"/downloads/ultrawide\",\n                    fileSize: \"25 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF0080\",\n                \"#00FF80\",\n                \"#8000FF\",\n                \"#FF8000\",\n                \"#000000\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.GAMING,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.ULTRAWIDE\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 2100\n        }\n    },\n    {\n        id: \"4\",\n        title: \"Nature Forest Path\",\n        description: \"Serene forest path with morning sunlight filtering through trees\",\n        price: 2.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"nature\",\n            \"forest\",\n            \"peaceful\",\n            \"photography\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\"\n        ],\n        fileSize: \"12-18 MB\",\n        createdAt: \"2024-01-04T00:00:00Z\",\n        updatedAt: \"2024-01-04T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"18 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"6 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#228B22\",\n                \"#8FBC8F\",\n                \"#F5DEB3\",\n                \"#DEB887\",\n                \"#654321\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.NATURE,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 756\n        }\n    },\n    {\n        id: \"5\",\n        title: \"Geometric Patterns\",\n        description: \"Modern geometric patterns in vibrant colors\",\n        price: 3.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"geometric\",\n            \"patterns\",\n            \"vibrant\",\n            \"modern\"\n        ],\n        inStock: true,\n        featured: true,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"15-22 MB\",\n        createdAt: \"2024-01-05T00:00:00Z\",\n        updatedAt: \"2024-01-05T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"22 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"16 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"8 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#FF6B6B\",\n                \"#4ECDC4\",\n                \"#45B7D1\",\n                \"#96CEB4\",\n                \"#FFEAA7\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.GEOMETRIC,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 1420\n        }\n    },\n    {\n        id: \"6\",\n        title: \"Dark Aesthetic\",\n        description: \"Sleek dark theme wallpaper with subtle textures\",\n        price: 2.99,\n        category: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductCategory.WALLPAPERS,\n        type: _types_product__WEBPACK_IMPORTED_MODULE_0__.ProductType.DIGITAL,\n        images: [\n            \"/api/placeholder/400/600\"\n        ],\n        tags: [\n            \"dark\",\n            \"aesthetic\",\n            \"minimal\",\n            \"texture\"\n        ],\n        inStock: true,\n        featured: false,\n        isDigital: true,\n        fileFormat: [\n            \"JPG\",\n            \"PNG\"\n        ],\n        fileSize: \"8-15 MB\",\n        createdAt: \"2024-01-06T00:00:00Z\",\n        updatedAt: \"2024-01-06T00:00:00Z\",\n        wallpaperData: {\n            resolutions: [\n                {\n                    width: 3840,\n                    height: 2160,\n                    label: \"4K\",\n                    downloadUrl: \"/downloads/4k\",\n                    fileSize: \"15 MB\"\n                },\n                {\n                    width: 2560,\n                    height: 1440,\n                    label: \"1440p\",\n                    downloadUrl: \"/downloads/1440p\",\n                    fileSize: \"12 MB\"\n                },\n                {\n                    width: 1920,\n                    height: 1080,\n                    label: \"1080p\",\n                    downloadUrl: \"/downloads/1080p\",\n                    fileSize: \"8 MB\"\n                },\n                {\n                    width: 1080,\n                    height: 2340,\n                    label: \"Mobile\",\n                    downloadUrl: \"/downloads/mobile\",\n                    fileSize: \"5 MB\"\n                }\n            ],\n            aspectRatio: \"16:9\",\n            colorPalette: [\n                \"#1A1A1A\",\n                \"#2D2D2D\",\n                \"#404040\",\n                \"#666666\",\n                \"#808080\"\n            ],\n            style: _types_product__WEBPACK_IMPORTED_MODULE_0__.WallpaperStyle.DARK,\n            deviceCompatibility: [\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.DESKTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.LAPTOP,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.MOBILE,\n                _types_product__WEBPACK_IMPORTED_MODULE_0__.DeviceType.TABLET\n            ],\n            previewUrl: \"/api/placeholder/800/600\",\n            watermarkedPreview: \"/api/placeholder/400/600\",\n            downloadCount: 980\n        }\n    }\n];\n// Helper functions for wallpaper management\nconst getWallpaperById = (id)=>{\n    return wallpapersData.find((wallpaper)=>wallpaper.id === id);\n};\nconst getFeaturedWallpapers = ()=>{\n    return wallpapersData.filter((wallpaper)=>wallpaper.featured);\n};\nconst getWallpapersByCategory = (style)=>{\n    return wallpapersData.filter((wallpaper)=>wallpaper.wallpaperData?.style === style);\n};\nconst getTotalDownloads = ()=>{\n    return wallpapersData.reduce((total, wallpaper)=>total + (wallpaper.wallpaperData?.downloadCount || 0), 0);\n};\n// Categories for filtering\nconst wallpaperCategories = [\n    \"All\",\n    \"Abstract\",\n    \"Nature\",\n    \"Geometric\",\n    \"Minimal\",\n    \"Dark\",\n    \"Light\",\n    \"Gaming\"\n];\n// Convert Product to admin Wallpaper format\nconst convertToAdminWallpaper = (product)=>{\n    return {\n        id: product.id,\n        title: product.title,\n        description: product.description,\n        category: product.wallpaperData?.style || \"Abstract\",\n        tags: product.tags,\n        price: product.price,\n        originalPrice: product.originalPrice,\n        featured: product.featured,\n        downloads: product.wallpaperData?.downloadCount || 0,\n        imageUrl: product.images[0],\n        thumbnailUrl: product.wallpaperData?.watermarkedPreview || product.images[0],\n        resolutions: {\n            desktop: product.wallpaperData?.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\") ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"1080p\" || r.label === \"1440p\").downloadUrl\n            } : {\n                width: 1920,\n                height: 1080,\n                url: \"/api/placeholder/1920/1080\"\n            },\n            mobile: product.wallpaperData?.resolutions.find((r)=>r.label === \"Mobile\") ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Mobile\").downloadUrl\n            } : {\n                width: 1080,\n                height: 1920,\n                url: \"/api/placeholder/1080/1920\"\n            },\n            tablet: product.wallpaperData?.resolutions.find((r)=>r.label === \"Tablet\") ? {\n                width: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").width,\n                height: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").height,\n                url: product.wallpaperData.resolutions.find((r)=>r.label === \"Tablet\").downloadUrl\n            } : {\n                width: 1536,\n                height: 2048,\n                url: \"/api/placeholder/1536/2048\"\n            }\n        },\n        createdAt: product.createdAt,\n        updatedAt: product.updatedAt\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/wallpapers.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useCart.ts":
/*!******************************!*\
  !*** ./src/hooks/useCart.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCartState: () => (/* binding */ useCartState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useCartState auto */ \nconst useCartState = ()=>{\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    // Load cart from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const savedCart = localStorage.getItem(\"cart\");\n        if (savedCart) {\n            try {\n                setItems(JSON.parse(savedCart));\n            } catch (error) {\n                console.error(\"Error loading cart from localStorage:\", error);\n            }\n        }\n    }, []);\n    // Save cart to localStorage whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        localStorage.setItem(\"cart\", JSON.stringify(items));\n    }, [\n        items\n    ]);\n    const itemCount = items.reduce((total, item)=>total + item.quantity, 0);\n    const totalPrice = items.reduce((total, item)=>total + item.price * item.quantity, 0);\n    const addItem = (newItem)=>{\n        setItems((currentItems)=>{\n            const existingItem = currentItems.find((item)=>item.id === newItem.id);\n            if (existingItem) {\n                return currentItems.map((item)=>item.id === newItem.id ? {\n                        ...item,\n                        quantity: item.quantity + 1\n                    } : item);\n            } else {\n                return [\n                    ...currentItems,\n                    {\n                        ...newItem,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const removeItem = (id)=>{\n        setItems((currentItems)=>currentItems.filter((item)=>item.id !== id));\n    };\n    const updateQuantity = (id, quantity)=>{\n        if (quantity <= 0) {\n            removeItem(id);\n            return;\n        }\n        setItems((currentItems)=>currentItems.map((item)=>item.id === id ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    const clearCart = ()=>{\n        setItems([]);\n    };\n    return {\n        items,\n        itemCount,\n        totalPrice,\n        addItem,\n        removeItem,\n        updateQuantity,\n        clearCart\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCart.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/product.ts":
/*!******************************!*\
  !*** ./src/types/product.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeviceType: () => (/* binding */ DeviceType),\n/* harmony export */   OrderStatus: () => (/* binding */ OrderStatus),\n/* harmony export */   PaymentStatus: () => (/* binding */ PaymentStatus),\n/* harmony export */   ProductCategory: () => (/* binding */ ProductCategory),\n/* harmony export */   ProductType: () => (/* binding */ ProductType),\n/* harmony export */   WallpaperStyle: () => (/* binding */ WallpaperStyle),\n/* harmony export */   getDeviceTypeLabel: () => (/* binding */ getDeviceTypeLabel),\n/* harmony export */   getProductCategoryLabel: () => (/* binding */ getProductCategoryLabel),\n/* harmony export */   getWallpaperStyleLabel: () => (/* binding */ getWallpaperStyleLabel)\n/* harmony export */ });\nvar ProductCategory;\n(function(ProductCategory) {\n    // Digital Categories\n    ProductCategory[\"WALLPAPERS\"] = \"wallpapers\";\n    ProductCategory[\"DIGITAL_ART\"] = \"digital-art\";\n    ProductCategory[\"TEMPLATES\"] = \"templates\";\n    ProductCategory[\"FONTS\"] = \"fonts\";\n    // Physical Categories\n    ProductCategory[\"CLOTHING\"] = \"clothing\";\n    ProductCategory[\"ACCESSORIES\"] = \"accessories\";\n    ProductCategory[\"HOME_DECOR\"] = \"home-decor\";\n    ProductCategory[\"STATIONERY\"] = \"stationery\";\n    // Services\n    ProductCategory[\"CONSULTING\"] = \"consulting\";\n    ProductCategory[\"DESIGN_SERVICES\"] = \"design-services\";\n})(ProductCategory || (ProductCategory = {}));\nvar ProductType;\n(function(ProductType) {\n    ProductType[\"DIGITAL\"] = \"digital\";\n    ProductType[\"PHYSICAL\"] = \"physical\";\n    ProductType[\"SERVICE\"] = \"service\";\n})(ProductType || (ProductType = {}));\nvar WallpaperStyle;\n(function(WallpaperStyle) {\n    WallpaperStyle[\"ABSTRACT\"] = \"abstract\";\n    WallpaperStyle[\"NATURE\"] = \"nature\";\n    WallpaperStyle[\"MINIMALIST\"] = \"minimalist\";\n    WallpaperStyle[\"GAMING\"] = \"gaming\";\n    WallpaperStyle[\"ANIME\"] = \"anime\";\n    WallpaperStyle[\"PHOTOGRAPHY\"] = \"photography\";\n    WallpaperStyle[\"GRADIENT\"] = \"gradient\";\n    WallpaperStyle[\"PATTERN\"] = \"pattern\";\n    WallpaperStyle[\"DARK\"] = \"dark\";\n    WallpaperStyle[\"LIGHT\"] = \"light\";\n    WallpaperStyle[\"NEON\"] = \"neon\";\n    WallpaperStyle[\"VINTAGE\"] = \"vintage\";\n    WallpaperStyle[\"MODERN\"] = \"modern\";\n    WallpaperStyle[\"ARTISTIC\"] = \"artistic\";\n})(WallpaperStyle || (WallpaperStyle = {}));\nvar DeviceType;\n(function(DeviceType) {\n    DeviceType[\"DESKTOP\"] = \"desktop\";\n    DeviceType[\"LAPTOP\"] = \"laptop\";\n    DeviceType[\"TABLET\"] = \"tablet\";\n    DeviceType[\"MOBILE\"] = \"mobile\";\n    DeviceType[\"ULTRAWIDE\"] = \"ultrawide\";\n    DeviceType[\"DUAL_MONITOR\"] = \"dual-monitor\";\n})(DeviceType || (DeviceType = {}));\nvar OrderStatus;\n(function(OrderStatus) {\n    OrderStatus[\"PENDING\"] = \"pending\";\n    OrderStatus[\"PROCESSING\"] = \"processing\";\n    OrderStatus[\"SHIPPED\"] = \"shipped\";\n    OrderStatus[\"DELIVERED\"] = \"delivered\";\n    OrderStatus[\"CANCELLED\"] = \"cancelled\";\n    OrderStatus[\"REFUNDED\"] = \"refunded\";\n})(OrderStatus || (OrderStatus = {}));\nvar PaymentStatus;\n(function(PaymentStatus) {\n    PaymentStatus[\"PENDING\"] = \"pending\";\n    PaymentStatus[\"PAID\"] = \"paid\";\n    PaymentStatus[\"FAILED\"] = \"failed\";\n    PaymentStatus[\"REFUNDED\"] = \"refunded\";\n})(PaymentStatus || (PaymentStatus = {}));\n// Utility functions\nconst getProductCategoryLabel = (category)=>{\n    const labels = {\n        [\"wallpapers\"]: \"Wallpapers\",\n        [\"digital-art\"]: \"Digital Art\",\n        [\"templates\"]: \"Templates\",\n        [\"fonts\"]: \"Fonts\",\n        [\"clothing\"]: \"Clothing\",\n        [\"accessories\"]: \"Accessories\",\n        [\"home-decor\"]: \"Home Decor\",\n        [\"stationery\"]: \"Stationery\",\n        [\"consulting\"]: \"Consulting\",\n        [\"design-services\"]: \"Design Services\"\n    };\n    return labels[category];\n};\nconst getWallpaperStyleLabel = (style)=>{\n    const labels = {\n        [\"abstract\"]: \"Abstract\",\n        [\"nature\"]: \"Nature\",\n        [\"minimalist\"]: \"Minimalist\",\n        [\"gaming\"]: \"Gaming\",\n        [\"anime\"]: \"Anime\",\n        [\"photography\"]: \"Photography\",\n        [\"gradient\"]: \"Gradient\",\n        [\"pattern\"]: \"Pattern\",\n        [\"dark\"]: \"Dark\",\n        [\"light\"]: \"Light\",\n        [\"neon\"]: \"Neon\",\n        [\"vintage\"]: \"Vintage\",\n        [\"modern\"]: \"Modern\",\n        [\"artistic\"]: \"Artistic\"\n    };\n    return labels[style];\n};\nconst getDeviceTypeLabel = (device)=>{\n    const labels = {\n        [\"desktop\"]: \"Desktop\",\n        [\"laptop\"]: \"Laptop\",\n        [\"tablet\"]: \"Tablet\",\n        [\"mobile\"]: \"Mobile\",\n        [\"ultrawide\"]: \"Ultrawide\",\n        [\"dual-monitor\"]: \"Dual Monitor\"\n    };\n    return labels[device];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/product.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5edbed136700\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxpc2hvcC1wb3J0Zm9saW8vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzVlYzIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZWRiZWQxMzY3MDBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CartContext */ \"(rsc)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ServicesContext */ \"(rsc)/./src/contexts/ServicesContext.tsx\");\n/* harmony import */ var _contexts_WallpaperContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/WallpaperContext */ \"(rsc)/./src/contexts/WallpaperContext.tsx\");\n/* harmony import */ var _components_layout_LayoutWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/LayoutWrapper */ \"(rsc)/./src/components/layout/LayoutWrapper.tsx\");\n\n\n\n// import { AuthProvider } from '@/contexts/AuthContext'\n\n\n\n\nconst metadata = {\n    title: \"Elias Mwangi - Creative Professional | Portfolio & Shop\",\n    description: \"Creative professional specializing in graphic design, web design, photography, and video editing. Bringing ideas to life through visually appealing and functional designs.\",\n    keywords: [\n        \"graphic design\",\n        \"web design\",\n        \"photography\",\n        \"video editing\",\n        \"creative professional\",\n        \"Elias Mwangi\",\n        \"portfolio\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ServicesContext__WEBPACK_IMPORTED_MODULE_3__.ServicesProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_WallpaperContext__WEBPACK_IMPORTED_MODULE_4__.WallpaperProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_LayoutWrapper__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/shop/page.tsx":
/*!*******************************!*\
  !*** ./src/app/shop/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\app\shop\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/LayoutWrapper.tsx":
/*!*************************************************!*\
  !*** ./src/components/layout/LayoutWrapper.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\components\layout\LayoutWrapper.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e1),
/* harmony export */   useCart: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\CartContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\CartContext.tsx#useCart`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\CartContext.tsx#CartProvider`);


/***/ }),

/***/ "(rsc)/./src/contexts/ServicesContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/ServicesContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   ServicesProvider: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useServices: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\ServicesContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\ServicesContext.tsx#ServicesProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\ServicesContext.tsx#useServices`);


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/contexts/WallpaperContext.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/WallpaperContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   WallpaperProvider: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useWallpapers: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\WallpaperContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\WallpaperContext.tsx#WallpaperProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\WallpaperContext.tsx#useWallpapers`);


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-icons","vendor-chunks/framer-motion","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fpage&page=%2Fshop%2Fpage&appPaths=%2Fshop%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();