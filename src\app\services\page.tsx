'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useServices } from '@/contexts/ServicesContext';
import {
  PaintBrushIcon,
  ComputerDesktopIcon,
  CameraIcon,
  VideoCameraIcon,
  CheckIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

// Services are now managed through ServicesContext

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6
    }
  }
};

const ServicesPage: React.FC = () => {
  const { activeServices, loading } = useServices();

  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'PaintBrushIcon':
        return PaintBrushIcon;
      case 'ComputerDesktopIcon':
        return ComputerDesktopIcon;
      case 'CameraIcon':
        return CameraIcon;
      case 'VideoCameraIcon':
        return VideoCameraIcon;
      default:
        return PaintBrushIcon;
    }
  };

  const openWhatsApp = (serviceName: string) => {
    const message = encodeURIComponent(
      `Hi Elias! 👋\n\nI'm interested in your ${serviceName} services. Please let me know about pricing and availability.\n\nThank you!`
    );
    const whatsappUrl = `https://wa.me/254703973225?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading services...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Creative <span 
                className="inline-block"
                style={{
                  background: 'linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >Services</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Professional creative services to bring your ideas to life. From design to development, 
              photography to video editing - I've got you covered.
            </p>
            <motion.button
              onClick={() => openWhatsApp('general')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto"
            >
              <ChatBubbleLeftRightIcon className="w-6 h-6" />
              <span>Get Started on WhatsApp</span>
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {activeServices.map((service) => {
              const IconComponent = getIconComponent(service.icon);
              return (
                <motion.div
                  key={service.id}
                  variants={itemVariants}
                  className={`${service.bgColor} ${service.borderColor} border-2 rounded-2xl p-8 hover:shadow-xl transition-all duration-300`}
                >
                  {/* Service Header */}
                  <div className="flex items-center space-x-4 mb-6">
                    <div className={`bg-gradient-to-r ${service.color} p-3 rounded-xl`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900">{service.title}</h3>
                      <p className="text-gray-600">{service.description}</p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">What I Offer:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {service.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckIcon className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tools */}
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">Tools & Technologies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.tools.map((tool, index) => (
                        <span
                          key={index}
                          className="bg-white px-3 py-1 rounded-full text-sm font-medium text-gray-700 border border-gray-200"
                        >
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Contact Button */}
                  <motion.button
                    onClick={() => openWhatsApp(service.title)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full bg-gradient-to-r ${service.color} text-white py-3 px-6 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2`}
                  >
                    <ChatBubbleLeftRightIcon className="w-5 h-5" />
                    <span>Get Quote via WhatsApp</span>
                  </motion.button>
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let's discuss your requirements and bring your vision to life. 
              Contact me on WhatsApp for a quick response!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                onClick={() => openWhatsApp('project consultation')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center space-x-2"
              >
                <ChatBubbleLeftRightIcon className="w-6 h-6" />
                <span>WhatsApp: +254 703 973 225</span>
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;
