'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { fadeInUp, staggerContainer } from '@/lib/utils'

const categories = ['All', 'Web Design', 'E-commerce', 'Mobile App', 'Branding']

const portfolioItems = [
  {
    id: 1,
    title: 'Modern E-commerce Platform',
    category: 'E-commerce',
    image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=500&h=300&fit=crop',
    description: 'A sleek online store with advanced features',
    tags: ['React', 'Next.js', 'Stripe'],
  },
  {
    id: 2,
    title: 'Creative Portfolio Website',
    category: 'Web Design',
    image: 'https://images.unsplash.com/photo-*************-a241de8bcf5d?w=500&h=300&fit=crop',
    description: 'Stunning portfolio with smooth animations',
    tags: ['Design', 'Animation', 'UI/UX'],
  },
  {
    id: 3,
    title: 'Mobile Banking App',
    category: 'Mobile App',
    image: 'https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=500&h=300&fit=crop',
    description: 'Secure and user-friendly banking solution',
    tags: ['React Native', 'Security', 'FinTech'],
  },
  {
    id: 4,
    title: 'Brand Identity Design',
    category: 'Branding',
    image: 'https://images.unsplash.com/photo-**********-2526d30994b5?w=500&h=300&fit=crop',
    description: 'Complete brand identity and guidelines',
    tags: ['Logo', 'Brand', 'Identity'],
  },
  {
    id: 5,
    title: 'Restaurant Website',
    category: 'Web Design',
    image: 'https://images.unsplash.com/photo-*************-338989a2e8c0?w=500&h=300&fit=crop',
    description: 'Appetizing design for food business',
    tags: ['Restaurant', 'Food', 'Booking'],
  },
  {
    id: 6,
    title: 'Fashion Store',
    category: 'E-commerce',
    image: 'https://images.unsplash.com/photo-*************-64674bd600d8?w=500&h=300&fit=crop',
    description: 'Trendy fashion e-commerce platform',
    tags: ['Fashion', 'Shopping', 'Style'],
  },
]

export default function Portfolio() {
  const [activeCategory, setActiveCategory] = useState('All')
  const [hoveredItem, setHoveredItem] = useState<number | null>(null)

  const filteredItems = activeCategory === 'All' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === activeCategory)

  return (
    <section id="portfolio" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={fadeInUp}
            className="text-3xl md:text-4xl font-bold text-gray-800 mb-4"
          >
            Our <span className="gradient-text">Portfolio</span>
          </motion.h2>
          <motion.p
            variants={fadeInUp}
            className="text-lg text-gray-600 max-w-2xl mx-auto"
          >
            Explore our latest projects showcasing creativity, innovation, and technical excellence
          </motion.p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          variants={fadeInUp}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <motion.button
              key={category}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-gray-50 shadow-md hover:shadow-lg'
              }`}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>

        {/* Portfolio Grid */}
        <motion.div
          layout
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
              onHoverStart={() => setHoveredItem(item.id)}
              onHoverEnd={() => setHoveredItem(null)}
              className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300"
            >
              {/* Image */}
              <div className="relative overflow-hidden">
                <motion.img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: hoveredItem === item.id ? 1 : 0 }}
                  className="absolute inset-0 bg-gradient-to-t from-primary-600/80 to-transparent flex items-end justify-start p-6"
                >
                  <motion.button
                    initial={{ y: 20 }}
                    animate={{ y: hoveredItem === item.id ? 0 : 20 }}
                    className="bg-white text-primary-600 px-4 py-2 rounded-lg font-medium hover:bg-primary-50 transition-colors duration-200"
                  >
                    View Project
                  </motion.button>
                </motion.div>
              </div>

              {/* Content */}
              <div className="p-6 space-y-4">
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {item.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-gradient-to-r from-primary-50 to-secondary-50 text-primary-700 text-xs font-medium rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Category Badge */}
                <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {item.category}
                  </span>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 btn-animate"
          >
            Load More Projects
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
