"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/wallpapers/page",{

/***/ "(app-pages-browser)/./src/app/admin/wallpapers/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/admin/wallpapers/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DeviceTabletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/wallpapers */ \"(app-pages-browser)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst WallpapersManagement = ()=>{\n    _s();\n    const [wallpapers, setWallpapers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [wallpaperToDelete, setWallpaperToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [wallpaperToEdit, setWallpaperToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [wallpaperToView, setWallpaperToView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpaperCategories.map((cat)=>cat.toLowerCase());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Convert shared wallpapers data to admin format\n        const adminWallpapers = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpapersData.map(_data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.convertToAdminWallpaper);\n        setWallpapers(adminWallpapers);\n        setLoading(false);\n    }, []);\n    const filteredWallpapers = wallpapers.filter((wallpaper)=>{\n        const matchesSearch = wallpaper.title.toLowerCase().includes(searchTerm.toLowerCase()) || wallpaper.description.toLowerCase().includes(searchTerm.toLowerCase()) || wallpaper.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || wallpaper.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleDeleteWallpaper = (id)=>{\n        setWallpaperToDelete(id);\n        setShowDeleteModal(true);\n    };\n    const confirmDelete = ()=>{\n        if (wallpaperToDelete) {\n            setWallpapers((prev)=>prev.filter((w)=>w.id !== wallpaperToDelete));\n            setWallpaperToDelete(null);\n            setShowDeleteModal(false);\n        }\n    };\n    const toggleFeatured = (id)=>{\n        setWallpapers((prev)=>prev.map((w)=>w.id === id ? {\n                    ...w,\n                    featured: !w.featured\n                } : w));\n    };\n    const handleViewWallpaper = (wallpaper)=>{\n        setWallpaperToView(wallpaper);\n        setShowViewModal(true);\n    };\n    const handleEditWallpaper = (wallpaper)=>{\n        setWallpaperToEdit(wallpaper);\n        setShowEditModal(true);\n    };\n    const handleUpdateWallpaper = (updatedWallpaper)=>{\n        setWallpapers((prev)=>prev.map((w)=>w.id === updatedWallpaper.id ? updatedWallpaper : w));\n        setShowEditModal(false);\n        setWallpaperToEdit(null);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Wallpapers Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage your wallpaper collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 sm:mt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUploadModal(true),\n                            className: \"text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center space-x-2\",\n                            style: {\n                                background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Add Wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Wallpapers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Downloads\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.reduce((sum, w)=>sum + w.downloads, 0).toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.filter((w)=>w.featured).length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 text-yellow-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: categories.length - 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedCategory(category),\n                                    className: \"px-4 py-2 rounded-lg font-medium transition-all duration-200 \".concat(selectedCategory === category ? \"text-white shadow-lg\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                    style: selectedCategory === category ? {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                    } : {},\n                                    children: category === \"all\" ? \"All Categories\" : category\n                                }, category, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    children: filteredWallpapers.map((wallpaper)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            layout: true,\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-16 h-16 text-primary-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        wallpaper.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 left-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                style: {\n                                                    background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                                },\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleFeatured(wallpaper.id),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: wallpaper.featured ? \"Remove from featured\" : \"Add to featured\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(wallpaper.featured ? \"text-purple-600\" : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleViewWallpaper(wallpaper),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: \"View wallpaper\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditWallpaper(wallpaper),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: \"Edit wallpaper\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteWallpaper(wallpaper.id),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-2 left-2 flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-1\",\n                                            children: wallpaper.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: wallpaper.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: [\n                                                wallpaper.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n                                                        children: tag\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                wallpaper.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        wallpaper.tags.length - 3\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                wallpaper.downloads,\n                                                                \" downloads\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                wallpaper.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: wallpaper.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, wallpaper.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No wallpapers found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Try adjusting your search or filter criteria\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperUploadModal, {\n                isOpen: showUploadModal,\n                onClose: ()=>setShowUploadModal(false),\n                onUpload: (wallpaper)=>{\n                    setWallpapers((prev)=>[\n                            wallpaper,\n                            ...prev\n                        ]);\n                    setShowUploadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined),\n            wallpaperToEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperEditModal, {\n                isOpen: showEditModal,\n                wallpaper: wallpaperToEdit,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setWallpaperToEdit(null);\n                },\n                onUpdate: handleUpdateWallpaper\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, undefined),\n            wallpaperToView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperViewModal, {\n                isOpen: showViewModal,\n                wallpaper: wallpaperToView,\n                onClose: ()=>{\n                    setShowViewModal(false);\n                    setWallpaperToView(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, undefined),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Delete Wallpaper\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to delete this wallpaper? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDelete,\n                                    className: \"flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors duration-200\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 383,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WallpapersManagement, \"QAM2Au/tK8ESN4/9KmUcYgjp1zI=\");\n_c = WallpapersManagement;\n// Wallpaper Upload Modal Component\nconst WallpaperUploadModal = (param)=>{\n    let { isOpen, onClose, onUpload } = param;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"Abstract\",\n        tags: \"\",\n        price: \"\",\n        originalPrice: \"\",\n        featured: false\n    });\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadStatus, setUploadStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const fileInputRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setUploading(true);\n        setUploadStatus(\"idle\");\n        try {\n            // Simulate upload\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const newWallpaper = {\n                id: Date.now().toString(),\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                tags: formData.tags.split(\",\").map((tag)=>tag.trim()).filter(Boolean),\n                price: parseFloat(formData.price) || 0,\n                originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,\n                featured: formData.featured,\n                downloads: 0,\n                imageUrl: \"/api/placeholder/1920/1080\",\n                thumbnailUrl: \"/api/placeholder/400/300\",\n                resolutions: {\n                    desktop: {\n                        width: 1920,\n                        height: 1080,\n                        url: \"/api/placeholder/1920/1080\"\n                    },\n                    mobile: {\n                        width: 1080,\n                        height: 1920,\n                        url: \"/api/placeholder/1080/1920\"\n                    },\n                    tablet: {\n                        width: 1536,\n                        height: 2048,\n                        url: \"/api/placeholder/1536/2048\"\n                    }\n                },\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            onUpload(newWallpaper);\n            setUploadStatus(\"success\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                category: \"Abstract\",\n                tags: \"\",\n                price: \"\",\n                originalPrice: \"\",\n                featured: false\n            });\n        } catch (error) {\n            setUploadStatus(\"error\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: \"Upload New Wallpaper\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Wallpaper Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                var _fileInputRef_current;\n                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                            },\n                                            className: \"text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Choose Image Files\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            multiple: true,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-2\",\n                                            children: \"Upload desktop, tablet, and mobile versions (JPG, PNG)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: formData.title,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"Enter wallpaper title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Category *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            required: true,\n                                            value: formData.category,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        category: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Abstract\",\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Nature\",\n                                                    children: \"Nature\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Geometric\",\n                                                    children: \"Geometric\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Minimal\",\n                                                    children: \"Minimal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Dark\",\n                                                    children: \"Dark\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Light\",\n                                                    children: \"Light\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Description *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                description: e.target.value\n                                            })),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Describe your wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.tags,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                tags: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Enter tags separated by commas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Price ($) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            required: true,\n                                            value: formData.price,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        price: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Price ($)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            value: formData.originalPrice,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        originalPrice: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"featured\",\n                                    checked: formData.featured,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                featured: e.target.checked\n                                            })),\n                                    className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"featured\",\n                                    className: \"ml-2 block text-sm text-gray-700\",\n                                    children: \"Mark as featured wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, undefined),\n                        uploadStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-800\",\n                                    children: \"Wallpaper uploaded successfully!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, undefined),\n                        uploadStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-5 h-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-800\",\n                                    children: \"Upload failed. Please try again.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: uploading,\n                                    className: \"flex-1 bg-purple-yellow-gradient text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                    children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Uploading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Upload Wallpaper\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 482,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 481,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WallpaperUploadModal, \"FFa/r8q3sl1tbMWGr6sEhy9jyn4=\");\n_c1 = WallpaperUploadModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpapersManagement);\nvar _c, _c1;\n$RefreshReg$(_c, \"WallpapersManagement\");\n$RefreshReg$(_c1, \"WallpaperUploadModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/wallpapers/page.tsx\n"));

/***/ })

});