'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  Squares2X2Icon,
  ListBulletIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import WallpaperGrid, { WallpaperFilters, WallpaperFiltersPanel } from '@/components/wallpaper/WallpaperGrid';
import { Product, ProductCategory, ProductType, WallpaperStyle, DeviceType } from '@/types/product';

const WallpapersPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<WallpaperFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [favorites, setFavorites] = useState<string[]>([]);

  // Mock wallpaper data - in real app, this would come from your database
  const mockWallpapers: Product[] = [
    {
      id: '1',
      title: 'Abstract Neon Waves',
      description: 'Vibrant neon waves flowing across a dark background, perfect for modern setups',
      price: 4.99,
      originalPrice: 7.99,
      category: ProductCategory.WALLPAPERS,
      type: ProductType.DIGITAL,
      images: ['/api/placeholder/400/600'],
      tags: ['abstract', 'neon', 'waves', 'modern'],
      inStock: true,
      featured: true,
      isDigital: true,
      fileFormat: ['JPG', 'PNG'],
      fileSize: '15-25 MB',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      wallpaperData: {
        resolutions: [
          { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '25 MB' },
          { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '18 MB' },
          { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '12 MB' },
          { width: 1080, height: 2340, label: 'Mobile', downloadUrl: '/downloads/mobile', fileSize: '8 MB' },
        ],
        aspectRatio: '16:9',
        colorPalette: ['#FF00FF', '#00FFFF', '#FF6B6B', '#4ECDC4', '#1A1A1A'],
        style: WallpaperStyle.ABSTRACT,
        deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.MOBILE],
        previewUrl: '/api/placeholder/800/600',
        watermarkedPreview: '/api/placeholder/400/600',
        downloadCount: 1250,
      },
    },
    {
      id: '2',
      title: 'Minimalist Mountain Range',
      description: 'Clean, minimalist mountain silhouettes with gradient sky',
      price: 3.99,
      category: ProductCategory.WALLPAPERS,
      type: ProductType.DIGITAL,
      images: ['/api/placeholder/400/600'],
      tags: ['minimalist', 'mountains', 'nature', 'gradient'],
      inStock: true,
      featured: false,
      isDigital: true,
      fileFormat: ['JPG', 'PNG'],
      fileSize: '10-20 MB',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
      wallpaperData: {
        resolutions: [
          { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '20 MB' },
          { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '15 MB' },
          { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '10 MB' },
        ],
        aspectRatio: '16:9',
        colorPalette: ['#87CEEB', '#FFB6C1', '#DDA0DD', '#F0F8FF', '#2F4F4F'],
        style: WallpaperStyle.MINIMALIST,
        deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.ULTRAWIDE],
        previewUrl: '/api/placeholder/800/600',
        watermarkedPreview: '/api/placeholder/400/600',
        downloadCount: 890,
      },
    },
    {
      id: '3',
      title: 'Gaming RGB Setup',
      description: 'Dynamic RGB lighting effects perfect for gaming setups',
      price: 5.99,
      category: ProductCategory.WALLPAPERS,
      type: ProductType.DIGITAL,
      images: ['/api/placeholder/400/600'],
      tags: ['gaming', 'rgb', 'neon', 'tech'],
      inStock: true,
      featured: true,
      isDigital: true,
      fileFormat: ['JPG', 'PNG'],
      fileSize: '20-30 MB',
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-03T00:00:00Z',
      wallpaperData: {
        resolutions: [
          { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '30 MB' },
          { width: 2560, height: 1440, label: '1440p', downloadUrl: '/downloads/1440p', fileSize: '22 MB' },
          { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '15 MB' },
          { width: 3440, height: 1440, label: 'Ultrawide', downloadUrl: '/downloads/ultrawide', fileSize: '25 MB' },
        ],
        aspectRatio: '16:9',
        colorPalette: ['#FF0080', '#00FF80', '#8000FF', '#FF8000', '#000000'],
        style: WallpaperStyle.GAMING,
        deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.ULTRAWIDE],
        previewUrl: '/api/placeholder/800/600',
        watermarkedPreview: '/api/placeholder/400/600',
        downloadCount: 2100,
      },
    },
    {
      id: '4',
      title: 'Nature Forest Path',
      description: 'Serene forest path with morning sunlight filtering through trees',
      price: 2.99,
      category: ProductCategory.WALLPAPERS,
      type: ProductType.DIGITAL,
      images: ['/api/placeholder/400/600'],
      tags: ['nature', 'forest', 'peaceful', 'photography'],
      inStock: true,
      featured: false,
      isDigital: true,
      fileFormat: ['JPG'],
      fileSize: '12-18 MB',
      createdAt: '2024-01-04T00:00:00Z',
      updatedAt: '2024-01-04T00:00:00Z',
      wallpaperData: {
        resolutions: [
          { width: 3840, height: 2160, label: '4K', downloadUrl: '/downloads/4k', fileSize: '18 MB' },
          { width: 1920, height: 1080, label: '1080p', downloadUrl: '/downloads/1080p', fileSize: '12 MB' },
          { width: 1080, height: 2340, label: 'Mobile', downloadUrl: '/downloads/mobile', fileSize: '6 MB' },
        ],
        aspectRatio: '16:9',
        colorPalette: ['#228B22', '#8FBC8F', '#F5DEB3', '#DEB887', '#654321'],
        style: WallpaperStyle.NATURE,
        deviceCompatibility: [DeviceType.DESKTOP, DeviceType.LAPTOP, DeviceType.MOBILE, DeviceType.TABLET],
        previewUrl: '/api/placeholder/800/600',
        watermarkedPreview: '/api/placeholder/400/600',
        downloadCount: 756,
      },
    },
  ];

  // Filter and search logic
  const filteredWallpapers = useMemo(() => {
    let filtered = mockWallpapers;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Style filter
    if (filters.style && filters.style.length > 0) {
      filtered = filtered.filter(product =>
        product.wallpaperData?.style && filters.style!.includes(product.wallpaperData.style)
      );
    }

    // Device filter
    if (filters.devices && filters.devices.length > 0) {
      filtered = filtered.filter(product =>
        product.wallpaperData?.deviceCompatibility.some(device =>
          filters.devices!.includes(device)
        )
      );
    }

    // Aspect ratio filter
    if (filters.aspectRatio && filters.aspectRatio.length > 0) {
      filtered = filtered.filter(product =>
        product.wallpaperData?.aspectRatio && filters.aspectRatio!.includes(product.wallpaperData.aspectRatio)
      );
    }

    // Color filter
    if (filters.colors && filters.colors.length > 0) {
      filtered = filtered.filter(product =>
        product.wallpaperData?.colorPalette.some(color =>
          filters.colors!.some(filterColor =>
            color.toLowerCase() === filterColor.toLowerCase()
          )
        )
      );
    }

    // Price range filter
    if (filters.priceRange) {
      filtered = filtered.filter(product =>
        product.price >= filters.priceRange![0] && product.price <= filters.priceRange![1]
      );
    }

    // Sort
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        switch (filters.sortBy) {
          case 'price-low':
            return a.price - b.price;
          case 'price-high':
            return b.price - a.price;
          case 'downloads':
            return (b.wallpaperData?.downloadCount || 0) - (a.wallpaperData?.downloadCount || 0);
          case 'popular':
            return (b.wallpaperData?.downloadCount || 0) - (a.wallpaperData?.downloadCount || 0);
          case 'newest':
          default:
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        }
      });
    }

    return filtered;
  }, [mockWallpapers, searchTerm, filters]);

  const handleAddToCart = (product: Product) => {
    console.log('Added to cart:', product.title);
    // In real app, this would add to cart context
  };

  const handleToggleFavorite = (productId: string) => {
    setFavorites(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl font-bold text-gray-900 mb-4"
          >
            Premium Wallpapers
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Transform your devices with our collection of high-quality wallpapers
          </motion.p>
        </div>

        {/* Search and Controls */}
        <div className="flex flex-col lg:flex-row gap-6 mb-8">
          {/* Search Bar */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search wallpapers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-4">
            {/* Sort */}
            <select
              value={filters.sortBy || 'newest'}
              onChange={(e) => setFilters({ ...filters, sortBy: e.target.value as any })}
              className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="newest">Newest</option>
              <option value="popular">Most Popular</option>
              <option value="downloads">Most Downloaded</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
            </select>

            {/* View Mode */}
            <div className="flex border border-gray-300 rounded-xl overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-3 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <Squares2X2Icon className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-3 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <ListBulletIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-3 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors duration-200"
            >
              <AdjustmentsHorizontalIcon className="w-5 h-5" />
              <span>Filters</span>
            </button>
          </div>
        </div>

        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="w-80 flex-shrink-0"
            >
              <WallpaperFiltersPanel
                filters={filters}
                onFiltersChange={setFilters}
                onClearFilters={() => setFilters({})}
              />
            </motion.div>
          )}

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Count */}
            <div className="flex items-center justify-between mb-6">
              <p className="text-gray-600">
                {filteredWallpapers.length} wallpaper{filteredWallpapers.length !== 1 ? 's' : ''} found
              </p>
              {Object.keys(filters).length > 0 && (
                <button
                  onClick={() => setFilters({})}
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  Clear all filters
                </button>
              )}
            </div>

            {/* Wallpapers Grid */}
            <WallpaperGrid
              products={filteredWallpapers}
              onAddToCart={handleAddToCart}
              onToggleFavorite={handleToggleFavorite}
              favorites={favorites}
            />

            {/* No Results */}
            {filteredWallpapers.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🖼️</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No wallpapers found</h3>
                <p className="text-gray-500 mb-4">
                  Try adjusting your search or filter criteria
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilters({});
                  }}
                  className="px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200"
                >
                  Clear Search & Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WallpapersPage;
