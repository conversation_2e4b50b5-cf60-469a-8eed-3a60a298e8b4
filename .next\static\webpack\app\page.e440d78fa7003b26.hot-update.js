"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!***************************************!*\
  !*** ./src/components/home/<USER>
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckCircleIcon,ComputerDesktopIcon,PaintBrushIcon,SparklesIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckCircleIcon,ComputerDesktopIcon,PaintBrushIcon,SparklesIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckCircleIcon,ComputerDesktopIcon,PaintBrushIcon,SparklesIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckCircleIcon,ComputerDesktopIcon,PaintBrushIcon,SparklesIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/VideoCameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckCircleIcon,ComputerDesktopIcon,PaintBrushIcon,SparklesIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckCircleIcon,ComputerDesktopIcon,PaintBrushIcon,SparklesIcon,VideoCameraIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst About = ()=>{\n    const skills = [\n        {\n            icon: _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: \"Graphic Design\",\n            description: \"Creating visually stunning designs that communicate your brand message effectively\",\n            tools: [\n                \"Adobe Illustrator\",\n                \"Photoshop\",\n                \"InDesign\",\n                \"Figma\"\n            ],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            icon: _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"Web Design\",\n            description: \"Building responsive, user-friendly websites that deliver exceptional user experiences\",\n            tools: [\n                \"React\",\n                \"Next.js\",\n                \"Tailwind CSS\",\n                \"WordPress\"\n            ],\n            color: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"Photography\",\n            description: \"Capturing compelling images that tell stories and showcase products beautifully\",\n            tools: [\n                \"Canon EOS\",\n                \"Lightroom\",\n                \"Photoshop\",\n                \"Studio Lighting\"\n            ],\n            color: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Video Editing\",\n            description: \"Creating engaging video content with professional editing and motion graphics\",\n            tools: [\n                \"After Effects\",\n                \"Premiere Pro\",\n                \"DaVinci Resolve\",\n                \"Final Cut Pro\"\n            ],\n            color: \"from-orange-500 to-red-500\"\n        }\n    ];\n    const achievements = [\n        \"Over 50+ successful projects completed\",\n        \"5+ years of creative industry experience\",\n        \"Specialized in brand identity and digital marketing\",\n        \"Expert in both print and digital media\",\n        \"Collaborative approach with clients\",\n        \"Fast turnaround times without compromising quality\"\n    ];\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-gray-50 via-white to-primary-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true,\n                        amount: 0.3\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            variants: itemVariants,\n                            className: \"inline-flex items-center space-x-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-6 h-6 text-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-primary-600 font-semibold text-sm uppercase tracking-wider\",\n                                    children: \"About Me\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h2, {\n                            variants: itemVariants,\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Hi, I'm \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-block\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\",\n                                        WebkitBackgroundClip: \"text\",\n                                        WebkitTextFillColor: \"transparent\",\n                                        backgroundClip: \"text\"\n                                    },\n                                    children: \"Elias Mwangi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                            variants: itemVariants,\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"I am a creative professional specializing in graphic design, web design, photography, and video editing. I bring ideas to life through visually appealing and functional designs that capture attention and tell compelling stories.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true,\n                        amount: 0.3\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20\",\n                    children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                y: -10,\n                                scale: 1.02\n                            },\n                            className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 rounded-2xl bg-gradient-to-r \".concat(skill.color, \" flex items-center justify-center mb-6\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(skill.icon, {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                    children: skill.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                    children: skill.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: skill.tools.map((tool, toolIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\",\n                                            children: tool\n                                        }, toolIndex, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, skill.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true,\n                        amount: 0.3\n                    },\n                    className: \"bg-white rounded-3xl p-8 md:p-12 shadow-xl border border-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: itemVariants,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-3xl font-bold text-gray-900 mb-6\",\n                                        children: \"Why Choose Me?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n                                        children: \"With a passion for creativity and attention to detail, I deliver high-quality work that exceeds expectations. My diverse skill set allows me to handle complete projects from concept to completion.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                variants: itemVariants,\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckCircleIcon_ComputerDesktopIcon_PaintBrushIcon_SparklesIcon_VideoCameraIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-700\",\n                                                        children: achievement\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: itemVariants,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl p-8 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-2xl font-bold mb-4\",\n                                                children: \"Let's Work Together\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-100 mb-6\",\n                                                children: \"Ready to bring your creative vision to life? Let's discuss your project and create something amazing together.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200\",\n                                                children: \"Get In Touch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-4 -right-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-400 rounded-full opacity-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\home\\\\About.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\n_c = About;\n/* harmony default export */ __webpack_exports__[\"default\"] = (About);\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});