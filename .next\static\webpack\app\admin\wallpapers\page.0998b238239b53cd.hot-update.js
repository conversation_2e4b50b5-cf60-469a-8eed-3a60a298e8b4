"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/wallpapers/page",{

/***/ "(app-pages-browser)/./src/app/admin/wallpapers/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/admin/wallpapers/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DeviceTabletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/wallpapers */ \"(app-pages-browser)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\nconst WallpapersManagement = ()=>{\n    _s();\n    const [wallpapers, setWallpapers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [wallpaperToDelete, setWallpaperToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [wallpaperToEdit, setWallpaperToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [wallpaperToView, setWallpaperToView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpaperCategories.map((cat)=>cat.toLowerCase());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Convert shared wallpapers data to admin format\n        const adminWallpapers = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpapersData.map(_data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.convertToAdminWallpaper);\n        setWallpapers(adminWallpapers);\n        setLoading(false);\n    }, []);\n    const filteredWallpapers = wallpapers.filter((wallpaper)=>{\n        const matchesSearch = wallpaper.title.toLowerCase().includes(searchTerm.toLowerCase()) || wallpaper.description.toLowerCase().includes(searchTerm.toLowerCase()) || wallpaper.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || wallpaper.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleDeleteWallpaper = (id)=>{\n        setWallpaperToDelete(id);\n        setShowDeleteModal(true);\n    };\n    const confirmDelete = ()=>{\n        if (wallpaperToDelete) {\n            setWallpapers((prev)=>prev.filter((w)=>w.id !== wallpaperToDelete));\n            setWallpaperToDelete(null);\n            setShowDeleteModal(false);\n        }\n    };\n    const toggleFeatured = (id)=>{\n        setWallpapers((prev)=>prev.map((w)=>w.id === id ? {\n                    ...w,\n                    featured: !w.featured\n                } : w));\n    };\n    const handleViewWallpaper = (wallpaper)=>{\n        setWallpaperToView(wallpaper);\n        setShowViewModal(true);\n    };\n    const handleEditWallpaper = (wallpaper)=>{\n        setWallpaperToEdit(wallpaper);\n        setShowEditModal(true);\n    };\n    const handleUpdateWallpaper = (updatedWallpaper)=>{\n        setWallpapers((prev)=>prev.map((w)=>w.id === updatedWallpaper.id ? updatedWallpaper : w));\n        setShowEditModal(false);\n        setWallpaperToEdit(null);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Wallpapers Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage your wallpaper collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 sm:mt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUploadModal(true),\n                            className: \"text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center space-x-2\",\n                            style: {\n                                background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Add Wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Wallpapers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Downloads\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.reduce((sum, w)=>sum + w.downloads, 0).toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.filter((w)=>w.featured).length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 text-yellow-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: categories.length - 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedCategory(category),\n                                    className: \"px-4 py-2 rounded-lg font-medium transition-all duration-200 \".concat(selectedCategory === category ? \"text-white shadow-lg\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                    style: selectedCategory === category ? {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                    } : {},\n                                    children: category === \"all\" ? \"All Categories\" : category\n                                }, category, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    children: filteredWallpapers.map((wallpaper)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            layout: true,\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-16 h-16 text-primary-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        wallpaper.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 left-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                style: {\n                                                    background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                                },\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleFeatured(wallpaper.id),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: wallpaper.featured ? \"Remove from featured\" : \"Add to featured\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(wallpaper.featured ? \"text-purple-600\" : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleViewWallpaper(wallpaper),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: \"View wallpaper\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditWallpaper(wallpaper),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: \"Edit wallpaper\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteWallpaper(wallpaper.id),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-2 left-2 flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-1\",\n                                            children: wallpaper.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: wallpaper.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: [\n                                                wallpaper.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n                                                        children: tag\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                wallpaper.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        wallpaper.tags.length - 3\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                wallpaper.downloads,\n                                                                \" downloads\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                wallpaper.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: wallpaper.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, wallpaper.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No wallpapers found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Try adjusting your search or filter criteria\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperUploadModal, {\n                isOpen: showUploadModal,\n                onClose: ()=>setShowUploadModal(false),\n                onUpload: (wallpaper)=>{\n                    setWallpapers((prev)=>[\n                            wallpaper,\n                            ...prev\n                        ]);\n                    setShowUploadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, undefined),\n            wallpaperToEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperEditModal, {\n                isOpen: showEditModal,\n                wallpaper: wallpaperToEdit,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setWallpaperToEdit(null);\n                },\n                onUpdate: handleUpdateWallpaper\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 9\n            }, undefined),\n            wallpaperToView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperViewModal, {\n                isOpen: showViewModal,\n                wallpaper: wallpaperToView,\n                onClose: ()=>{\n                    setShowViewModal(false);\n                    setWallpaperToView(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, undefined),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Delete Wallpaper\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to delete this wallpaper? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDelete,\n                                    className: \"flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors duration-200\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WallpapersManagement, \"QAM2Au/tK8ESN4/9KmUcYgjp1zI=\");\n_c = WallpapersManagement;\n// Wallpaper Upload Modal Component\nconst WallpaperUploadModal = (param)=>{\n    let { isOpen, onClose, onUpload } = param;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"Abstract\",\n        tags: \"\",\n        price: \"\",\n        originalPrice: \"\",\n        featured: false\n    });\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadStatus, setUploadStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const fileInputRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setUploading(true);\n        setUploadStatus(\"idle\");\n        try {\n            // Simulate upload\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const newWallpaper = {\n                id: Date.now().toString(),\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                tags: formData.tags.split(\",\").map((tag)=>tag.trim()).filter(Boolean),\n                price: parseFloat(formData.price) || 0,\n                originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,\n                featured: formData.featured,\n                downloads: 0,\n                imageUrl: \"/api/placeholder/1920/1080\",\n                thumbnailUrl: \"/api/placeholder/400/300\",\n                resolutions: {\n                    desktop: {\n                        width: 1920,\n                        height: 1080,\n                        url: \"/api/placeholder/1920/1080\"\n                    },\n                    mobile: {\n                        width: 1080,\n                        height: 1920,\n                        url: \"/api/placeholder/1080/1920\"\n                    },\n                    tablet: {\n                        width: 1536,\n                        height: 2048,\n                        url: \"/api/placeholder/1536/2048\"\n                    }\n                },\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            onUpload(newWallpaper);\n            setUploadStatus(\"success\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                category: \"Abstract\",\n                tags: \"\",\n                price: \"\",\n                originalPrice: \"\",\n                featured: false\n            });\n        } catch (error) {\n            setUploadStatus(\"error\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: \"Upload New Wallpaper\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Wallpaper Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                var _fileInputRef_current;\n                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                            },\n                                            className: \"text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Choose Image Files\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            multiple: true,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-2\",\n                                            children: \"Upload desktop, tablet, and mobile versions (JPG, PNG)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: formData.title,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"Enter wallpaper title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Category *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            required: true,\n                                            value: formData.category,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        category: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Abstract\",\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Nature\",\n                                                    children: \"Nature\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Geometric\",\n                                                    children: \"Geometric\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Minimal\",\n                                                    children: \"Minimal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Dark\",\n                                                    children: \"Dark\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Light\",\n                                                    children: \"Light\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Description *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                description: e.target.value\n                                            })),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Describe your wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.tags,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                tags: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Enter tags separated by commas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Price ($) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            required: true,\n                                            value: formData.price,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        price: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Price ($)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            value: formData.originalPrice,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        originalPrice: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"featured\",\n                                    checked: formData.featured,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                featured: e.target.checked\n                                            })),\n                                    className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"featured\",\n                                    className: \"ml-2 block text-sm text-gray-700\",\n                                    children: \"Mark as featured wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, undefined),\n                        uploadStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-800\",\n                                    children: \"Wallpaper uploaded successfully!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, undefined),\n                        uploadStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-5 h-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-800\",\n                                    children: \"Upload failed. Please try again.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: uploading,\n                                    className: \"flex-1 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                    },\n                                    children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Uploading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Upload Wallpaper\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 483,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 482,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WallpaperUploadModal, \"FFa/r8q3sl1tbMWGr6sEhy9jyn4=\");\n_c1 = WallpaperUploadModal;\n// Wallpaper View Modal Component\nconst WallpaperViewModal = (param)=>{\n    let { isOpen, wallpaper, onClose } = param;\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"View Wallpaper\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 670,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 671,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 669,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-16 h-16 text-primary-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-gray-50 p-3 rounded-lg text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-6 h-6 mx-auto mb-1 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Desktop\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: [\n                                                        wallpaper.resolutions.desktop.width,\n                                                        \"x\",\n                                                        wallpaper.resolutions.desktop.height\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-gray-50 p-3 rounded-lg text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 mx-auto mb-1 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Tablet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: [\n                                                        wallpaper.resolutions.tablet.width,\n                                                        \"x\",\n                                                        wallpaper.resolutions.tablet.height\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 bg-gray-50 p-3 rounded-lg text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 mx-auto mb-1 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Mobile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: [\n                                                        wallpaper.resolutions.mobile.width,\n                                                        \"x\",\n                                                        wallpaper.resolutions.mobile.height\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-900 mb-2\",\n                                            children: wallpaper.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: wallpaper.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: wallpaper.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        wallpaper.price\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Downloads\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: wallpaper.downloads.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Featured\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: wallpaper.featured ? \"Yes\" : \"No\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Tags\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: wallpaper.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-xs text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Created: \",\n                                                    new Date(wallpaper.createdAt).toLocaleDateString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Updated: \",\n                                                    new Date(wallpaper.updatedAt).toLocaleDateString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 757,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 668,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 667,\n        columnNumber: 5\n    }, undefined);\n};\n_c2 = WallpaperViewModal;\n// Wallpaper Edit Modal Component\nconst WallpaperEditModal = (param)=>{\n    let { isOpen, wallpaper, onClose, onUpdate } = param;\n    var _wallpaper_originalPrice;\n    _s2();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: wallpaper.title,\n        description: wallpaper.description,\n        category: wallpaper.category,\n        tags: wallpaper.tags.join(\", \"),\n        price: wallpaper.price.toString(),\n        originalPrice: ((_wallpaper_originalPrice = wallpaper.originalPrice) === null || _wallpaper_originalPrice === void 0 ? void 0 : _wallpaper_originalPrice.toString()) || \"\",\n        featured: wallpaper.featured\n    });\n    const [updating, setUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setUpdating(true);\n        try {\n            // Simulate update\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const updatedWallpaper = {\n                ...wallpaper,\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                tags: formData.tags.split(\",\").map((tag)=>tag.trim()).filter(Boolean),\n                price: parseFloat(formData.price) || 0,\n                originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,\n                featured: formData.featured,\n                updatedAt: new Date().toISOString()\n            };\n            onUpdate(updatedWallpaper);\n        } catch (error) {\n            console.error(\"Error updating wallpaper:\", error);\n        } finally{\n            setUpdating(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: \"Edit Wallpaper\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 821,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: formData.title,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Category *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 837,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            required: true,\n                                            value: formData.category,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        category: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Abstract\",\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Nature\",\n                                                    children: \"Nature\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Geometric\",\n                                                    children: \"Geometric\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Minimal\",\n                                                    children: \"Minimal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Dark\",\n                                                    children: \"Dark\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Light\",\n                                                    children: \"Light\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Description *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                description: e.target.value\n                                            })),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 866,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.tags,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                tags: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Enter tags separated by commas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Price ($) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            required: true,\n                                            value: formData.price,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        price: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 880,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Price ($)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            value: formData.originalPrice,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        originalPrice: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 877,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"editFeatured\",\n                                    checked: formData.featured,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                featured: e.target.checked\n                                            })),\n                                    className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"editFeatured\",\n                                    className: \"ml-2 block text-sm text-gray-700\",\n                                    children: \"Mark as featured wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 902,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: updating,\n                                    className: \"flex-1 text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                    style: {\n                                        background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                                    },\n                                    children: updating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Updating...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Update Wallpaper\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 823,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 820,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 819,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(WallpaperEditModal, \"LApV82+AF1CEXmJl8q601ix5Coo=\");\n_c3 = WallpaperEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpapersManagement);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"WallpapersManagement\");\n$RefreshReg$(_c1, \"WallpaperUploadModal\");\n$RefreshReg$(_c2, \"WallpaperViewModal\");\n$RefreshReg$(_c3, \"WallpaperEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/wallpapers/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction XMarkIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M6 18 18 6M6 6l12 12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(XMarkIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL1hNYXJrSWNvbi5qcyIsIm1hcHBpbmdzIjoiOztBQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxzQkFBc0IsZ0RBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUcsK0JBQStCLGdEQUFtQjtBQUNyRDtBQUNBLEdBQUcsOEJBQThCLGdEQUFtQjtBQUNwRDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxpQ0FBaUMsNkNBQWdCO0FBQ2pELCtEQUFlLFVBQVUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vWE1hcmtJY29uLmpzP2I4NTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBYTWFya0ljb24oe1xuICB0aXRsZSxcbiAgdGl0bGVJZCxcbiAgLi4ucHJvcHNcbn0sIHN2Z1JlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzdmdcIiwgT2JqZWN0LmFzc2lnbih7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICBmaWxsOiBcIm5vbmVcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIHN0cm9rZVdpZHRoOiAxLjUsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgXCJkYXRhLXNsb3RcIjogXCJpY29uXCIsXG4gICAgcmVmOiBzdmdSZWYsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogdGl0bGVJZFxuICB9LCBwcm9wcyksIHRpdGxlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJ0aXRsZVwiLCB7XG4gICAgaWQ6IHRpdGxlSWRcbiAgfSwgdGl0bGUpIDogbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICBkOiBcIk02IDE4IDE4IDZNNiA2bDEyIDEyXCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihYTWFya0ljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\n"));

/***/ })

});