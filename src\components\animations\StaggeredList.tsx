'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface StaggeredListProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  initialDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'scale' | 'fade';
  distance?: number;
  duration?: number;
  className?: string;
}

const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 0.1,
  initialDelay = 0,
  direction = 'up',
  distance = 30,
  duration = 0.5,
  className = '',
}) => {
  const getItemVariants = () => {
    const baseVariants = {
      visible: {
        opacity: 1,
        x: 0,
        y: 0,
        scale: 1,
        transition: {
          duration,
          ease: [0.25, 0.25, 0.25, 0.75],
        },
      },
    };

    switch (direction) {
      case 'up':
        return {
          ...baseVariants,
          hidden: { opacity: 0, y: distance },
        };
      case 'down':
        return {
          ...baseVariants,
          hidden: { opacity: 0, y: -distance },
        };
      case 'left':
        return {
          ...baseVariants,
          hidden: { opacity: 0, x: distance },
        };
      case 'right':
        return {
          ...baseVariants,
          hidden: { opacity: 0, x: -distance },
        };
      case 'scale':
        return {
          ...baseVariants,
          hidden: { opacity: 0, scale: 0.8 },
        };
      case 'fade':
        return {
          ...baseVariants,
          hidden: { opacity: 0 },
        };
      default:
        return {
          ...baseVariants,
          hidden: { opacity: 0, y: distance },
        };
    }
  };

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: initialDelay,
      },
    },
  };

  const itemVariants = getItemVariants();

  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '0px 0px -100px 0px' }}
      variants={containerVariants}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

export default StaggeredList;
