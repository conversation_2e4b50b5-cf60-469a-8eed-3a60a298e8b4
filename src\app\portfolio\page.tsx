'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FunnelIcon, 
  MagnifyingGlassIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';

interface Project {
  id: number;
  title: string;
  category: string;
  description: string;
  longDescription: string;
  image: string;
  technologies: string[];
  liveUrl?: string;
  portfolioUrl?: string;
  featured: boolean;
}

const Portfolio: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = ['All', 'Graphic Design', 'Web Design', 'Photography', 'Video Editing'];

  const projects: Project[] = [
    {
      id: 1,
      title: 'Corporate Brand Identity Package',
      category: 'Graphic Design',
      description: 'Complete brand identity design for a tech startup',
      longDescription: 'Comprehensive brand identity package including logo design, business cards, letterheads, brand guidelines, and marketing materials. Created a modern, professional look that reflects the company\'s innovative approach.',
      image: '/api/placeholder/600/400',
      technologies: ['Adobe Illustrator', 'Photoshop', 'InDesign', 'Brand Strategy'],
      liveUrl: 'https://example.com',
      portfolioUrl: 'https://behance.net',
      featured: true,
    },
    {
      id: 2,
      title: 'E-Commerce Website Design',
      category: 'Web Design',
      description: 'Modern, responsive e-commerce platform',
      longDescription: 'Designed and developed a fully responsive e-commerce website with intuitive user interface, seamless shopping experience, and mobile-first approach. Includes custom animations and interactive elements.',
      image: '/api/placeholder/600/400',
      technologies: ['React', 'Next.js', 'Tailwind CSS', 'Figma'],
      liveUrl: 'https://example-store.com',
      portfolioUrl: 'https://github.com',
      featured: true,
    },
    {
      id: 3,
      title: 'Product Photography Collection',
      category: 'Photography',
      description: 'Professional product photography for e-commerce',
      longDescription: 'High-quality product photography session for an e-commerce brand, featuring creative lighting setups, multiple angles, and lifestyle shots. All images optimized for web and print use.',
      image: '/api/placeholder/600/400',
      technologies: ['Canon EOS R5', 'Adobe Lightroom', 'Photoshop', 'Studio Lighting'],
      liveUrl: 'https://photography-portfolio.com',
      portfolioUrl: 'https://instagram.com',
      featured: true,
    },
    {
      id: 4,
      title: 'Brand Promotional Video',
      category: 'Video Editing',
      description: 'Engaging promotional video with motion graphics',
      longDescription: 'Created a compelling brand promotional video featuring motion graphics, professional editing, color grading, and sound design. The video effectively communicates the brand story and values.',
      image: '/api/placeholder/600/400',
      technologies: ['After Effects', 'Premiere Pro', 'DaVinci Resolve', 'Motion Graphics'],
      liveUrl: 'https://vimeo.com/video',
      portfolioUrl: 'https://youtube.com',
      featured: true,
    },
    {
      id: 5,
      title: 'Restaurant Menu Design',
      category: 'Graphic Design',
      description: 'Elegant menu design for upscale restaurant',
      longDescription: 'Designed an elegant, easy-to-read menu for an upscale restaurant. Focused on typography, layout, and visual hierarchy to enhance the dining experience.',
      image: '/api/placeholder/600/400',
      technologies: ['Adobe InDesign', 'Illustrator', 'Typography', 'Print Design'],
      liveUrl: 'https://restaurant.com',
      portfolioUrl: 'https://dribbble.com',
      featured: false,
    },
    {
      id: 6,
      title: 'Mobile App UI Design',
      category: 'Web Design',
      description: 'Modern mobile app interface design',
      longDescription: 'Designed a clean, intuitive mobile app interface with focus on user experience and accessibility. Includes wireframes, prototypes, and final UI designs.',
      image: '/api/placeholder/600/400',
      technologies: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      liveUrl: 'https://app-preview.com',
      portfolioUrl: 'https://figma.com',
      featured: false,
    },
  ];

  const filteredProjects = projects.filter(project => {
    const matchesCategory = selectedCategory === 'All' || project.category === selectedCategory;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-primary-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            My <span className="bg-purple-yellow-gradient bg-clip-text text-transparent">Portfolio</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore my creative work across graphic design, web design, photography, and video editing. 
            Each project represents a unique story and creative solution.
          </p>
        </motion.div>

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-lg p-6 mb-12 border border-gray-100"
        >
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-2 rounded-xl font-medium transition-all duration-200 ${
                    selectedCategory === category
                      ? 'bg-purple-yellow-gradient text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                variants={itemVariants}
                layout
                exit={{ opacity: 0, scale: 0.8 }}
                whileHover={{ y: -10 }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300"
              >
                {/* Project Image */}
                <div className="relative h-64 bg-gradient-to-br from-primary-100 to-secondary-100 overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-6xl font-bold text-primary-300">
                      {project.title.charAt(0)}
                    </div>
                  </div>
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 hover:opacity-100 transition-opacity duration-300 flex space-x-3">
                      {project.liveUrl && (
                        <a
                          href={project.liveUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200"
                        >
                          <EyeIcon className="w-5 h-5" />
                        </a>
                      )}
                      {project.portfolioUrl && (
                        <a
                          href={project.portfolioUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors duration-200"
                        >
                          <ArrowTopRightOnSquareIcon className="w-5 h-5" />
                        </a>
                      )}
                    </div>
                  </div>

                  {/* Featured Badge */}
                  {project.featured && (
                    <div className="absolute top-4 left-4">
                      <span className="bg-purple-yellow-gradient text-white px-3 py-1 rounded-full text-sm font-medium">
                        Featured
                      </span>
                    </div>
                  )}

                  {/* Category Badge */}
                  <div className="absolute top-4 right-4">
                    <span className="bg-white/90 backdrop-blur-sm text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{project.title}</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">{project.description}</p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.slice(0, 3).map((tech, index) => (
                      <span
                        key={index}
                        className="text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Links */}
                  <div className="flex space-x-3">
                    {project.liveUrl && (
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 bg-purple-yellow-gradient text-white text-center py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-200"
                      >
                        View Live
                      </a>
                    )}
                    {project.portfolioUrl && (
                      <a
                        href={project.portfolioUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 bg-gray-100 text-gray-700 text-center py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200"
                      >
                        Portfolio
                      </a>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* No Results */}
        {filteredProjects.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">No projects found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Portfolio;
