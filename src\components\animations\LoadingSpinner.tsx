'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'gradient';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'spinner',
  color = 'primary',
  className = '',
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-8 h-8';
      case 'lg':
        return 'w-12 h-12';
      case 'xl':
        return 'w-16 h-16';
      default:
        return 'w-8 h-8';
    }
  };

  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'text-primary-600 border-primary-600';
      case 'secondary':
        return 'text-secondary-600 border-secondary-600';
      case 'white':
        return 'text-white border-white';
      case 'gray':
        return 'text-gray-600 border-gray-600';
      default:
        return 'text-primary-600 border-primary-600';
    }
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <motion.div
            className={`${getSizeClasses()} border-2 border-t-transparent rounded-full ${getColorClasses()} ${className}`}
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'linear',
            }}
          />
        );

      case 'dots':
        return (
          <div className={`flex space-x-1 ${className}`}>
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className={`w-2 h-2 rounded-full bg-current ${getColorClasses()}`}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: 'easeInOut',
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <motion.div
            className={`${getSizeClasses()} rounded-full bg-current ${getColorClasses()} ${className}`}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        );

      case 'bars':
        return (
          <div className={`flex space-x-1 items-end ${className}`}>
            {[0, 1, 2, 3].map((index) => (
              <motion.div
                key={index}
                className={`w-1 bg-current ${getColorClasses()}`}
                style={{ height: size === 'sm' ? '12px' : size === 'md' ? '20px' : size === 'lg' ? '28px' : '36px' }}
                animate={{
                  scaleY: [1, 2, 1],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: index * 0.1,
                  ease: 'easeInOut',
                }}
              />
            ))}
          </div>
        );

      case 'gradient':
        return (
          <motion.div
            className={`${getSizeClasses()} rounded-full ${className}`}
            style={{
              background: 'conic-gradient(from 0deg, transparent, #a855f7, #eab308, transparent)',
            }}
            animate={{ rotate: 360 }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'linear',
            }}
          >
            <div className="w-full h-full rounded-full bg-white" style={{ margin: '2px' }} />
          </motion.div>
        );

      default:
        return (
          <motion.div
            className={`${getSizeClasses()} border-2 border-t-transparent rounded-full ${getColorClasses()} ${className}`}
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'linear',
            }}
          />
        );
    }
  };

  return renderSpinner();
};

// Full-screen loading overlay component
interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  variant?: LoadingSpinnerProps['variant'];
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = 'Loading...',
  variant = 'gradient',
}) => {
  if (!isLoading) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="text-center"
      >
        <LoadingSpinner size="xl" variant={variant} />
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-4 text-gray-600 font-medium"
        >
          {message}
        </motion.p>
      </motion.div>
    </motion.div>
  );
};

export default LoadingSpinner;
