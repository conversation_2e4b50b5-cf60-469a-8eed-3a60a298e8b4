"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/wallpapers/page",{

/***/ "(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx":
/*!****************************************************!*\
  !*** ./src/components/wallpaper/WallpaperGrid.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WallpaperFiltersPanel: function() { return /* binding */ WallpaperFiltersPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/product */ \"(app-pages-browser)/./src/types/product.ts\");\n/* harmony import */ var _WallpaperPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./WallpaperPreview */ \"(app-pages-browser)/./src/components/wallpaper/WallpaperPreview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,WallpaperFiltersPanel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst WallpaperGrid = (param)=>{\n    let { products, onAddToCart, onToggleFavorite, favorites = [] } = param;\n    _s();\n    const [previewProduct, setPreviewProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredProduct, setHoveredProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePreview = (product)=>{\n        setPreviewProduct(product);\n    };\n    const handleDownload = (product)=>{\n        // In a real app, this would handle the purchase/download process\n        console.log(\"Download:\", product.title);\n        onAddToCart === null || onAddToCart === void 0 ? void 0 : onAddToCart(product);\n    };\n    const getStyleColor = (style)=>{\n        const colors = {\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ABSTRACT]: \"bg-purple-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.NATURE]: \"bg-green-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.MINIMALIST]: \"bg-gray-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.GAMING]: \"bg-red-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ANIME]: \"bg-pink-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.PHOTOGRAPHY]: \"bg-blue-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.GRADIENT]: \"bg-gradient-to-r from-purple-500 to-pink-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.PATTERN]: \"bg-indigo-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.DARK]: \"bg-gray-900\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.LIGHT]: \"bg-gray-100\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.NEON]: \"bg-cyan-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.VINTAGE]: \"bg-amber-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.MODERN]: \"bg-slate-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ARTISTIC]: \"bg-orange-500\"\n        };\n        return colors[style] || \"bg-gray-500\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    children: products.map((product)=>{\n                        var _product_wallpaperData, _product_wallpaperData1, _product_wallpaperData2, _product_wallpaperData3, _product_wallpaperData4, _product_wallpaperData5, _product_wallpaperData6;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            layout: true,\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            className: \"group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300\",\n                            onMouseEnter: ()=>setHoveredProduct(product.id),\n                            onMouseLeave: ()=>setHoveredProduct(null),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative aspect-[3/4] overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: ((_product_wallpaperData = product.wallpaperData) === null || _product_wallpaperData === void 0 ? void 0 : _product_wallpaperData.watermarkedPreview) || product.images[0],\n                                            alt: product.title,\n                                            className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>handlePreview(product),\n                                                        className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200\",\n                                                        title: \"Preview\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>onToggleFavorite === null || onToggleFavorite === void 0 ? void 0 : onToggleFavorite(product.id),\n                                                        className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200\",\n                                                        title: \"Add to Favorites\",\n                                                        children: favorites.includes(product.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>handleDownload(product),\n                                                        className: \"p-3 bg-purple-yellow-gradient rounded-full text-white hover:shadow-lg transition-all duration-200\",\n                                                        title: \"Add to Cart\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        ((_product_wallpaperData1 = product.wallpaperData) === null || _product_wallpaperData1 === void 0 ? void 0 : _product_wallpaperData1.style) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1 rounded-full text-xs font-medium text-white \".concat(getStyleColor(product.wallpaperData.style)),\n                                                children: product.wallpaperData.style.charAt(0).toUpperCase() + product.wallpaperData.style.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        ((_product_wallpaperData2 = product.wallpaperData) === null || _product_wallpaperData2 === void 0 ? void 0 : _product_wallpaperData2.resolutions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium\",\n                                                children: [\n                                                    product.wallpaperData.resolutions.length,\n                                                    \" resolutions\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-1 line-clamp-1\",\n                                            children: product.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                    children: [\n                                                        ((_product_wallpaperData3 = product.wallpaperData) === null || _product_wallpaperData3 === void 0 ? void 0 : _product_wallpaperData3.aspectRatio) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.wallpaperData.aspectRatio\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        product.fileFormat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: product.fileFormat.join(\", \")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                ((_product_wallpaperData4 = product.wallpaperData) === null || _product_wallpaperData4 === void 0 ? void 0 : _product_wallpaperData4.downloadCount) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.wallpaperData.downloadCount.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        ((_product_wallpaperData5 = product.wallpaperData) === null || _product_wallpaperData5 === void 0 ? void 0 : _product_wallpaperData5.colorPalette) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1 mb-3\",\n                                            children: [\n                                                product.wallpaperData.colorPalette.slice(0, 5).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full border border-gray-200\",\n                                                        style: {\n                                                            backgroundColor: color\n                                                        }\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                product.wallpaperData.colorPalette.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs text-gray-600\",\n                                                    children: [\n                                                        \"+\",\n                                                        product.wallpaperData.colorPalette.length - 5\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        ((_product_wallpaperData6 = product.wallpaperData) === null || _product_wallpaperData6 === void 0 ? void 0 : _product_wallpaperData6.deviceCompatibility) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: [\n                                                product.wallpaperData.deviceCompatibility.slice(0, 3).map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md\",\n                                                        children: device\n                                                    }, device, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                product.wallpaperData.deviceCompatibility.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        product.wallpaperData.deviceCompatibility.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.price.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 line-through\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.originalPrice.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: ()=>handleDownload(product),\n                                                    className: \"px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-200\",\n                                                    children: \"Add to Cart\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            previewProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WallpaperPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                product: previewProduct,\n                isOpen: !!previewProduct,\n                onClose: ()=>setPreviewProduct(null),\n                onDownload: (resolution)=>{\n                    console.log(\"Download resolution:\", resolution);\n                    handleDownload(previewProduct);\n                    setPreviewProduct(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(WallpaperGrid, \"qWycweb6R+Xv+UUVoR7Ea9aO4N8=\");\n_c = WallpaperGrid;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpaperGrid);\nconst WallpaperFiltersPanel = (param)=>{\n    let { filters, onFiltersChange, onClearFilters } = param;\n    const styles = Object.values(_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle);\n    const devices = Object.values(_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType);\n    const aspectRatios = [\n        \"16:9\",\n        \"16:10\",\n        \"21:9\",\n        \"4:3\",\n        \"9:16\",\n        \"3:2\"\n    ];\n    const commonColors = [\n        \"#FF6B6B\",\n        \"#4ECDC4\",\n        \"#45B7D1\",\n        \"#96CEB4\",\n        \"#FFEAA7\",\n        \"#DDA0DD\",\n        \"#98D8C8\",\n        \"#F7DC6F\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClearFilters,\n                        className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Style\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: styles.map((style)=>{\n                            var _filters_style;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filters_style = filters.style) === null || _filters_style === void 0 ? void 0 : _filters_style.includes(style)) || false,\n                                        onChange: (e)=>{\n                                            const newStyles = e.target.checked ? [\n                                                ...filters.style || [],\n                                                style\n                                            ] : (filters.style || []).filter((s)=>s !== style);\n                                            onFiltersChange({\n                                                ...filters,\n                                                style: newStyles\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 capitalize\",\n                                        children: style\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, style, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Device\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: devices.map((device)=>{\n                            var _filters_devices;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filters_devices = filters.devices) === null || _filters_devices === void 0 ? void 0 : _filters_devices.includes(device)) || false,\n                                        onChange: (e)=>{\n                                            const newDevices = e.target.checked ? [\n                                                ...filters.devices || [],\n                                                device\n                                            ] : (filters.devices || []).filter((d)=>d !== device);\n                                            onFiltersChange({\n                                                ...filters,\n                                                devices: newDevices\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 capitalize\",\n                                        children: device.replace(\"_\", \" \")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, device, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Aspect Ratio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: aspectRatios.map((ratio)=>{\n                            var _filters_aspectRatio;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: ((_filters_aspectRatio = filters.aspectRatio) === null || _filters_aspectRatio === void 0 ? void 0 : _filters_aspectRatio.includes(ratio)) || false,\n                                        onChange: (e)=>{\n                                            const newRatios = e.target.checked ? [\n                                                ...filters.aspectRatio || [],\n                                                ratio\n                                            ] : (filters.aspectRatio || []).filter((r)=>r !== ratio);\n                                            onFiltersChange({\n                                                ...filters,\n                                                aspectRatio: newRatios\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: ratio\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, ratio, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Colors\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-2\",\n                        children: commonColors.map((color)=>{\n                            var _filters_colors;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    var _filters_colors;\n                                    const newColors = ((_filters_colors = filters.colors) === null || _filters_colors === void 0 ? void 0 : _filters_colors.includes(color)) ? (filters.colors || []).filter((c)=>c !== color) : [\n                                        ...filters.colors || [],\n                                        color\n                                    ];\n                                    onFiltersChange({\n                                        ...filters,\n                                        colors: newColors\n                                    });\n                                },\n                                className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(((_filters_colors = filters.colors) === null || _filters_colors === void 0 ? void 0 : _filters_colors.includes(color)) ? \"border-gray-800 scale-110\" : \"border-gray-300 hover:border-gray-400\"),\n                                style: {\n                                    backgroundColor: color\n                                },\n                                title: color\n                            }, color, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = WallpaperFiltersPanel;\nvar _c, _c1;\n$RefreshReg$(_c, \"WallpaperGrid\");\n$RefreshReg$(_c1, \"WallpaperFiltersPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/wallpaper/WallpaperGrid.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowDownTrayIcon);\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0Fycm93RG93blRyYXlJY29uLmpzIiwibWFwcGluZ3MiOiI7O0FBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELHNCQUFzQixnREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRywrQkFBK0IsZ0RBQW1CO0FBQ3JEO0FBQ0EsR0FBRyw4QkFBOEIsZ0RBQW1CO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLGlDQUFpQyw2Q0FBZ0I7QUFDakQsK0RBQWUsVUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9BcnJvd0Rvd25UcmF5SWNvbi5qcz8xMzMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gQXJyb3dEb3duVHJheUljb24oe1xuICB0aXRsZSxcbiAgdGl0bGVJZCxcbiAgLi4ucHJvcHNcbn0sIHN2Z1JlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJzdmdcIiwgT2JqZWN0LmFzc2lnbih7XG4gICAgeG1sbnM6IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIixcbiAgICBmaWxsOiBcIm5vbmVcIixcbiAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgIHN0cm9rZVdpZHRoOiAxLjUsXG4gICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwiYXJpYS1oaWRkZW5cIjogXCJ0cnVlXCIsXG4gICAgXCJkYXRhLXNsb3RcIjogXCJpY29uXCIsXG4gICAgcmVmOiBzdmdSZWYsXG4gICAgXCJhcmlhLWxhYmVsbGVkYnlcIjogdGl0bGVJZFxuICB9LCBwcm9wcyksIHRpdGxlID8gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJ0aXRsZVwiLCB7XG4gICAgaWQ6IHRpdGxlSWRcbiAgfSwgdGl0bGUpIDogbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwYXRoXCIsIHtcbiAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICBkOiBcIk0zIDE2LjV2Mi4yNUEyLjI1IDIuMjUgMCAwIDAgNS4yNSAyMWgxMy41QTIuMjUgMi4yNSAwIDAgMCAyMSAxOC43NVYxNi41TTE2LjUgMTIgMTIgMTYuNW0wIDBMNy41IDEybTQuNSA0LjVWM1wiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoQXJyb3dEb3duVHJheUljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\n"));

/***/ })

});