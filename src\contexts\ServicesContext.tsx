'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Service {
  id: string;
  title: string;
  description: string;
  features: string[];
  tools: string[];
  icon: string;
  color: string;
  bgColor: string;
  borderColor: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ServicesContextType {
  services: Service[];
  activeServices: Service[];
  addService: (service: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateService: (id: string, service: Partial<Service>) => void;
  deleteService: (id: string) => void;
  toggleServiceActive: (id: string) => void;
  getService: (id: string) => Service | undefined;
  loading: boolean;
}

const ServicesContext = createContext<ServicesContextType | undefined>(undefined);

const defaultServices: Service[] = [
  {
    id: '1',
    title: 'Graphic Design',
    description: 'Creating visually stunning designs that communicate your brand message effectively',
    features: [
      'Logo Design & Branding',
      'Business Cards & Stationery',
      'Brochures & Flyers',
      'Social Media Graphics',
      'Print Design',
      'Brand Identity Development'
    ],
    tools: ['Adobe Illustrator', 'Photoshop', 'InDesign', 'Figma'],
    icon: 'PaintBrushIcon',
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Web Design',
    description: 'Building responsive, user-friendly websites that deliver exceptional user experiences',
    features: [
      'Responsive Web Design',
      'UI/UX Design',
      'E-commerce Websites',
      'Landing Pages',
      'Web Applications',
      'Website Maintenance'
    ],
    tools: ['React', 'Next.js', 'Tailwind CSS', 'WordPress'],
    icon: 'ComputerDesktopIcon',
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    title: 'Photography',
    description: 'Capturing compelling images that tell stories and showcase products beautifully',
    features: [
      'Product Photography',
      'Portrait Photography',
      'Event Photography',
      'Commercial Photography',
      'Photo Editing & Retouching',
      'Studio & Location Shoots'
    ],
    tools: ['Canon EOS', 'Lightroom', 'Photoshop', 'Studio Lighting'],
    icon: 'CameraIcon',
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '4',
    title: 'Video Editing',
    description: 'Creating engaging video content with professional editing and motion graphics',
    features: [
      'Video Production',
      'Motion Graphics',
      'Color Correction',
      'Audio Enhancement',
      'Social Media Videos',
      'Commercial Videos'
    ],
    tools: ['After Effects', 'Premiere Pro', 'DaVinci Resolve', 'Final Cut Pro'],
    icon: 'VideoCameraIcon',
    color: 'from-red-500 to-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

interface ServicesProviderProps {
  children: ReactNode;
}

export const ServicesProvider: React.FC<ServicesProviderProps> = ({ children }) => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  // Initialize services from localStorage or use defaults
  useEffect(() => {
    const loadServices = () => {
      try {
        const savedServices = localStorage.getItem('elishop_services');
        if (savedServices) {
          const parsedServices = JSON.parse(savedServices);
          setServices(parsedServices);
        } else {
          setServices(defaultServices);
          localStorage.setItem('elishop_services', JSON.stringify(defaultServices));
        }
      } catch (error) {
        console.error('Error loading services:', error);
        setServices(defaultServices);
      } finally {
        setLoading(false);
      }
    };

    loadServices();
  }, []);

  // Save services to localStorage whenever services change
  useEffect(() => {
    if (!loading && services.length > 0) {
      localStorage.setItem('elishop_services', JSON.stringify(services));
    }
  }, [services, loading]);

  const activeServices = services.filter(service => service.isActive);

  const addService = (serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>) => {
    const now = new Date().toISOString();
    const newService: Service = {
      ...serviceData,
      id: Date.now().toString(),
      createdAt: now,
      updatedAt: now
    };
    setServices(prev => [...prev, newService]);
  };

  const updateService = (id: string, serviceData: Partial<Service>) => {
    setServices(prev => prev.map(service => 
      service.id === id 
        ? { ...service, ...serviceData, updatedAt: new Date().toISOString() }
        : service
    ));
  };

  const deleteService = (id: string) => {
    setServices(prev => prev.filter(service => service.id !== id));
  };

  const toggleServiceActive = (id: string) => {
    setServices(prev => prev.map(service => 
      service.id === id 
        ? { ...service, isActive: !service.isActive, updatedAt: new Date().toISOString() }
        : service
    ));
  };

  const getService = (id: string): Service | undefined => {
    return services.find(service => service.id === id);
  };

  const value: ServicesContextType = {
    services,
    activeServices,
    addService,
    updateService,
    deleteService,
    toggleServiceActive,
    getService,
    loading
  };

  return (
    <ServicesContext.Provider value={value}>
      {children}
    </ServicesContext.Provider>
  );
};

export const useServices = (): ServicesContextType => {
  const context = useContext(ServicesContext);
  if (context === undefined) {
    throw new Error('useServices must be used within a ServicesProvider');
  }
  return context;
};

export default ServicesContext;
