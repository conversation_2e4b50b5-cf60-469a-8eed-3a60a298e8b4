# EliShop - Modern Portfolio & Shop Website

A stunning portfolio website with integrated shop functionality, featuring beautiful purple and yellow color scheme, smooth animations, and modern design.

## ✨ Features

- **Modern Design**: Clean, contemporary design with purple and yellow color palette
- **Smooth Animations**: Powered by Framer Motion for fluid transitions and interactions
- **Responsive Layout**: Fully responsive design that works on all devices
- **Portfolio Showcase**: Animated portfolio section with filtering capabilities
- **E-commerce Shop**: Integrated shopping cart with product management
- **Contact Form**: Interactive contact form with validation
- **Performance Optimized**: Built with Next.js for optimal performance

## 🚀 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom animations
- **Animations**: Framer Motion
- **TypeScript**: Full TypeScript support
- **Icons**: Heroicons and Lucide React

## 🎨 Design Features

- **Color Scheme**: Purple (#a855f7) and Yellow (#eab308) gradients
- **Animations**: 
  - Smooth page transitions
  - Hover effects and micro-interactions
  - Animated text reveals
  - Floating elements
  - Loading animations
- **Components**:
  - Animated navigation with mobile menu
  - Hero section with floating elements
  - Skills progress bars
  - Portfolio filtering system
  - Shopping cart with animations
  - Contact form with validation

## 📦 Installation

1. **Install Node.js** (if not already installed):
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🛠️ Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and animations
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Main page
├── components/
│   ├── ui/                  # Reusable UI components
│   │   ├── Button.tsx
│   │   ├── AnimatedText.tsx
│   │   ├── ParticleBackground.tsx
│   │   └── LoadingSpinner.tsx
│   ├── Navigation.tsx       # Navigation component
│   ├── Hero.tsx            # Hero section
│   ├── About.tsx           # About section
│   ├── Portfolio.tsx       # Portfolio showcase
│   ├── Shop.tsx            # Shop with cart
│   └── Contact.tsx         # Contact form
└── lib/
    └── utils.ts            # Utility functions and animations
```

## 🎯 Sections

1. **Hero Section**: Eye-catching introduction with animated elements
2. **About Section**: Skills showcase with animated progress bars
3. **Portfolio Section**: Filterable project gallery with hover effects
4. **Shop Section**: Product showcase with shopping cart functionality
5. **Contact Section**: Contact form and information
6. **Footer**: Links and newsletter signup

## 🎨 Customization

### Colors
The color scheme can be customized in `tailwind.config.ts`:
- Primary colors (Purple): `primary-*`
- Secondary colors (Yellow): `secondary-*`

### Animations
Animation variants are defined in `src/lib/utils.ts` and can be customized:
- `fadeInUp`, `fadeInLeft`, `fadeInRight`
- `scaleIn`, `staggerContainer`
- Custom keyframes in `globals.css`

### Content
Update content in respective component files:
- Portfolio items in `Portfolio.tsx`
- Products in `Shop.tsx`
- Contact information in `Contact.tsx`

## 📱 Responsive Design

The website is fully responsive with breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 🔧 Performance Features

- **Next.js App Router**: Latest Next.js features
- **Image Optimization**: Automatic image optimization
- **Code Splitting**: Automatic code splitting
- **SEO Optimized**: Meta tags and structured data

## 🌟 Animation Features

- **Page Transitions**: Smooth transitions between sections
- **Hover Effects**: Interactive hover states
- **Loading States**: Beautiful loading animations
- **Scroll Animations**: Elements animate on scroll
- **Micro-interactions**: Button clicks, form interactions

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or create an issue on GitHub.

---

Built with ❤️ using Next.js, Tailwind CSS, and Framer Motion
