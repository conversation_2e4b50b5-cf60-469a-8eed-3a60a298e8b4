'use client';

import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon,
  EnvelopeIcon,
  ArrowDownTrayIcon,
  HomeIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import confetti from 'canvas-confetti';

const CheckoutSuccessPage: React.FC = () => {
  useEffect(() => {
    // Trigger confetti animation
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(function() {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);
      
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      }));
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      }));
    }, 250);

    return () => clearInterval(interval);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-green-50 via-white to-primary-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center"
        >
          {/* Success Icon */}
          <motion.div
            variants={itemVariants}
            className="mb-8"
          >
            <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircleIcon className="w-12 h-12 text-green-600" />
            </div>
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
              Order <span className="bg-gradient-to-r from-green-600 to-primary-600 bg-clip-text text-transparent">Successful!</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Thank you for your purchase! Your order has been processed successfully and you'll receive a confirmation email shortly.
            </p>
          </motion.div>

          {/* Order Details */}
          <motion.div
            variants={itemVariants}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8 max-w-2xl mx-auto"
          >
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Order Details</h2>
            
            <div className="space-y-4 text-left">
              <div className="flex justify-between items-center py-3 border-b border-gray-200">
                <span className="text-gray-600">Order Number:</span>
                <span className="font-semibold text-gray-900">#ELI-{Date.now().toString().slice(-6)}</span>
              </div>
              <div className="flex justify-between items-center py-3 border-b border-gray-200">
                <span className="text-gray-600">Order Date:</span>
                <span className="font-semibold text-gray-900">{new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between items-center py-3 border-b border-gray-200">
                <span className="text-gray-600">Payment Method:</span>
                <span className="font-semibold text-gray-900">Credit Card (**** 3456)</span>
              </div>
              <div className="flex justify-between items-center py-3">
                <span className="text-gray-600">Total Amount:</span>
                <span className="font-bold text-2xl text-green-600">$89.99</span>
              </div>
            </div>
          </motion.div>

          {/* Next Steps */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          >
            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <EnvelopeIcon className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Check Your Email</h3>
              <p className="text-gray-600 text-sm">
                We've sent a confirmation email with your receipt and download links.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <ArrowDownTrayIcon className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Download Files</h3>
              <p className="text-gray-600 text-sm">
                Your digital products are ready for immediate download.
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <CheckCircleIcon className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Support</h3>
              <p className="text-gray-600 text-sm">
                Need help? Our support team is here to assist you 24/7.
              </p>
            </div>
          </motion.div>

          {/* Download Section */}
          <motion.div
            variants={itemVariants}
            className="bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 text-white mb-8"
          >
            <h2 className="text-2xl font-bold mb-4">Your Downloads Are Ready!</h2>
            <p className="text-lg opacity-90 mb-6">
              Click the button below to access your purchased digital products.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-primary-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-2 mx-auto"
            >
              <ArrowDownTrayIcon className="w-6 h-6" />
              <span>Download Now</span>
            </motion.button>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              href="/"
              className="inline-flex items-center space-x-2 bg-gray-100 text-gray-700 px-6 py-3 rounded-xl font-semibold hover:bg-gray-200 transition-colors duration-200"
            >
              <HomeIcon className="w-5 h-5" />
              <span>Back to Home</span>
            </Link>
            
            <Link
              href="/shop"
              className="inline-flex items-center space-x-2 bg-purple-yellow-gradient text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-200"
            >
              <ShoppingBagIcon className="w-5 h-5" />
              <span>Continue Shopping</span>
            </Link>
          </motion.div>

          {/* Additional Info */}
          <motion.div
            variants={itemVariants}
            className="mt-12 p-6 bg-gray-50 rounded-xl max-w-2xl mx-auto"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-3">What happens next?</h3>
            <div className="text-left space-y-2 text-gray-600">
              <p>• You'll receive an email confirmation within 5 minutes</p>
              <p>• Download links will be available for 30 days</p>
              <p>• All purchases include free updates for 1 year</p>
              <p>• 30-day money-back guarantee applies</p>
            </div>
          </motion.div>

          {/* Contact Support */}
          <motion.div
            variants={itemVariants}
            className="mt-8 text-center"
          >
            <p className="text-gray-600 mb-2">Need help with your order?</p>
            <Link
              href="/contact"
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              Contact our support team
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default CheckoutSuccessPage;
