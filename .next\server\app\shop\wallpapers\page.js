/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/shop/wallpapers/page";
exports.ids = ["app/shop/wallpapers/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fwallpapers%2Fpage&page=%2Fshop%2Fwallpapers%2Fpage&appPaths=%2Fshop%2Fwallpapers%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fwallpapers%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fwallpapers%2Fpage&page=%2Fshop%2Fwallpapers%2Fpage&appPaths=%2Fshop%2Fwallpapers%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fwallpapers%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'shop',\n        {\n        children: [\n        'wallpapers',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shop/wallpapers/page.tsx */ \"(rsc)/./src/app/shop/wallpapers/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/shop/wallpapers/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/shop/wallpapers/page\",\n        pathname: \"/shop/wallpapers\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fwallpapers%2Fpage&page=%2Fshop%2Fwallpapers%2Fpage&appPaths=%2Fshop%2Fwallpapers%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fwallpapers%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/CartContext.tsx */ \"(ssr)/./src/contexts/CartContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQURNTkklNUNEb2N1bWVudHMlNUNlbGlzaG9wJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQURNTkklNUNEb2N1bWVudHMlNUNlbGlzaG9wJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNBRE1OSSU1Q0RvY3VtZW50cyU1Q2VsaXNob3AlNUNzcmMlNUNjb21wb25lbnRzJTVDbGF5b3V0JTVDRm9vdGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0FETU5JJTVDRG9jdW1lbnRzJTVDZWxpc2hvcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNsYXlvdXQlNUNIZWFkZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQURNTkklNUNEb2N1bWVudHMlNUNlbGlzaG9wJTVDc3JjJTVDY29udGV4dHMlNUNDYXJ0Q29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUE4RztBQUM5RyxnTEFBOEc7QUFDOUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGlzaG9wLXBvcnRmb2xpby8/YzQyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFETU5JXFxcXERvY3VtZW50c1xcXFxlbGlzaG9wXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxGb290ZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBRE1OSVxcXFxEb2N1bWVudHNcXFxcZWxpc2hvcFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQURNTklcXFxcRG9jdW1lbnRzXFxcXGVsaXNob3BcXFxcc3JjXFxcXGNvbnRleHRzXFxcXENhcnRDb250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CFooter.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccomponents%5Clayout%5CHeader.tsx&modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Ccontexts%5CCartContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cshop%5Cwallpapers%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cshop%5Cwallpapers%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shop/wallpapers/page.tsx */ \"(ssr)/./src/app/shop/wallpapers/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQURNTkklNUNEb2N1bWVudHMlNUNlbGlzaG9wJTVDc3JjJTVDYXBwJTVDc2hvcCU1Q3dhbGxwYXBlcnMlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGlzaG9wLXBvcnRmb2xpby8/NDE2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFETU5JXFxcXERvY3VtZW50c1xcXFxlbGlzaG9wXFxcXHNyY1xcXFxhcHBcXFxcc2hvcFxcXFx3YWxscGFwZXJzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp%5Cshop%5Cwallpapers%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComputerDesktopIcon: () => (/* reexport safe */ _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   DevicePhoneMobileIcon: () => (/* reexport safe */ _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   DeviceTabletIcon: () => (/* reexport safe */ _DeviceTabletIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   EyeIcon: () => (/* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MagnifyingGlassIcon: () => (/* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ComputerDesktopIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DevicePhoneMobileIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _DeviceTabletIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DeviceTabletIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DeviceTabletIcon.js\");\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EyeIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./XMarkIcon.js */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db21wdXRlckRlc2t0b3BJY29uLERldmljZVBob25lTW9iaWxlSWNvbixEZXZpY2VUYWJsZXRJY29uLERvd25sb2FkSWNvbixFeWVJY29uLE1hZ25pZnlpbmdHbGFzc0ljb24sWE1hcmtJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFDeUU7QUFDSTtBQUNWO0FBQ2xCO0FBQ3dCO0FBQ3BCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxpc2hvcC1wb3J0Zm9saW8vLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8zY2EwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb21wdXRlckRlc2t0b3BJY29uIH0gZnJvbSBcIi4vQ29tcHV0ZXJEZXNrdG9wSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERldmljZVBob25lTW9iaWxlSWNvbiB9IGZyb20gXCIuL0RldmljZVBob25lTW9iaWxlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERldmljZVRhYmxldEljb24gfSBmcm9tIFwiLi9EZXZpY2VUYWJsZXRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllSWNvbiB9IGZyb20gXCIuL0V5ZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSBcIi4vTWFnbmlmeWluZ0dsYXNzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJDb21wdXRlckRlc2t0b3BJY29uIiwiRGV2aWNlUGhvbmVNb2JpbGVJY29uIiwiRGV2aWNlVGFibGV0SWNvbiIsIkV5ZUljb24iLCJNYWduaWZ5aW5nR2xhc3NJY29uIiwiWE1hcmtJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./src/app/shop/wallpapers/page.tsx":
/*!******************************************!*\
  !*** ./src/app/shop/wallpapers/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ListBulletIcon,MagnifyingGlassIcon,Squares2X2Icon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallpaper/WallpaperGrid */ \"(ssr)/./src/components/wallpaper/WallpaperGrid.tsx\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/product */ \"(ssr)/./src/types/product.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst WallpapersPage = ()=>{\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"grid\");\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Mock wallpaper data - in real app, this would come from your database\n    const mockWallpapers = [\n        {\n            id: \"1\",\n            title: \"Abstract Neon Waves\",\n            description: \"Vibrant neon waves flowing across a dark background, perfect for modern setups\",\n            price: 4.99,\n            originalPrice: 7.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"abstract\",\n                \"neon\",\n                \"waves\",\n                \"modern\"\n            ],\n            inStock: true,\n            featured: true,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"15-25 MB\",\n            createdAt: \"2024-01-01T00:00:00Z\",\n            updatedAt: \"2024-01-01T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"25 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"18 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"12 MB\"\n                    },\n                    {\n                        width: 1080,\n                        height: 2340,\n                        label: \"Mobile\",\n                        downloadUrl: \"/downloads/mobile\",\n                        fileSize: \"8 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#FF00FF\",\n                    \"#00FFFF\",\n                    \"#FF6B6B\",\n                    \"#4ECDC4\",\n                    \"#1A1A1A\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.ABSTRACT,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.MOBILE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 1250\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Minimalist Mountain Range\",\n            description: \"Clean, minimalist mountain silhouettes with gradient sky\",\n            price: 3.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"minimalist\",\n                \"mountains\",\n                \"nature\",\n                \"gradient\"\n            ],\n            inStock: true,\n            featured: false,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"10-20 MB\",\n            createdAt: \"2024-01-02T00:00:00Z\",\n            updatedAt: \"2024-01-02T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"20 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"15 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"10 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#87CEEB\",\n                    \"#FFB6C1\",\n                    \"#DDA0DD\",\n                    \"#F0F8FF\",\n                    \"#2F4F4F\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.MINIMALIST,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.ULTRAWIDE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 890\n            }\n        },\n        {\n            id: \"3\",\n            title: \"Gaming RGB Setup\",\n            description: \"Dynamic RGB lighting effects perfect for gaming setups\",\n            price: 5.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"gaming\",\n                \"rgb\",\n                \"neon\",\n                \"tech\"\n            ],\n            inStock: true,\n            featured: true,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\",\n                \"PNG\"\n            ],\n            fileSize: \"20-30 MB\",\n            createdAt: \"2024-01-03T00:00:00Z\",\n            updatedAt: \"2024-01-03T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"30 MB\"\n                    },\n                    {\n                        width: 2560,\n                        height: 1440,\n                        label: \"1440p\",\n                        downloadUrl: \"/downloads/1440p\",\n                        fileSize: \"22 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"15 MB\"\n                    },\n                    {\n                        width: 3440,\n                        height: 1440,\n                        label: \"Ultrawide\",\n                        downloadUrl: \"/downloads/ultrawide\",\n                        fileSize: \"25 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#FF0080\",\n                    \"#00FF80\",\n                    \"#8000FF\",\n                    \"#FF8000\",\n                    \"#000000\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.GAMING,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.ULTRAWIDE\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 2100\n            }\n        },\n        {\n            id: \"4\",\n            title: \"Nature Forest Path\",\n            description: \"Serene forest path with morning sunlight filtering through trees\",\n            price: 2.99,\n            category: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductCategory.WALLPAPERS,\n            type: _types_product__WEBPACK_IMPORTED_MODULE_3__.ProductType.DIGITAL,\n            images: [\n                \"/api/placeholder/400/600\"\n            ],\n            tags: [\n                \"nature\",\n                \"forest\",\n                \"peaceful\",\n                \"photography\"\n            ],\n            inStock: true,\n            featured: false,\n            isDigital: true,\n            fileFormat: [\n                \"JPG\"\n            ],\n            fileSize: \"12-18 MB\",\n            createdAt: \"2024-01-04T00:00:00Z\",\n            updatedAt: \"2024-01-04T00:00:00Z\",\n            wallpaperData: {\n                resolutions: [\n                    {\n                        width: 3840,\n                        height: 2160,\n                        label: \"4K\",\n                        downloadUrl: \"/downloads/4k\",\n                        fileSize: \"18 MB\"\n                    },\n                    {\n                        width: 1920,\n                        height: 1080,\n                        label: \"1080p\",\n                        downloadUrl: \"/downloads/1080p\",\n                        fileSize: \"12 MB\"\n                    },\n                    {\n                        width: 1080,\n                        height: 2340,\n                        label: \"Mobile\",\n                        downloadUrl: \"/downloads/mobile\",\n                        fileSize: \"6 MB\"\n                    }\n                ],\n                aspectRatio: \"16:9\",\n                colorPalette: [\n                    \"#228B22\",\n                    \"#8FBC8F\",\n                    \"#F5DEB3\",\n                    \"#DEB887\",\n                    \"#654321\"\n                ],\n                style: _types_product__WEBPACK_IMPORTED_MODULE_3__.WallpaperStyle.NATURE,\n                deviceCompatibility: [\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.DESKTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.LAPTOP,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.MOBILE,\n                    _types_product__WEBPACK_IMPORTED_MODULE_3__.DeviceType.TABLET\n                ],\n                previewUrl: \"/api/placeholder/800/600\",\n                watermarkedPreview: \"/api/placeholder/400/600\",\n                downloadCount: 756\n            }\n        }\n    ];\n    // Filter and search logic\n    const filteredWallpapers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = mockWallpapers;\n        // Search filter\n        if (searchTerm) {\n            filtered = filtered.filter((product)=>product.title.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase()) || product.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase())));\n        }\n        // Style filter\n        if (filters.style && filters.style.length > 0) {\n            filtered = filtered.filter((product)=>product.wallpaperData?.style && filters.style.includes(product.wallpaperData.style));\n        }\n        // Device filter\n        if (filters.devices && filters.devices.length > 0) {\n            filtered = filtered.filter((product)=>product.wallpaperData?.deviceCompatibility.some((device)=>filters.devices.includes(device)));\n        }\n        // Aspect ratio filter\n        if (filters.aspectRatio && filters.aspectRatio.length > 0) {\n            filtered = filtered.filter((product)=>product.wallpaperData?.aspectRatio && filters.aspectRatio.includes(product.wallpaperData.aspectRatio));\n        }\n        // Color filter\n        if (filters.colors && filters.colors.length > 0) {\n            filtered = filtered.filter((product)=>product.wallpaperData?.colorPalette.some((color)=>filters.colors.some((filterColor)=>color.toLowerCase() === filterColor.toLowerCase())));\n        }\n        // Price range filter\n        if (filters.priceRange) {\n            filtered = filtered.filter((product)=>product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]);\n        }\n        // Sort\n        if (filters.sortBy) {\n            filtered.sort((a, b)=>{\n                switch(filters.sortBy){\n                    case \"price-low\":\n                        return a.price - b.price;\n                    case \"price-high\":\n                        return b.price - a.price;\n                    case \"downloads\":\n                        return (b.wallpaperData?.downloadCount || 0) - (a.wallpaperData?.downloadCount || 0);\n                    case \"popular\":\n                        return (b.wallpaperData?.downloadCount || 0) - (a.wallpaperData?.downloadCount || 0);\n                    case \"newest\":\n                    default:\n                        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                }\n            });\n        }\n        return filtered;\n    }, [\n        mockWallpapers,\n        searchTerm,\n        filters\n    ]);\n    const handleAddToCart = (product)=>{\n        console.log(\"Added to cart:\", product.title);\n    // In real app, this would add to cart context\n    };\n    const handleToggleFavorite = (productId)=>{\n        setFavorites((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Premium Wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Transform your devices with our collection of high-quality wallpapers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: filters.sortBy || \"newest\",\n                                    onChange: (e)=>setFilters({\n                                            ...filters,\n                                            sortBy: e.target.value\n                                        }),\n                                    className: \"px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"newest\",\n                                            children: \"Newest\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"popular\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"downloads\",\n                                            children: \"Most Downloaded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-low\",\n                                            children: \"Price: Low to High\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"price-high\",\n                                            children: \"Price: High to Low\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex border border-gray-300 rounded-xl overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"grid\"),\n                                            className: `p-3 ${viewMode === \"grid\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setViewMode(\"list\"),\n                                            className: `p-3 ${viewMode === \"list\" ? \"bg-primary-600 text-white\" : \"bg-white text-gray-600 hover:bg-gray-50\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"flex items-center space-x-2 px-4 py-3 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ListBulletIcon_MagnifyingGlassIcon_Squares2X2Icon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8\",\n                    children: [\n                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            className: \"w-80 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__.WallpaperFiltersPanel, {\n                                filters: filters,\n                                onFiltersChange: setFilters,\n                                onClearFilters: ()=>setFilters({})\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                filteredWallpapers.length,\n                                                \" wallpaper\",\n                                                filteredWallpapers.length !== 1 ? \"s\" : \"\",\n                                                \" found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        Object.keys(filters).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilters({}),\n                                            className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Clear all filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallpaper_WallpaperGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    products: filteredWallpapers,\n                                    onAddToCart: handleAddToCart,\n                                    onToggleFavorite: handleToggleFavorite,\n                                    favorites: favorites\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDDBC️\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No wallpapers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-4\",\n                                            children: \"Try adjusting your search or filter criteria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setSearchTerm(\"\");\n                                                setFilters({});\n                                            },\n                                            className: \"px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200\",\n                                            children: \"Clear Search & Filters\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\shop\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WallpapersPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Nob3Avd2FsbHBhcGVycy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRWlEO0FBQ1Y7QUFPRjtBQUN5RTtBQUNWO0FBRXBHLE1BQU1jLGlCQUEyQjtJQUMvQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0IsU0FBU0MsV0FBVyxHQUFHakIsK0NBQVFBLENBQW1CLENBQUM7SUFDMUQsTUFBTSxDQUFDa0IsYUFBYUMsZUFBZSxHQUFHbkIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHckIsK0NBQVFBLENBQWtCO0lBQzFELE1BQU0sQ0FBQ3NCLFdBQVdDLGFBQWEsR0FBR3ZCLCtDQUFRQSxDQUFXLEVBQUU7SUFFdkQsd0VBQXdFO0lBQ3hFLE1BQU13QixpQkFBNEI7UUFDaEM7WUFDRUMsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztZQUNQQyxlQUFlO1lBQ2ZDLFVBQVVyQiwyREFBZUEsQ0FBQ3NCLFVBQVU7WUFDcENDLE1BQU10Qix1REFBV0EsQ0FBQ3VCLE9BQU87WUFDekJDLFFBQVE7Z0JBQUM7YUFBMkI7WUFDcENDLE1BQU07Z0JBQUM7Z0JBQVk7Z0JBQVE7Z0JBQVM7YUFBUztZQUM3Q0MsU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsWUFBWTtnQkFBQztnQkFBTzthQUFNO1lBQzFCQyxVQUFVO1lBQ1ZDLFdBQVc7WUFDWEMsV0FBVztZQUNYQyxlQUFlO2dCQUNiQyxhQUFhO29CQUNYO3dCQUFFQyxPQUFPO3dCQUFNQyxRQUFRO3dCQUFNQyxPQUFPO3dCQUFNQyxhQUFhO3dCQUFpQlIsVUFBVTtvQkFBUTtvQkFDMUY7d0JBQUVLLE9BQU87d0JBQU1DLFFBQVE7d0JBQU1DLE9BQU87d0JBQVNDLGFBQWE7d0JBQW9CUixVQUFVO29CQUFRO29CQUNoRzt3QkFBRUssT0FBTzt3QkFBTUMsUUFBUTt3QkFBTUMsT0FBTzt3QkFBU0MsYUFBYTt3QkFBb0JSLFVBQVU7b0JBQVE7b0JBQ2hHO3dCQUFFSyxPQUFPO3dCQUFNQyxRQUFRO3dCQUFNQyxPQUFPO3dCQUFVQyxhQUFhO3dCQUFxQlIsVUFBVTtvQkFBTztpQkFDbEc7Z0JBQ0RTLGFBQWE7Z0JBQ2JDLGNBQWM7b0JBQUM7b0JBQVc7b0JBQVc7b0JBQVc7b0JBQVc7aUJBQVU7Z0JBQ3JFQyxPQUFPeEMsMERBQWNBLENBQUN5QyxRQUFRO2dCQUM5QkMscUJBQXFCO29CQUFDekMsc0RBQVVBLENBQUMwQyxPQUFPO29CQUFFMUMsc0RBQVVBLENBQUMyQyxNQUFNO29CQUFFM0Msc0RBQVVBLENBQUM0QyxNQUFNO2lCQUFDO2dCQUMvRUMsWUFBWTtnQkFDWkMsb0JBQW9CO2dCQUNwQkMsZUFBZTtZQUNqQjtRQUNGO1FBQ0E7WUFDRWxDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE9BQU87WUFDUEUsVUFBVXJCLDJEQUFlQSxDQUFDc0IsVUFBVTtZQUNwQ0MsTUFBTXRCLHVEQUFXQSxDQUFDdUIsT0FBTztZQUN6QkMsUUFBUTtnQkFBQzthQUEyQjtZQUNwQ0MsTUFBTTtnQkFBQztnQkFBYztnQkFBYTtnQkFBVTthQUFXO1lBQ3ZEQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxZQUFZO2dCQUFDO2dCQUFPO2FBQU07WUFDMUJDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxXQUFXO1lBQ1hDLGVBQWU7Z0JBQ2JDLGFBQWE7b0JBQ1g7d0JBQUVDLE9BQU87d0JBQU1DLFFBQVE7d0JBQU1DLE9BQU87d0JBQU1DLGFBQWE7d0JBQWlCUixVQUFVO29CQUFRO29CQUMxRjt3QkFBRUssT0FBTzt3QkFBTUMsUUFBUTt3QkFBTUMsT0FBTzt3QkFBU0MsYUFBYTt3QkFBb0JSLFVBQVU7b0JBQVE7b0JBQ2hHO3dCQUFFSyxPQUFPO3dCQUFNQyxRQUFRO3dCQUFNQyxPQUFPO3dCQUFTQyxhQUFhO3dCQUFvQlIsVUFBVTtvQkFBUTtpQkFDakc7Z0JBQ0RTLGFBQWE7Z0JBQ2JDLGNBQWM7b0JBQUM7b0JBQVc7b0JBQVc7b0JBQVc7b0JBQVc7aUJBQVU7Z0JBQ3JFQyxPQUFPeEMsMERBQWNBLENBQUNpRCxVQUFVO2dCQUNoQ1AscUJBQXFCO29CQUFDekMsc0RBQVVBLENBQUMwQyxPQUFPO29CQUFFMUMsc0RBQVVBLENBQUMyQyxNQUFNO29CQUFFM0Msc0RBQVVBLENBQUNpRCxTQUFTO2lCQUFDO2dCQUNsRkosWUFBWTtnQkFDWkMsb0JBQW9CO2dCQUNwQkMsZUFBZTtZQUNqQjtRQUNGO1FBQ0E7WUFDRWxDLElBQUk7WUFDSkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE9BQU87WUFDUEUsVUFBVXJCLDJEQUFlQSxDQUFDc0IsVUFBVTtZQUNwQ0MsTUFBTXRCLHVEQUFXQSxDQUFDdUIsT0FBTztZQUN6QkMsUUFBUTtnQkFBQzthQUEyQjtZQUNwQ0MsTUFBTTtnQkFBQztnQkFBVTtnQkFBTztnQkFBUTthQUFPO1lBQ3ZDQyxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxZQUFZO2dCQUFDO2dCQUFPO2FBQU07WUFDMUJDLFVBQVU7WUFDVkMsV0FBVztZQUNYQyxXQUFXO1lBQ1hDLGVBQWU7Z0JBQ2JDLGFBQWE7b0JBQ1g7d0JBQUVDLE9BQU87d0JBQU1DLFFBQVE7d0JBQU1DLE9BQU87d0JBQU1DLGFBQWE7d0JBQWlCUixVQUFVO29CQUFRO29CQUMxRjt3QkFBRUssT0FBTzt3QkFBTUMsUUFBUTt3QkFBTUMsT0FBTzt3QkFBU0MsYUFBYTt3QkFBb0JSLFVBQVU7b0JBQVE7b0JBQ2hHO3dCQUFFSyxPQUFPO3dCQUFNQyxRQUFRO3dCQUFNQyxPQUFPO3dCQUFTQyxhQUFhO3dCQUFvQlIsVUFBVTtvQkFBUTtvQkFDaEc7d0JBQUVLLE9BQU87d0JBQU1DLFFBQVE7d0JBQU1DLE9BQU87d0JBQWFDLGFBQWE7d0JBQXdCUixVQUFVO29CQUFRO2lCQUN6RztnQkFDRFMsYUFBYTtnQkFDYkMsY0FBYztvQkFBQztvQkFBVztvQkFBVztvQkFBVztvQkFBVztpQkFBVTtnQkFDckVDLE9BQU94QywwREFBY0EsQ0FBQ21ELE1BQU07Z0JBQzVCVCxxQkFBcUI7b0JBQUN6QyxzREFBVUEsQ0FBQzBDLE9BQU87b0JBQUUxQyxzREFBVUEsQ0FBQzJDLE1BQU07b0JBQUUzQyxzREFBVUEsQ0FBQ2lELFNBQVM7aUJBQUM7Z0JBQ2xGSixZQUFZO2dCQUNaQyxvQkFBb0I7Z0JBQ3BCQyxlQUFlO1lBQ2pCO1FBQ0Y7UUFDQTtZQUNFbEMsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztZQUNQRSxVQUFVckIsMkRBQWVBLENBQUNzQixVQUFVO1lBQ3BDQyxNQUFNdEIsdURBQVdBLENBQUN1QixPQUFPO1lBQ3pCQyxRQUFRO2dCQUFDO2FBQTJCO1lBQ3BDQyxNQUFNO2dCQUFDO2dCQUFVO2dCQUFVO2dCQUFZO2FBQWM7WUFDckRDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxXQUFXO1lBQ1hDLFlBQVk7Z0JBQUM7YUFBTTtZQUNuQkMsVUFBVTtZQUNWQyxXQUFXO1lBQ1hDLFdBQVc7WUFDWEMsZUFBZTtnQkFDYkMsYUFBYTtvQkFDWDt3QkFBRUMsT0FBTzt3QkFBTUMsUUFBUTt3QkFBTUMsT0FBTzt3QkFBTUMsYUFBYTt3QkFBaUJSLFVBQVU7b0JBQVE7b0JBQzFGO3dCQUFFSyxPQUFPO3dCQUFNQyxRQUFRO3dCQUFNQyxPQUFPO3dCQUFTQyxhQUFhO3dCQUFvQlIsVUFBVTtvQkFBUTtvQkFDaEc7d0JBQUVLLE9BQU87d0JBQU1DLFFBQVE7d0JBQU1DLE9BQU87d0JBQVVDLGFBQWE7d0JBQXFCUixVQUFVO29CQUFPO2lCQUNsRztnQkFDRFMsYUFBYTtnQkFDYkMsY0FBYztvQkFBQztvQkFBVztvQkFBVztvQkFBVztvQkFBVztpQkFBVTtnQkFDckVDLE9BQU94QywwREFBY0EsQ0FBQ29ELE1BQU07Z0JBQzVCVixxQkFBcUI7b0JBQUN6QyxzREFBVUEsQ0FBQzBDLE9BQU87b0JBQUUxQyxzREFBVUEsQ0FBQzJDLE1BQU07b0JBQUUzQyxzREFBVUEsQ0FBQzRDLE1BQU07b0JBQUU1QyxzREFBVUEsQ0FBQ29ELE1BQU07aUJBQUM7Z0JBQ2xHUCxZQUFZO2dCQUNaQyxvQkFBb0I7Z0JBQ3BCQyxlQUFlO1lBQ2pCO1FBQ0Y7S0FDRDtJQUVELDBCQUEwQjtJQUMxQixNQUFNTSxxQkFBcUJoRSw4Q0FBT0EsQ0FBQztRQUNqQyxJQUFJaUUsV0FBVzFDO1FBRWYsZ0JBQWdCO1FBQ2hCLElBQUlWLFlBQVk7WUFDZG9ELFdBQVdBLFNBQVNDLE1BQU0sQ0FBQ0MsQ0FBQUEsVUFDekJBLFFBQVExQyxLQUFLLENBQUMyQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3hELFdBQVd1RCxXQUFXLE9BQzNERCxRQUFRekMsV0FBVyxDQUFDMEMsV0FBVyxHQUFHQyxRQUFRLENBQUN4RCxXQUFXdUQsV0FBVyxPQUNqRUQsUUFBUWpDLElBQUksQ0FBQ29DLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUgsV0FBVyxHQUFHQyxRQUFRLENBQUN4RCxXQUFXdUQsV0FBVztRQUU5RTtRQUVBLGVBQWU7UUFDZixJQUFJckQsUUFBUW1DLEtBQUssSUFBSW5DLFFBQVFtQyxLQUFLLENBQUNzQixNQUFNLEdBQUcsR0FBRztZQUM3Q1AsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxVQUN6QkEsUUFBUXpCLGFBQWEsRUFBRVEsU0FBU25DLFFBQVFtQyxLQUFLLENBQUVtQixRQUFRLENBQUNGLFFBQVF6QixhQUFhLENBQUNRLEtBQUs7UUFFdkY7UUFFQSxnQkFBZ0I7UUFDaEIsSUFBSW5DLFFBQVEwRCxPQUFPLElBQUkxRCxRQUFRMEQsT0FBTyxDQUFDRCxNQUFNLEdBQUcsR0FBRztZQUNqRFAsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxVQUN6QkEsUUFBUXpCLGFBQWEsRUFBRVUsb0JBQW9Ca0IsS0FBS0ksQ0FBQUEsU0FDOUMzRCxRQUFRMEQsT0FBTyxDQUFFSixRQUFRLENBQUNLO1FBR2hDO1FBRUEsc0JBQXNCO1FBQ3RCLElBQUkzRCxRQUFRaUMsV0FBVyxJQUFJakMsUUFBUWlDLFdBQVcsQ0FBQ3dCLE1BQU0sR0FBRyxHQUFHO1lBQ3pEUCxXQUFXQSxTQUFTQyxNQUFNLENBQUNDLENBQUFBLFVBQ3pCQSxRQUFRekIsYUFBYSxFQUFFTSxlQUFlakMsUUFBUWlDLFdBQVcsQ0FBRXFCLFFBQVEsQ0FBQ0YsUUFBUXpCLGFBQWEsQ0FBQ00sV0FBVztRQUV6RztRQUVBLGVBQWU7UUFDZixJQUFJakMsUUFBUTRELE1BQU0sSUFBSTVELFFBQVE0RCxNQUFNLENBQUNILE1BQU0sR0FBRyxHQUFHO1lBQy9DUCxXQUFXQSxTQUFTQyxNQUFNLENBQUNDLENBQUFBLFVBQ3pCQSxRQUFRekIsYUFBYSxFQUFFTyxhQUFhcUIsS0FBS00sQ0FBQUEsUUFDdkM3RCxRQUFRNEQsTUFBTSxDQUFFTCxJQUFJLENBQUNPLENBQUFBLGNBQ25CRCxNQUFNUixXQUFXLE9BQU9TLFlBQVlULFdBQVc7UUFJdkQ7UUFFQSxxQkFBcUI7UUFDckIsSUFBSXJELFFBQVErRCxVQUFVLEVBQUU7WUFDdEJiLFdBQVdBLFNBQVNDLE1BQU0sQ0FBQ0MsQ0FBQUEsVUFDekJBLFFBQVF4QyxLQUFLLElBQUlaLFFBQVErRCxVQUFVLENBQUUsRUFBRSxJQUFJWCxRQUFReEMsS0FBSyxJQUFJWixRQUFRK0QsVUFBVSxDQUFFLEVBQUU7UUFFdEY7UUFFQSxPQUFPO1FBQ1AsSUFBSS9ELFFBQVFnRSxNQUFNLEVBQUU7WUFDbEJkLFNBQVNlLElBQUksQ0FBQyxDQUFDQyxHQUFHQztnQkFDaEIsT0FBUW5FLFFBQVFnRSxNQUFNO29CQUNwQixLQUFLO3dCQUNILE9BQU9FLEVBQUV0RCxLQUFLLEdBQUd1RCxFQUFFdkQsS0FBSztvQkFDMUIsS0FBSzt3QkFDSCxPQUFPdUQsRUFBRXZELEtBQUssR0FBR3NELEVBQUV0RCxLQUFLO29CQUMxQixLQUFLO3dCQUNILE9BQU8sQ0FBQ3VELEVBQUV4QyxhQUFhLEVBQUVnQixpQkFBaUIsS0FBTXVCLENBQUFBLEVBQUV2QyxhQUFhLEVBQUVnQixpQkFBaUI7b0JBQ3BGLEtBQUs7d0JBQ0gsT0FBTyxDQUFDd0IsRUFBRXhDLGFBQWEsRUFBRWdCLGlCQUFpQixLQUFNdUIsQ0FBQUEsRUFBRXZDLGFBQWEsRUFBRWdCLGlCQUFpQjtvQkFDcEYsS0FBSztvQkFDTDt3QkFDRSxPQUFPLElBQUl5QixLQUFLRCxFQUFFMUMsU0FBUyxFQUFFNEMsT0FBTyxLQUFLLElBQUlELEtBQUtGLEVBQUV6QyxTQUFTLEVBQUU0QyxPQUFPO2dCQUMxRTtZQUNGO1FBQ0Y7UUFFQSxPQUFPbkI7SUFDVCxHQUFHO1FBQUMxQztRQUFnQlY7UUFBWUU7S0FBUTtJQUV4QyxNQUFNc0Usa0JBQWtCLENBQUNsQjtRQUN2Qm1CLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JwQixRQUFRMUMsS0FBSztJQUMzQyw4Q0FBOEM7SUFDaEQ7SUFFQSxNQUFNK0QsdUJBQXVCLENBQUNDO1FBQzVCbkUsYUFBYW9FLENBQUFBLE9BQ1hBLEtBQUtyQixRQUFRLENBQUNvQixhQUNWQyxLQUFLeEIsTUFBTSxDQUFDMUMsQ0FBQUEsS0FBTUEsT0FBT2lFLGFBQ3pCO21CQUFJQztnQkFBTUQ7YUFBVTtJQUU1QjtJQUVBLHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDM0YsaURBQU1BLENBQUM0RixFQUFFOzRCQUNSQyxTQUFTO2dDQUFFQyxTQUFTO2dDQUFHQyxHQUFHOzRCQUFHOzRCQUM3QkMsU0FBUztnQ0FBRUYsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRTs0QkFDNUJKLFdBQVU7c0NBQ1g7Ozs7OztzQ0FHRCw4REFBQzNGLGlEQUFNQSxDQUFDaUcsQ0FBQzs0QkFDUEosU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLFNBQVM7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQzVCRyxZQUFZO2dDQUFFQyxPQUFPOzRCQUFJOzRCQUN6QlIsV0FBVTtzQ0FDWDs7Ozs7Ozs7Ozs7OzhCQU1ILDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMxRixxS0FBbUJBO3dDQUFDMEYsV0FBVTs7Ozs7O2tEQUMvQiw4REFBQ1M7d0NBQ0N0RSxNQUFLO3dDQUNMdUUsYUFBWTt3Q0FDWkMsT0FBTzFGO3dDQUNQMkYsVUFBVSxDQUFDQyxJQUFNM0YsY0FBYzJGLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDN0NYLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1oQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDZTtvQ0FDQ0osT0FBT3hGLFFBQVFnRSxNQUFNLElBQUk7b0NBQ3pCeUIsVUFBVSxDQUFDQyxJQUFNekYsV0FBVzs0Q0FBRSxHQUFHRCxPQUFPOzRDQUFFZ0UsUUFBUTBCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FBUTtvQ0FDeEVYLFdBQVU7O3NEQUVWLDhEQUFDZ0I7NENBQU9MLE9BQU07c0RBQVM7Ozs7OztzREFDdkIsOERBQUNLOzRDQUFPTCxPQUFNO3NEQUFVOzs7Ozs7c0RBQ3hCLDhEQUFDSzs0Q0FBT0wsT0FBTTtzREFBWTs7Ozs7O3NEQUMxQiw4REFBQ0s7NENBQU9MLE9BQU07c0RBQVk7Ozs7OztzREFDMUIsOERBQUNLOzRDQUFPTCxPQUFNO3NEQUFhOzs7Ozs7Ozs7Ozs7OENBSTdCLDhEQUFDWjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNpQjs0Q0FDQ0MsU0FBUyxJQUFNMUYsWUFBWTs0Q0FDM0J3RSxXQUFXLENBQUMsSUFBSSxFQUFFekUsYUFBYSxTQUFTLDhCQUE4QiwwQ0FBMEMsQ0FBQztzREFFakgsNEVBQUNmLHFLQUFjQTtnREFBQ3dGLFdBQVU7Ozs7Ozs7Ozs7O3NEQUU1Qiw4REFBQ2lCOzRDQUNDQyxTQUFTLElBQU0xRixZQUFZOzRDQUMzQndFLFdBQVcsQ0FBQyxJQUFJLEVBQUV6RSxhQUFhLFNBQVMsOEJBQThCLDBDQUEwQyxDQUFDO3NEQUVqSCw0RUFBQ2QscUtBQWNBO2dEQUFDdUYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzlCLDhEQUFDaUI7b0NBQ0NDLFNBQVMsSUFBTTVGLGVBQWUsQ0FBQ0Q7b0NBQy9CMkUsV0FBVTs7c0RBRVYsOERBQUN6RixxS0FBeUJBOzRDQUFDeUYsV0FBVTs7Ozs7O3NEQUNyQyw4REFBQ21CO3NEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS1osOERBQUNwQjtvQkFBSUMsV0FBVTs7d0JBRVozRSw2QkFDQyw4REFBQ2hCLGlEQUFNQSxDQUFDMEYsR0FBRzs0QkFDVEcsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR2lCLEdBQUcsQ0FBQzs0QkFBRzs0QkFDOUJmLFNBQVM7Z0NBQUVGLFNBQVM7Z0NBQUdpQixHQUFHOzRCQUFFOzRCQUM1QnBCLFdBQVU7c0NBRVYsNEVBQUNyRixzRkFBcUJBO2dDQUNwQlEsU0FBU0E7Z0NBQ1RrRyxpQkFBaUJqRztnQ0FDakJrRyxnQkFBZ0IsSUFBTWxHLFdBQVcsQ0FBQzs7Ozs7Ozs7Ozs7c0NBTXhDLDhEQUFDMkU7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNNOzRDQUFFTixXQUFVOztnREFDVjVCLG1CQUFtQlEsTUFBTTtnREFBQztnREFBV1IsbUJBQW1CUSxNQUFNLEtBQUssSUFBSSxNQUFNO2dEQUFHOzs7Ozs7O3dDQUVsRjJDLE9BQU9DLElBQUksQ0FBQ3JHLFNBQVN5RCxNQUFNLEdBQUcsbUJBQzdCLDhEQUFDcUM7NENBQ0NDLFNBQVMsSUFBTTlGLFdBQVcsQ0FBQzs0Q0FDM0I0RSxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7OENBT0wsOERBQUN0RiwyRUFBYUE7b0NBQ1orRyxVQUFVckQ7b0NBQ1ZzRCxhQUFhakM7b0NBQ2JrQyxrQkFBa0IvQjtvQ0FDbEJuRSxXQUFXQTs7Ozs7O2dDQUlaMkMsbUJBQW1CUSxNQUFNLEtBQUssbUJBQzdCLDhEQUFDbUI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFBZ0I7Ozs7OztzREFDL0IsOERBQUM0Qjs0Q0FBRzVCLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDTTs0Q0FBRU4sV0FBVTtzREFBcUI7Ozs7OztzREFHbEMsOERBQUNpQjs0Q0FDQ0MsU0FBUztnREFDUGhHLGNBQWM7Z0RBQ2RFLFdBQVcsQ0FBQzs0Q0FDZDs0Q0FDQTRFLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0FBRUEsaUVBQWVoRixjQUFjQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxpc2hvcC1wb3J0Zm9saW8vLi9zcmMvYXBwL3Nob3Avd2FsbHBhcGVycy9wYWdlLnRzeD9mYjVkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQge1xuICBNYWduaWZ5aW5nR2xhc3NJY29uLFxuICBBZGp1c3RtZW50c0hvcml6b250YWxJY29uLFxuICBTcXVhcmVzMlgySWNvbixcbiAgTGlzdEJ1bGxldEljb24sXG4gIFhNYXJrSWNvbixcbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCBXYWxscGFwZXJHcmlkLCB7IFdhbGxwYXBlckZpbHRlcnMsIFdhbGxwYXBlckZpbHRlcnNQYW5lbCB9IGZyb20gJ0AvY29tcG9uZW50cy93YWxscGFwZXIvV2FsbHBhcGVyR3JpZCc7XG5pbXBvcnQgeyBQcm9kdWN0LCBQcm9kdWN0Q2F0ZWdvcnksIFByb2R1Y3RUeXBlLCBXYWxscGFwZXJTdHlsZSwgRGV2aWNlVHlwZSB9IGZyb20gJ0AvdHlwZXMvcHJvZHVjdCc7XG5cbmNvbnN0IFdhbGxwYXBlcnNQYWdlOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbZmlsdGVycywgc2V0RmlsdGVyc10gPSB1c2VTdGF0ZTxXYWxscGFwZXJGaWx0ZXJzPih7fSk7XG4gIGNvbnN0IFtzaG93RmlsdGVycywgc2V0U2hvd0ZpbHRlcnNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdmlld01vZGUsIHNldFZpZXdNb2RlXSA9IHVzZVN0YXRlPCdncmlkJyB8ICdsaXN0Jz4oJ2dyaWQnKTtcbiAgY29uc3QgW2Zhdm9yaXRlcywgc2V0RmF2b3JpdGVzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XG5cbiAgLy8gTW9jayB3YWxscGFwZXIgZGF0YSAtIGluIHJlYWwgYXBwLCB0aGlzIHdvdWxkIGNvbWUgZnJvbSB5b3VyIGRhdGFiYXNlXG4gIGNvbnN0IG1vY2tXYWxscGFwZXJzOiBQcm9kdWN0W10gPSBbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIHRpdGxlOiAnQWJzdHJhY3QgTmVvbiBXYXZlcycsXG4gICAgICBkZXNjcmlwdGlvbjogJ1ZpYnJhbnQgbmVvbiB3YXZlcyBmbG93aW5nIGFjcm9zcyBhIGRhcmsgYmFja2dyb3VuZCwgcGVyZmVjdCBmb3IgbW9kZXJuIHNldHVwcycsXG4gICAgICBwcmljZTogNC45OSxcbiAgICAgIG9yaWdpbmFsUHJpY2U6IDcuOTksXG4gICAgICBjYXRlZ29yeTogUHJvZHVjdENhdGVnb3J5LldBTExQQVBFUlMsXG4gICAgICB0eXBlOiBQcm9kdWN0VHlwZS5ESUdJVEFMLFxuICAgICAgaW1hZ2VzOiBbJy9hcGkvcGxhY2Vob2xkZXIvNDAwLzYwMCddLFxuICAgICAgdGFnczogWydhYnN0cmFjdCcsICduZW9uJywgJ3dhdmVzJywgJ21vZGVybiddLFxuICAgICAgaW5TdG9jazogdHJ1ZSxcbiAgICAgIGZlYXR1cmVkOiB0cnVlLFxuICAgICAgaXNEaWdpdGFsOiB0cnVlLFxuICAgICAgZmlsZUZvcm1hdDogWydKUEcnLCAnUE5HJ10sXG4gICAgICBmaWxlU2l6ZTogJzE1LTI1IE1CJyxcbiAgICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgIHdhbGxwYXBlckRhdGE6IHtcbiAgICAgICAgcmVzb2x1dGlvbnM6IFtcbiAgICAgICAgICB7IHdpZHRoOiAzODQwLCBoZWlnaHQ6IDIxNjAsIGxhYmVsOiAnNEsnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvNGsnLCBmaWxlU2l6ZTogJzI1IE1CJyB9LFxuICAgICAgICAgIHsgd2lkdGg6IDI1NjAsIGhlaWdodDogMTQ0MCwgbGFiZWw6ICcxNDQwcCcsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy8xNDQwcCcsIGZpbGVTaXplOiAnMTggTUInIH0sXG4gICAgICAgICAgeyB3aWR0aDogMTkyMCwgaGVpZ2h0OiAxMDgwLCBsYWJlbDogJzEwODBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzEwODBwJywgZmlsZVNpemU6ICcxMiBNQicgfSxcbiAgICAgICAgICB7IHdpZHRoOiAxMDgwLCBoZWlnaHQ6IDIzNDAsIGxhYmVsOiAnTW9iaWxlJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzL21vYmlsZScsIGZpbGVTaXplOiAnOCBNQicgfSxcbiAgICAgICAgXSxcbiAgICAgICAgYXNwZWN0UmF0aW86ICcxNjo5JyxcbiAgICAgICAgY29sb3JQYWxldHRlOiBbJyNGRjAwRkYnLCAnIzAwRkZGRicsICcjRkY2QjZCJywgJyM0RUNEQzQnLCAnIzFBMUExQSddLFxuICAgICAgICBzdHlsZTogV2FsbHBhcGVyU3R5bGUuQUJTVFJBQ1QsXG4gICAgICAgIGRldmljZUNvbXBhdGliaWxpdHk6IFtEZXZpY2VUeXBlLkRFU0tUT1AsIERldmljZVR5cGUuTEFQVE9QLCBEZXZpY2VUeXBlLk1PQklMRV0sXG4gICAgICAgIHByZXZpZXdVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzgwMC82MDAnLFxuICAgICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgICBkb3dubG9hZENvdW50OiAxMjUwLFxuICAgICAgfSxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMicsXG4gICAgICB0aXRsZTogJ01pbmltYWxpc3QgTW91bnRhaW4gUmFuZ2UnLFxuICAgICAgZGVzY3JpcHRpb246ICdDbGVhbiwgbWluaW1hbGlzdCBtb3VudGFpbiBzaWxob3VldHRlcyB3aXRoIGdyYWRpZW50IHNreScsXG4gICAgICBwcmljZTogMy45OSxcbiAgICAgIGNhdGVnb3J5OiBQcm9kdWN0Q2F0ZWdvcnkuV0FMTFBBUEVSUyxcbiAgICAgIHR5cGU6IFByb2R1Y3RUeXBlLkRJR0lUQUwsXG4gICAgICBpbWFnZXM6IFsnL2FwaS9wbGFjZWhvbGRlci80MDAvNjAwJ10sXG4gICAgICB0YWdzOiBbJ21pbmltYWxpc3QnLCAnbW91bnRhaW5zJywgJ25hdHVyZScsICdncmFkaWVudCddLFxuICAgICAgaW5TdG9jazogdHJ1ZSxcbiAgICAgIGZlYXR1cmVkOiBmYWxzZSxcbiAgICAgIGlzRGlnaXRhbDogdHJ1ZSxcbiAgICAgIGZpbGVGb3JtYXQ6IFsnSlBHJywgJ1BORyddLFxuICAgICAgZmlsZVNpemU6ICcxMC0yMCBNQicsXG4gICAgICBjcmVhdGVkQXQ6ICcyMDI0LTAxLTAyVDAwOjAwOjAwWicsXG4gICAgICB1cGRhdGVkQXQ6ICcyMDI0LTAxLTAyVDAwOjAwOjAwWicsXG4gICAgICB3YWxscGFwZXJEYXRhOiB7XG4gICAgICAgIHJlc29sdXRpb25zOiBbXG4gICAgICAgICAgeyB3aWR0aDogMzg0MCwgaGVpZ2h0OiAyMTYwLCBsYWJlbDogJzRLJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzRrJywgZmlsZVNpemU6ICcyMCBNQicgfSxcbiAgICAgICAgICB7IHdpZHRoOiAyNTYwLCBoZWlnaHQ6IDE0NDAsIGxhYmVsOiAnMTQ0MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTQ0MHAnLCBmaWxlU2l6ZTogJzE1IE1CJyB9LFxuICAgICAgICAgIHsgd2lkdGg6IDE5MjAsIGhlaWdodDogMTA4MCwgbGFiZWw6ICcxMDgwcCcsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy8xMDgwcCcsIGZpbGVTaXplOiAnMTAgTUInIH0sXG4gICAgICAgIF0sXG4gICAgICAgIGFzcGVjdFJhdGlvOiAnMTY6OScsXG4gICAgICAgIGNvbG9yUGFsZXR0ZTogWycjODdDRUVCJywgJyNGRkI2QzEnLCAnI0REQTBERCcsICcjRjBGOEZGJywgJyMyRjRGNEYnXSxcbiAgICAgICAgc3R5bGU6IFdhbGxwYXBlclN0eWxlLk1JTklNQUxJU1QsXG4gICAgICAgIGRldmljZUNvbXBhdGliaWxpdHk6IFtEZXZpY2VUeXBlLkRFU0tUT1AsIERldmljZVR5cGUuTEFQVE9QLCBEZXZpY2VUeXBlLlVMVFJBV0lERV0sXG4gICAgICAgIHByZXZpZXdVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzgwMC82MDAnLFxuICAgICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgICBkb3dubG9hZENvdW50OiA4OTAsXG4gICAgICB9LFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICczJyxcbiAgICAgIHRpdGxlOiAnR2FtaW5nIFJHQiBTZXR1cCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ0R5bmFtaWMgUkdCIGxpZ2h0aW5nIGVmZmVjdHMgcGVyZmVjdCBmb3IgZ2FtaW5nIHNldHVwcycsXG4gICAgICBwcmljZTogNS45OSxcbiAgICAgIGNhdGVnb3J5OiBQcm9kdWN0Q2F0ZWdvcnkuV0FMTFBBUEVSUyxcbiAgICAgIHR5cGU6IFByb2R1Y3RUeXBlLkRJR0lUQUwsXG4gICAgICBpbWFnZXM6IFsnL2FwaS9wbGFjZWhvbGRlci80MDAvNjAwJ10sXG4gICAgICB0YWdzOiBbJ2dhbWluZycsICdyZ2InLCAnbmVvbicsICd0ZWNoJ10sXG4gICAgICBpblN0b2NrOiB0cnVlLFxuICAgICAgZmVhdHVyZWQ6IHRydWUsXG4gICAgICBpc0RpZ2l0YWw6IHRydWUsXG4gICAgICBmaWxlRm9ybWF0OiBbJ0pQRycsICdQTkcnXSxcbiAgICAgIGZpbGVTaXplOiAnMjAtMzAgTUInLFxuICAgICAgY3JlYXRlZEF0OiAnMjAyNC0wMS0wM1QwMDowMDowMFonLFxuICAgICAgdXBkYXRlZEF0OiAnMjAyNC0wMS0wM1QwMDowMDowMFonLFxuICAgICAgd2FsbHBhcGVyRGF0YToge1xuICAgICAgICByZXNvbHV0aW9uczogW1xuICAgICAgICAgIHsgd2lkdGg6IDM4NDAsIGhlaWdodDogMjE2MCwgbGFiZWw6ICc0SycsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy80aycsIGZpbGVTaXplOiAnMzAgTUInIH0sXG4gICAgICAgICAgeyB3aWR0aDogMjU2MCwgaGVpZ2h0OiAxNDQwLCBsYWJlbDogJzE0NDBwJywgZG93bmxvYWRVcmw6ICcvZG93bmxvYWRzLzE0NDBwJywgZmlsZVNpemU6ICcyMiBNQicgfSxcbiAgICAgICAgICB7IHdpZHRoOiAxOTIwLCBoZWlnaHQ6IDEwODAsIGxhYmVsOiAnMTA4MHAnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvMTA4MHAnLCBmaWxlU2l6ZTogJzE1IE1CJyB9LFxuICAgICAgICAgIHsgd2lkdGg6IDM0NDAsIGhlaWdodDogMTQ0MCwgbGFiZWw6ICdVbHRyYXdpZGUnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvdWx0cmF3aWRlJywgZmlsZVNpemU6ICcyNSBNQicgfSxcbiAgICAgICAgXSxcbiAgICAgICAgYXNwZWN0UmF0aW86ICcxNjo5JyxcbiAgICAgICAgY29sb3JQYWxldHRlOiBbJyNGRjAwODAnLCAnIzAwRkY4MCcsICcjODAwMEZGJywgJyNGRjgwMDAnLCAnIzAwMDAwMCddLFxuICAgICAgICBzdHlsZTogV2FsbHBhcGVyU3R5bGUuR0FNSU5HLFxuICAgICAgICBkZXZpY2VDb21wYXRpYmlsaXR5OiBbRGV2aWNlVHlwZS5ERVNLVE9QLCBEZXZpY2VUeXBlLkxBUFRPUCwgRGV2aWNlVHlwZS5VTFRSQVdJREVdLFxuICAgICAgICBwcmV2aWV3VXJsOiAnL2FwaS9wbGFjZWhvbGRlci84MDAvNjAwJyxcbiAgICAgICAgd2F0ZXJtYXJrZWRQcmV2aWV3OiAnL2FwaS9wbGFjZWhvbGRlci80MDAvNjAwJyxcbiAgICAgICAgZG93bmxvYWRDb3VudDogMjEwMCxcbiAgICAgIH0sXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzQnLFxuICAgICAgdGl0bGU6ICdOYXR1cmUgRm9yZXN0IFBhdGgnLFxuICAgICAgZGVzY3JpcHRpb246ICdTZXJlbmUgZm9yZXN0IHBhdGggd2l0aCBtb3JuaW5nIHN1bmxpZ2h0IGZpbHRlcmluZyB0aHJvdWdoIHRyZWVzJyxcbiAgICAgIHByaWNlOiAyLjk5LFxuICAgICAgY2F0ZWdvcnk6IFByb2R1Y3RDYXRlZ29yeS5XQUxMUEFQRVJTLFxuICAgICAgdHlwZTogUHJvZHVjdFR5cGUuRElHSVRBTCxcbiAgICAgIGltYWdlczogWycvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnXSxcbiAgICAgIHRhZ3M6IFsnbmF0dXJlJywgJ2ZvcmVzdCcsICdwZWFjZWZ1bCcsICdwaG90b2dyYXBoeSddLFxuICAgICAgaW5TdG9jazogdHJ1ZSxcbiAgICAgIGZlYXR1cmVkOiBmYWxzZSxcbiAgICAgIGlzRGlnaXRhbDogdHJ1ZSxcbiAgICAgIGZpbGVGb3JtYXQ6IFsnSlBHJ10sXG4gICAgICBmaWxlU2l6ZTogJzEyLTE4IE1CJyxcbiAgICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDRUMDA6MDA6MDBaJyxcbiAgICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDRUMDA6MDA6MDBaJyxcbiAgICAgIHdhbGxwYXBlckRhdGE6IHtcbiAgICAgICAgcmVzb2x1dGlvbnM6IFtcbiAgICAgICAgICB7IHdpZHRoOiAzODQwLCBoZWlnaHQ6IDIxNjAsIGxhYmVsOiAnNEsnLCBkb3dubG9hZFVybDogJy9kb3dubG9hZHMvNGsnLCBmaWxlU2l6ZTogJzE4IE1CJyB9LFxuICAgICAgICAgIHsgd2lkdGg6IDE5MjAsIGhlaWdodDogMTA4MCwgbGFiZWw6ICcxMDgwcCcsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy8xMDgwcCcsIGZpbGVTaXplOiAnMTIgTUInIH0sXG4gICAgICAgICAgeyB3aWR0aDogMTA4MCwgaGVpZ2h0OiAyMzQwLCBsYWJlbDogJ01vYmlsZScsIGRvd25sb2FkVXJsOiAnL2Rvd25sb2Fkcy9tb2JpbGUnLCBmaWxlU2l6ZTogJzYgTUInIH0sXG4gICAgICAgIF0sXG4gICAgICAgIGFzcGVjdFJhdGlvOiAnMTY6OScsXG4gICAgICAgIGNvbG9yUGFsZXR0ZTogWycjMjI4QjIyJywgJyM4RkJDOEYnLCAnI0Y1REVCMycsICcjREVCODg3JywgJyM2NTQzMjEnXSxcbiAgICAgICAgc3R5bGU6IFdhbGxwYXBlclN0eWxlLk5BVFVSRSxcbiAgICAgICAgZGV2aWNlQ29tcGF0aWJpbGl0eTogW0RldmljZVR5cGUuREVTS1RPUCwgRGV2aWNlVHlwZS5MQVBUT1AsIERldmljZVR5cGUuTU9CSUxFLCBEZXZpY2VUeXBlLlRBQkxFVF0sXG4gICAgICAgIHByZXZpZXdVcmw6ICcvYXBpL3BsYWNlaG9sZGVyLzgwMC82MDAnLFxuICAgICAgICB3YXRlcm1hcmtlZFByZXZpZXc6ICcvYXBpL3BsYWNlaG9sZGVyLzQwMC82MDAnLFxuICAgICAgICBkb3dubG9hZENvdW50OiA3NTYsXG4gICAgICB9LFxuICAgIH0sXG4gIF07XG5cbiAgLy8gRmlsdGVyIGFuZCBzZWFyY2ggbG9naWNcbiAgY29uc3QgZmlsdGVyZWRXYWxscGFwZXJzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkID0gbW9ja1dhbGxwYXBlcnM7XG5cbiAgICAvLyBTZWFyY2ggZmlsdGVyXG4gICAgaWYgKHNlYXJjaFRlcm0pIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHByb2R1Y3QgPT5cbiAgICAgICAgcHJvZHVjdC50aXRsZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgcHJvZHVjdC5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcbiAgICAgICAgcHJvZHVjdC50YWdzLnNvbWUodGFnID0+IHRhZy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkpXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFN0eWxlIGZpbHRlclxuICAgIGlmIChmaWx0ZXJzLnN0eWxlICYmIGZpbHRlcnMuc3R5bGUubGVuZ3RoID4gMCkge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIocHJvZHVjdCA9PlxuICAgICAgICBwcm9kdWN0LndhbGxwYXBlckRhdGE/LnN0eWxlICYmIGZpbHRlcnMuc3R5bGUhLmluY2x1ZGVzKHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5zdHlsZSlcbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gRGV2aWNlIGZpbHRlclxuICAgIGlmIChmaWx0ZXJzLmRldmljZXMgJiYgZmlsdGVycy5kZXZpY2VzLmxlbmd0aCA+IDApIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHByb2R1Y3QgPT5cbiAgICAgICAgcHJvZHVjdC53YWxscGFwZXJEYXRhPy5kZXZpY2VDb21wYXRpYmlsaXR5LnNvbWUoZGV2aWNlID0+XG4gICAgICAgICAgZmlsdGVycy5kZXZpY2VzIS5pbmNsdWRlcyhkZXZpY2UpXG4gICAgICAgIClcbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQXNwZWN0IHJhdGlvIGZpbHRlclxuICAgIGlmIChmaWx0ZXJzLmFzcGVjdFJhdGlvICYmIGZpbHRlcnMuYXNwZWN0UmF0aW8ubGVuZ3RoID4gMCkge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIocHJvZHVjdCA9PlxuICAgICAgICBwcm9kdWN0LndhbGxwYXBlckRhdGE/LmFzcGVjdFJhdGlvICYmIGZpbHRlcnMuYXNwZWN0UmF0aW8hLmluY2x1ZGVzKHByb2R1Y3Qud2FsbHBhcGVyRGF0YS5hc3BlY3RSYXRpbylcbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gQ29sb3IgZmlsdGVyXG4gICAgaWYgKGZpbHRlcnMuY29sb3JzICYmIGZpbHRlcnMuY29sb3JzLmxlbmd0aCA+IDApIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHByb2R1Y3QgPT5cbiAgICAgICAgcHJvZHVjdC53YWxscGFwZXJEYXRhPy5jb2xvclBhbGV0dGUuc29tZShjb2xvciA9PlxuICAgICAgICAgIGZpbHRlcnMuY29sb3JzIS5zb21lKGZpbHRlckNvbG9yID0+XG4gICAgICAgICAgICBjb2xvci50b0xvd2VyQ2FzZSgpID09PSBmaWx0ZXJDb2xvci50b0xvd2VyQ2FzZSgpXG4gICAgICAgICAgKVxuICAgICAgICApXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFByaWNlIHJhbmdlIGZpbHRlclxuICAgIGlmIChmaWx0ZXJzLnByaWNlUmFuZ2UpIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKHByb2R1Y3QgPT5cbiAgICAgICAgcHJvZHVjdC5wcmljZSA+PSBmaWx0ZXJzLnByaWNlUmFuZ2UhWzBdICYmIHByb2R1Y3QucHJpY2UgPD0gZmlsdGVycy5wcmljZVJhbmdlIVsxXVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBTb3J0XG4gICAgaWYgKGZpbHRlcnMuc29ydEJ5KSB7XG4gICAgICBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgIHN3aXRjaCAoZmlsdGVycy5zb3J0QnkpIHtcbiAgICAgICAgICBjYXNlICdwcmljZS1sb3cnOlxuICAgICAgICAgICAgcmV0dXJuIGEucHJpY2UgLSBiLnByaWNlO1xuICAgICAgICAgIGNhc2UgJ3ByaWNlLWhpZ2gnOlxuICAgICAgICAgICAgcmV0dXJuIGIucHJpY2UgLSBhLnByaWNlO1xuICAgICAgICAgIGNhc2UgJ2Rvd25sb2Fkcyc6XG4gICAgICAgICAgICByZXR1cm4gKGIud2FsbHBhcGVyRGF0YT8uZG93bmxvYWRDb3VudCB8fCAwKSAtIChhLndhbGxwYXBlckRhdGE/LmRvd25sb2FkQ291bnQgfHwgMCk7XG4gICAgICAgICAgY2FzZSAncG9wdWxhcic6XG4gICAgICAgICAgICByZXR1cm4gKGIud2FsbHBhcGVyRGF0YT8uZG93bmxvYWRDb3VudCB8fCAwKSAtIChhLndhbGxwYXBlckRhdGE/LmRvd25sb2FkQ291bnQgfHwgMCk7XG4gICAgICAgICAgY2FzZSAnbmV3ZXN0JzpcbiAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShhLmNyZWF0ZWRBdCkuZ2V0VGltZSgpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gZmlsdGVyZWQ7XG4gIH0sIFttb2NrV2FsbHBhcGVycywgc2VhcmNoVGVybSwgZmlsdGVyc10pO1xuXG4gIGNvbnN0IGhhbmRsZUFkZFRvQ2FydCA9IChwcm9kdWN0OiBQcm9kdWN0KSA9PiB7XG4gICAgY29uc29sZS5sb2coJ0FkZGVkIHRvIGNhcnQ6JywgcHJvZHVjdC50aXRsZSk7XG4gICAgLy8gSW4gcmVhbCBhcHAsIHRoaXMgd291bGQgYWRkIHRvIGNhcnQgY29udGV4dFxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVRvZ2dsZUZhdm9yaXRlID0gKHByb2R1Y3RJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0RmF2b3JpdGVzKHByZXYgPT5cbiAgICAgIHByZXYuaW5jbHVkZXMocHJvZHVjdElkKVxuICAgICAgICA/IHByZXYuZmlsdGVyKGlkID0+IGlkICE9PSBwcm9kdWN0SWQpXG4gICAgICAgIDogWy4uLnByZXYsIHByb2R1Y3RJZF1cbiAgICApO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LTUwIHZpYS13aGl0ZSB0by1zZWNvbmRhcnktNTAgcHQtMjBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgPG1vdGlvbi5oMVxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBQcmVtaXVtIFdhbGxwYXBlcnNcbiAgICAgICAgICA8L21vdGlvbi5oMT5cbiAgICAgICAgICA8bW90aW9uLnBcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogMC4xIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWF4LXctMnhsIG14LWF1dG9cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIFRyYW5zZm9ybSB5b3VyIGRldmljZXMgd2l0aCBvdXIgY29sbGVjdGlvbiBvZiBoaWdoLXF1YWxpdHkgd2FsbHBhcGVyc1xuICAgICAgICAgIDwvbW90aW9uLnA+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTZWFyY2ggYW5kIENvbnRyb2xzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbGc6ZmxleC1yb3cgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgIHsvKiBTZWFyY2ggQmFyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgIDxNYWduaWZ5aW5nR2xhc3NJY29uIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHctNSBoLTUgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCB3YWxscGFwZXJzLi4uXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwbC0xMCBwci00IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDb250cm9scyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgey8qIFNvcnQgKi99XG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLnNvcnRCeSB8fCAnbmV3ZXN0J31cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGaWx0ZXJzKHsgLi4uZmlsdGVycywgc29ydEJ5OiBlLnRhcmdldC52YWx1ZSBhcyBhbnkgfSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMyBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQteGwgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5ld2VzdFwiPk5ld2VzdDwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicG9wdWxhclwiPk1vc3QgUG9wdWxhcjwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZG93bmxvYWRzXCI+TW9zdCBEb3dubG9hZGVkPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJwcmljZS1sb3dcIj5QcmljZTogTG93IHRvIEhpZ2g8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInByaWNlLWhpZ2hcIj5QcmljZTogSGlnaCB0byBMb3c8L29wdGlvbj5cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuXG4gICAgICAgICAgICB7LyogVmlldyBNb2RlICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC14bCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFZpZXdNb2RlKCdncmlkJyl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zICR7dmlld01vZGUgPT09ICdncmlkJyA/ICdiZy1wcmltYXJ5LTYwMCB0ZXh0LXdoaXRlJyA6ICdiZy13aGl0ZSB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAnfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U3F1YXJlczJYMkljb24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoJ2xpc3QnKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgJHt2aWV3TW9kZSA9PT0gJ2xpc3QnID8gJ2JnLXByaW1hcnktNjAwIHRleHQtd2hpdGUnIDogJ2JnLXdoaXRlIHRleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS01MCd9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxMaXN0QnVsbGV0SWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZpbHRlcnMgVG9nZ2xlICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RmlsdGVycyghc2hvd0ZpbHRlcnMpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0zIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC14bCBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxBZGp1c3RtZW50c0hvcml6b250YWxJY29uIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj5GaWx0ZXJzPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtOFwiPlxuICAgICAgICAgIHsvKiBGaWx0ZXJzIFNpZGViYXIgKi99XG4gICAgICAgICAge3Nob3dGaWx0ZXJzICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTgwIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8V2FsbHBhcGVyRmlsdGVyc1BhbmVsXG4gICAgICAgICAgICAgICAgZmlsdGVycz17ZmlsdGVyc31cbiAgICAgICAgICAgICAgICBvbkZpbHRlcnNDaGFuZ2U9e3NldEZpbHRlcnN9XG4gICAgICAgICAgICAgICAgb25DbGVhckZpbHRlcnM9eygpID0+IHNldEZpbHRlcnMoe30pfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICB7LyogUmVzdWx0cyBDb3VudCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIHtmaWx0ZXJlZFdhbGxwYXBlcnMubGVuZ3RofSB3YWxscGFwZXJ7ZmlsdGVyZWRXYWxscGFwZXJzLmxlbmd0aCAhPT0gMSA/ICdzJyA6ICcnfSBmb3VuZFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIHtPYmplY3Qua2V5cyhmaWx0ZXJzKS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRGaWx0ZXJzKHt9KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMCBob3Zlcjp0ZXh0LXByaW1hcnktNzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDbGVhciBhbGwgZmlsdGVyc1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBXYWxscGFwZXJzIEdyaWQgKi99XG4gICAgICAgICAgICA8V2FsbHBhcGVyR3JpZFxuICAgICAgICAgICAgICBwcm9kdWN0cz17ZmlsdGVyZWRXYWxscGFwZXJzfVxuICAgICAgICAgICAgICBvbkFkZFRvQ2FydD17aGFuZGxlQWRkVG9DYXJ0fVxuICAgICAgICAgICAgICBvblRvZ2dsZUZhdm9yaXRlPXtoYW5kbGVUb2dnbGVGYXZvcml0ZX1cbiAgICAgICAgICAgICAgZmF2b3JpdGVzPXtmYXZvcml0ZXN9XG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICB7LyogTm8gUmVzdWx0cyAqL31cbiAgICAgICAgICAgIHtmaWx0ZXJlZFdhbGxwYXBlcnMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNFwiPvCflrzvuI88L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyB3YWxscGFwZXJzIGZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIFRyeSBhZGp1c3RpbmcgeW91ciBzZWFyY2ggb3IgZmlsdGVyIGNyaXRlcmlhXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0U2VhcmNoVGVybSgnJyk7XG4gICAgICAgICAgICAgICAgICAgIHNldEZpbHRlcnMoe30pO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1wdXJwbGUteWVsbG93LWdyYWRpZW50IHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDbGVhciBTZWFyY2ggJiBGaWx0ZXJzXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFdhbGxwYXBlcnNQYWdlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VNZW1vIiwibW90aW9uIiwiTWFnbmlmeWluZ0dsYXNzSWNvbiIsIkFkanVzdG1lbnRzSG9yaXpvbnRhbEljb24iLCJTcXVhcmVzMlgySWNvbiIsIkxpc3RCdWxsZXRJY29uIiwiV2FsbHBhcGVyR3JpZCIsIldhbGxwYXBlckZpbHRlcnNQYW5lbCIsIlByb2R1Y3RDYXRlZ29yeSIsIlByb2R1Y3RUeXBlIiwiV2FsbHBhcGVyU3R5bGUiLCJEZXZpY2VUeXBlIiwiV2FsbHBhcGVyc1BhZ2UiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwic2hvd0ZpbHRlcnMiLCJzZXRTaG93RmlsdGVycyIsInZpZXdNb2RlIiwic2V0Vmlld01vZGUiLCJmYXZvcml0ZXMiLCJzZXRGYXZvcml0ZXMiLCJtb2NrV2FsbHBhcGVycyIsImlkIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInByaWNlIiwib3JpZ2luYWxQcmljZSIsImNhdGVnb3J5IiwiV0FMTFBBUEVSUyIsInR5cGUiLCJESUdJVEFMIiwiaW1hZ2VzIiwidGFncyIsImluU3RvY2siLCJmZWF0dXJlZCIsImlzRGlnaXRhbCIsImZpbGVGb3JtYXQiLCJmaWxlU2l6ZSIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsIndhbGxwYXBlckRhdGEiLCJyZXNvbHV0aW9ucyIsIndpZHRoIiwiaGVpZ2h0IiwibGFiZWwiLCJkb3dubG9hZFVybCIsImFzcGVjdFJhdGlvIiwiY29sb3JQYWxldHRlIiwic3R5bGUiLCJBQlNUUkFDVCIsImRldmljZUNvbXBhdGliaWxpdHkiLCJERVNLVE9QIiwiTEFQVE9QIiwiTU9CSUxFIiwicHJldmlld1VybCIsIndhdGVybWFya2VkUHJldmlldyIsImRvd25sb2FkQ291bnQiLCJNSU5JTUFMSVNUIiwiVUxUUkFXSURFIiwiR0FNSU5HIiwiTkFUVVJFIiwiVEFCTEVUIiwiZmlsdGVyZWRXYWxscGFwZXJzIiwiZmlsdGVyZWQiLCJmaWx0ZXIiLCJwcm9kdWN0IiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNvbWUiLCJ0YWciLCJsZW5ndGgiLCJkZXZpY2VzIiwiZGV2aWNlIiwiY29sb3JzIiwiY29sb3IiLCJmaWx0ZXJDb2xvciIsInByaWNlUmFuZ2UiLCJzb3J0QnkiLCJzb3J0IiwiYSIsImIiLCJEYXRlIiwiZ2V0VGltZSIsImhhbmRsZUFkZFRvQ2FydCIsImNvbnNvbGUiLCJsb2ciLCJoYW5kbGVUb2dnbGVGYXZvcml0ZSIsInByb2R1Y3RJZCIsInByZXYiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJwIiwidHJhbnNpdGlvbiIsImRlbGF5IiwiaW5wdXQiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwic2VsZWN0Iiwib3B0aW9uIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iLCJ4Iiwib25GaWx0ZXJzQ2hhbmdlIiwib25DbGVhckZpbHRlcnMiLCJPYmplY3QiLCJrZXlzIiwicHJvZHVjdHMiLCJvbkFkZFRvQ2FydCIsIm9uVG9nZ2xlRmF2b3JpdGUiLCJoMyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/shop/wallpapers/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaDribbble,FaGithub,FaInstagram,FaLinkedin,FaTwitter!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        portfolio: [\n            {\n                name: \"Projects\",\n                href: \"/portfolio\"\n            },\n            {\n                name: \"Skills\",\n                href: \"/portfolio#skills\"\n            },\n            {\n                name: \"Experience\",\n                href: \"/portfolio#experience\"\n            },\n            {\n                name: \"Testimonials\",\n                href: \"/portfolio#testimonials\"\n            }\n        ],\n        shop: [\n            {\n                name: \"All Products\",\n                href: \"/shop\"\n            },\n            {\n                name: \"Featured\",\n                href: \"/shop/featured\"\n            },\n            {\n                name: \"Categories\",\n                href: \"/shop/categories\"\n            },\n            {\n                name: \"Sale\",\n                href: \"/shop/sale\"\n            }\n        ],\n        support: [\n            {\n                name: \"Contact\",\n                href: \"/contact\"\n            },\n            {\n                name: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                name: \"Shipping\",\n                href: \"/shipping\"\n            },\n            {\n                name: \"Returns\",\n                href: \"/returns\"\n            }\n        ],\n        legal: [\n            {\n                name: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                name: \"Cookie Policy\",\n                href: \"/cookies\"\n            },\n            {\n                name: \"Refund Policy\",\n                href: \"/refunds\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"GitHub\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGithub,\n            href: \"https://github.com\",\n            color: \"hover:text-gray-900\"\n        },\n        {\n            name: \"LinkedIn\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaLinkedin,\n            href: \"https://linkedin.com\",\n            color: \"hover:text-blue-600\"\n        },\n        {\n            name: \"Twitter\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTwitter,\n            href: \"https://twitter.com\",\n            color: \"hover:text-blue-400\"\n        },\n        {\n            name: \"Instagram\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaInstagram,\n            href: \"https://instagram.com\",\n            color: \"hover:text-pink-600\"\n        },\n        {\n            name: \"Dribbble\",\n            icon: _barrel_optimize_names_FaDribbble_FaGithub_FaInstagram_FaLinkedin_FaTwitter_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaDribbble,\n            href: \"https://dribbble.com\",\n            color: \"hover:text-pink-500\"\n        }\n    ];\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            text: \"<EMAIL>\",\n            href: \"mailto:<EMAIL>\"\n        },\n        {\n            icon: _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            text: \"+****************\",\n            href: \"tel:+15551234567\"\n        },\n        {\n            icon: _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            text: \"New York, NY\",\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gradient-to-br from-gray-900 via-primary-900 to-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-purple-yellow-gradient rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-2xl\",\n                                                        children: \"E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                                    children: \"EliShop\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.1\n                                        },\n                                        className: \"text-gray-300 text-lg leading-relaxed max-w-md\",\n                                        children: \"A modern portfolio and shop experience featuring stunning animations, premium products, and exceptional user experience.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.2\n                                        },\n                                        className: \"space-y-3\",\n                                        children: contactInfo.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                href: item.href,\n                                                whileHover: {\n                                                    x: 5\n                                                },\n                                                className: \"flex items-center space-x-3 text-gray-300 hover:text-secondary-400 transition-colors duration-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.text\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.a, {\n                                                href: social.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                whileHover: {\n                                                    scale: 1.2,\n                                                    y: -2\n                                                },\n                                                whileTap: {\n                                                    scale: 0.9\n                                                },\n                                                className: `p-3 bg-white/10 rounded-lg text-gray-300 ${social.color} transition-all duration-200 hover:bg-white/20`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, social.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, undefined),\n                            Object.entries(footerLinks).map(([category, links], categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1 * (categoryIndex + 1)\n                                    },\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white capitalize\",\n                                            children: category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: links.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: link.href,\n                                                        className: \"text-gray-300 hover:text-secondary-400 transition-colors duration-200 hover:translate-x-1 transform inline-block\",\n                                                        children: link.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, category, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.4\n                        },\n                        className: \"mt-16 pt-8 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-white mb-4\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6\",\n                                    children: \"Subscribe to get notified about new projects and products.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent transition-all duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            className: \"px-6 py-3 bg-purple-yellow-gradient text-white rounded-lg font-medium hover:shadow-lg transition-all duration-200\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.6,\n                    delay: 0.5\n                },\n                className: \"border-t border-gray-700 bg-gray-900/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" EliShop. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Made with ❤️ and lots of ☕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Powered by\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-secondary-400 font-medium\",\n                                                children: \"Next.js\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"&\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary-400 font-medium\",\n                                                children: \"Firebase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/CartContext */ \"(ssr)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ShoppingCartIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// import { useAuth } from '@/contexts/AuthContext';\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const { user, logout } = useAuth();\n    const user = null; // Temporary for testing\n    const { itemCount } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_3__.useCart)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Portfolio\",\n            href: \"/portfolio\"\n        },\n        {\n            name: \"Shop\",\n            href: \"/shop\",\n            submenu: [\n                {\n                    name: \"All Products\",\n                    href: \"/shop\"\n                },\n                {\n                    name: \"Wallpapers\",\n                    href: \"/shop/wallpapers\"\n                },\n                {\n                    name: \"Templates\",\n                    href: \"/shop/templates\"\n                },\n                {\n                    name: \"UI Kits\",\n                    href: \"/shop/ui-kits\"\n                }\n            ]\n        },\n        {\n            name: \"About\",\n            href: \"/about\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ];\n    const headerVariants = {\n        initial: {\n            y: -100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        exit: {\n            y: -100,\n            opacity: 0\n        }\n    };\n    const mobileMenuVariants = {\n        closed: {\n            opacity: 0,\n            x: \"100%\"\n        },\n        open: {\n            opacity: 1,\n            x: 0\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.header, {\n        variants: headerVariants,\n        initial: \"initial\",\n        animate: \"animate\",\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/90 backdrop-blur-md shadow-lg border-b border-primary-100\" : \"bg-transparent\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-purple-yellow-gradient rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: \"E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent\",\n                                        children: \"EliShop\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"relative text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium group\",\n                                        children: [\n                                            item.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 transition-all duration-300 group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    whileTap: {\n                                        scale: 0.9\n                                    },\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cart\",\n                                        className: \"p-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            itemCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"absolute -top-1 -right-1 bg-secondary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium\",\n                                                children: itemCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"flex items-center space-x-2 p-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-6 h-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: user.displayName || \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/profile\",\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Orders\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{},\n                                                        className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Sign Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signin\",\n                                            className: \"px-4 py-2 text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth/signup\",\n                                            className: \"px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            className: \"lg:hidden p-2 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    variants: mobileMenuVariants,\n                    initial: \"closed\",\n                    animate: \"open\",\n                    exit: \"closed\",\n                    transition: {\n                        type: \"tween\",\n                        duration: 0.3\n                    },\n                    className: \"lg:hidden fixed top-16 right-0 bottom-0 w-80 bg-white shadow-xl border-l border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-4\",\n                                children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            className: \"block text-lg font-medium text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-6 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/cart\",\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"flex items-center space-x-3 text-gray-700 hover:text-primary-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Cart (\",\n                                                    itemCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ShoppingCartIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: user.displayName || \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-9 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/profile\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        className: \"block text-gray-600 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Profile\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/orders\",\n                                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                                        className: \"block text-gray-600 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Orders\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            // logout();\n                                                            setIsMobileMenuOpen(false);\n                                                        },\n                                                        className: \"block text-gray-600 hover:text-primary-600 transition-colors duration-200\",\n                                                        children: \"Sign Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signin\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                className: \"block w-full text-center px-4 py-2 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors duration-200\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signup\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                className: \"block w-full text-center px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg hover:shadow-lg transition-all duration-200\",\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/wallpaper/WallpaperGrid.tsx":
/*!****************************************************!*\
  !*** ./src/components/wallpaper/WallpaperGrid.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WallpaperFiltersPanel: () => (/* binding */ WallpaperFiltersPanel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,HeartIcon,ShoppingCartIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/product */ \"(ssr)/./src/types/product.ts\");\n/* harmony import */ var _WallpaperPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./WallpaperPreview */ \"(ssr)/./src/components/wallpaper/WallpaperPreview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,WallpaperFiltersPanel auto */ \n\n\n\n\n\n\nconst WallpaperGrid = ({ products, onAddToCart, onToggleFavorite, favorites = [] })=>{\n    const [previewProduct, setPreviewProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredProduct, setHoveredProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handlePreview = (product)=>{\n        setPreviewProduct(product);\n    };\n    const handleDownload = (product)=>{\n        // In a real app, this would handle the purchase/download process\n        console.log(\"Download:\", product.title);\n        onAddToCart?.(product);\n    };\n    const getStyleColor = (style)=>{\n        const colors = {\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ABSTRACT]: \"bg-purple-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.NATURE]: \"bg-green-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.MINIMALIST]: \"bg-gray-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.GAMING]: \"bg-red-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ANIME]: \"bg-pink-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.PHOTOGRAPHY]: \"bg-blue-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.GRADIENT]: \"bg-gradient-to-r from-purple-500 to-pink-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.PATTERN]: \"bg-indigo-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.DARK]: \"bg-gray-900\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.LIGHT]: \"bg-gray-100\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.NEON]: \"bg-cyan-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.VINTAGE]: \"bg-amber-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.MODERN]: \"bg-slate-500\",\n            [_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle.ARTISTIC]: \"bg-orange-500\"\n        };\n        return colors[style] || \"bg-gray-500\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            layout: true,\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            className: \"group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300\",\n                            onMouseEnter: ()=>setHoveredProduct(product.id),\n                            onMouseLeave: ()=>setHoveredProduct(null),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative aspect-[3/4] overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: product.wallpaperData?.watermarkedPreview || product.images[0],\n                                            alt: product.title,\n                                            className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>handlePreview(product),\n                                                        className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200\",\n                                                        title: \"Preview\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>onToggleFavorite?.(product.id),\n                                                        className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors duration-200\",\n                                                        title: \"Add to Favorites\",\n                                                        children: favorites.includes(product.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        onClick: ()=>handleDownload(product),\n                                                        className: \"p-3 bg-purple-yellow-gradient rounded-full text-white hover:shadow-lg transition-all duration-200\",\n                                                        title: \"Add to Cart\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        product.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 left-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_HeartIcon_ShoppingCartIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Featured\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        product.wallpaperData?.style && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `px-2 py-1 rounded-full text-xs font-medium text-white ${getStyleColor(product.wallpaperData.style)}`,\n                                                children: product.wallpaperData.style.charAt(0).toUpperCase() + product.wallpaperData.style.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        product.wallpaperData?.resolutions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-3 left-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium\",\n                                                children: [\n                                                    product.wallpaperData.resolutions.length,\n                                                    \" resolutions\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-1 line-clamp-1\",\n                                            children: product.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                    children: [\n                                                        product.wallpaperData?.aspectRatio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.wallpaperData.aspectRatio\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        product.fileFormat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: product.fileFormat.join(\", \")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                                    lineNumber: 176,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                product.wallpaperData?.downloadCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 text-xs text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DownloadIcon, {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: product.wallpaperData.downloadCount.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        product.wallpaperData?.colorPalette && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1 mb-3\",\n                                            children: [\n                                                product.wallpaperData.colorPalette.slice(0, 5).map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full border border-gray-200\",\n                                                        style: {\n                                                            backgroundColor: color\n                                                        }\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                product.wallpaperData.colorPalette.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs text-gray-600\",\n                                                    children: [\n                                                        \"+\",\n                                                        product.wallpaperData.colorPalette.length - 5\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        product.wallpaperData?.deviceCompatibility && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: [\n                                                product.wallpaperData.deviceCompatibility.slice(0, 3).map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-md\",\n                                                        children: device\n                                                    }, device, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                product.wallpaperData.deviceCompatibility.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        product.wallpaperData.deviceCompatibility.length - 3,\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.price.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-500 line-through\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.originalPrice.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    onClick: ()=>handleDownload(product),\n                                                    className: \"px-4 py-2 bg-purple-yellow-gradient text-white rounded-lg text-sm font-medium hover:shadow-lg transition-all duration-200\",\n                                                    children: \"Add to Cart\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            previewProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WallpaperPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                product: previewProduct,\n                isOpen: !!previewProduct,\n                onClose: ()=>setPreviewProduct(null),\n                onDownload: (resolution)=>{\n                    console.log(\"Download resolution:\", resolution);\n                    handleDownload(previewProduct);\n                    setPreviewProduct(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WallpaperGrid);\nconst WallpaperFiltersPanel = ({ filters, onFiltersChange, onClearFilters })=>{\n    const styles = Object.values(_types_product__WEBPACK_IMPORTED_MODULE_2__.WallpaperStyle);\n    const devices = Object.values(_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType);\n    const aspectRatios = [\n        \"16:9\",\n        \"16:10\",\n        \"21:9\",\n        \"4:3\",\n        \"9:16\",\n        \"3:2\"\n    ];\n    const commonColors = [\n        \"#FF6B6B\",\n        \"#4ECDC4\",\n        \"#45B7D1\",\n        \"#96CEB4\",\n        \"#FFEAA7\",\n        \"#DDA0DD\",\n        \"#98D8C8\",\n        \"#F7DC6F\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClearFilters,\n                        className: \"text-sm text-primary-600 hover:text-primary-700 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Style\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: styles.map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filters.style?.includes(style) || false,\n                                        onChange: (e)=>{\n                                            const newStyles = e.target.checked ? [\n                                                ...filters.style || [],\n                                                style\n                                            ] : (filters.style || []).filter((s)=>s !== style);\n                                            onFiltersChange({\n                                                ...filters,\n                                                style: newStyles\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 capitalize\",\n                                        children: style\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, style, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Device\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filters.devices?.includes(device) || false,\n                                        onChange: (e)=>{\n                                            const newDevices = e.target.checked ? [\n                                                ...filters.devices || [],\n                                                device\n                                            ] : (filters.devices || []).filter((d)=>d !== device);\n                                            onFiltersChange({\n                                                ...filters,\n                                                devices: newDevices\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 capitalize\",\n                                        children: device.replace(\"_\", \" \")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, device, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Aspect Ratio\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: aspectRatios.map((ratio)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filters.aspectRatio?.includes(ratio) || false,\n                                        onChange: (e)=>{\n                                            const newRatios = e.target.checked ? [\n                                                ...filters.aspectRatio || [],\n                                                ratio\n                                            ] : (filters.aspectRatio || []).filter((r)=>r !== ratio);\n                                            onFiltersChange({\n                                                ...filters,\n                                                aspectRatio: newRatios\n                                            });\n                                        },\n                                        className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: ratio\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, ratio, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-3\",\n                        children: \"Colors\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-4 gap-2\",\n                        children: commonColors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const newColors = filters.colors?.includes(color) ? (filters.colors || []).filter((c)=>c !== color) : [\n                                        ...filters.colors || [],\n                                        color\n                                    ];\n                                    onFiltersChange({\n                                        ...filters,\n                                        colors: newColors\n                                    });\n                                },\n                                className: `w-8 h-8 rounded-full border-2 transition-all duration-200 ${filters.colors?.includes(color) ? \"border-gray-800 scale-110\" : \"border-gray-300 hover:border-gray-400\"}`,\n                                style: {\n                                    backgroundColor: color\n                                },\n                                title: color\n                            }, color, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperGrid.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/wallpaper/WallpaperGrid.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/wallpaper/WallpaperPreview.tsx":
/*!*******************************************************!*\
  !*** ./src/components/wallpaper/WallpaperPreview.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DeviceTabletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/__barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,DownloadIcon,EyeIcon,MagnifyingGlassIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _types_product__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/product */ \"(ssr)/./src/types/product.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst WallpaperPreview = ({ product, isOpen, onClose, onDownload })=>{\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType.DESKTOP);\n    const [isZoomed, setIsZoomed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedResolution, setSelectedResolution] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(product.wallpaperData?.resolutions[0] || null);\n    if (!product.wallpaperData) return null;\n    const { wallpaperData } = product;\n    const deviceMockups = {\n        [_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType.DESKTOP]: {\n            icon: _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            label: \"Desktop\",\n            aspectRatio: \"16/9\",\n            width: \"w-80\",\n            height: \"h-45\"\n        },\n        [_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType.LAPTOP]: {\n            icon: _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            label: \"Laptop\",\n            aspectRatio: \"16/10\",\n            width: \"w-72\",\n            height: \"h-45\"\n        },\n        [_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType.TABLET]: {\n            icon: _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Tablet\",\n            aspectRatio: \"4/3\",\n            width: \"w-60\",\n            height: \"h-45\"\n        },\n        [_types_product__WEBPACK_IMPORTED_MODULE_2__.DeviceType.MOBILE]: {\n            icon: _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Mobile\",\n            aspectRatio: \"9/19.5\",\n            width: \"w-32\",\n            height: \"h-64\"\n        }\n    };\n    const availableDevices = wallpaperData.deviceCompatibility.filter((device)=>device in deviceMockups);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\",\n            onClick: onClose,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        scale: 0.9,\n                        opacity: 0\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1\n                    },\n                    exit: {\n                        scale: 0.9,\n                        opacity: 0\n                    },\n                    className: \"bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: product.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: [\n                                                wallpaperData.aspectRatio,\n                                                \" • \",\n                                                wallpaperData.style\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-700\",\n                                                    children: \"Preview on:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: availableDevices.map((device)=>{\n                                                        const mockup = deviceMockups[device];\n                                                        const Icon = mockup.icon;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setSelectedDevice(device),\n                                                            className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${selectedDevice === device ? \"bg-purple-yellow-gradient text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: mockup.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, device, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 25\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center min-h-[400px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `relative ${deviceMockups[selectedDevice].width} ${deviceMockups[selectedDevice].height} bg-gray-800 rounded-lg p-2 shadow-2xl`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full rounded-md overflow-hidden cursor-pointer relative group\",\n                                                            onClick: ()=>setIsZoomed(true),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: wallpaperData.watermarkedPreview,\n                                                                    alt: product.title,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                        lineNumber: 154,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mt-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                deviceMockups[selectedDevice].label,\n                                                                \" Preview\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                    children: \"Color Palette\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2\",\n                                                    children: wallpaperData.colorPalette.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full border-2 border-white shadow-md\",\n                                                            style: {\n                                                                backgroundColor: color\n                                                            },\n                                                            title: color\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full lg:w-80 border-l border-gray-200 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Available Resolutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: wallpaperData.resolutions.map((resolution, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `p-3 border rounded-lg cursor-pointer transition-colors duration-200 ${selectedResolution?.label === resolution.label ? \"border-primary-500 bg-primary-50\" : \"border-gray-200 hover:border-gray-300\"}`,\n                                                            onClick: ()=>setSelectedResolution(resolution),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: resolution.label\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                                lineNumber: 202,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    resolution.width,\n                                                                                    \" \\xd7 \",\n                                                                                    resolution.height\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                                lineNumber: 203,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-right\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: resolution.fileSize\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Aspect Ratio:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: wallpaperData.aspectRatio\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Style:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium capitalize\",\n                                                                    children: wallpaperData.style\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Format:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: product.fileFormat?.join(\", \")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        wallpaperData.downloadCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Downloads:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: wallpaperData.downloadCount.toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>selectedResolution && onDownload?.(selectedResolution),\n                                                    disabled: !selectedResolution,\n                                                    className: \"w-full bg-purple-yellow-gradient text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__.DownloadIcon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Download \",\n                                                                selectedResolution?.label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsZoomed(true),\n                                                    className: \"w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Full Size Preview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: [\n                                                            \"$\",\n                                                            product.price.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 line-through\",\n                                                        children: [\n                                                            \"$\",\n                                                            product.originalPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: isZoomed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"absolute inset-0 bg-black bg-opacity-95 flex items-center justify-center p-4 z-10\",\n                        onClick: ()=>setIsZoomed(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                scale: 0.8\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            exit: {\n                                scale: 0.8\n                            },\n                            className: \"relative max-w-full max-h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: wallpaperData.previewUrl,\n                                    alt: product.title,\n                                    className: \"max-w-full max-h-full object-contain rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsZoomed(false),\n                                    className: \"absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_DownloadIcon_EyeIcon_MagnifyingGlassIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n            lineNumber: 78,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\components\\\\wallpaper\\\\WallpaperPreview.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WallpaperPreview);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/wallpaper/WallpaperPreview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CartProvider: () => (/* binding */ CartProvider),\n/* harmony export */   useCart: () => (/* binding */ useCart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useCart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useCart */ \"(ssr)/./src/hooks/useCart.ts\");\n/* __next_internal_client_entry_do_not_use__ useCart,CartProvider auto */ \n\n\nconst CartContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCart = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CartContext);\n    if (context === undefined) {\n        throw new Error(\"useCart must be used within a CartProvider\");\n    }\n    return context;\n};\nconst CartProvider = ({ children })=>{\n    const cartState = (0,_hooks_useCart__WEBPACK_IMPORTED_MODULE_2__.useCartState)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CartContext.Provider, {\n        value: cartState,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\contexts\\\\CartContext.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/CartContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useCart.ts":
/*!******************************!*\
  !*** ./src/hooks/useCart.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCartState: () => (/* binding */ useCartState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useCartState auto */ \nconst useCartState = ()=>{\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    // Load cart from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const savedCart = localStorage.getItem(\"cart\");\n        if (savedCart) {\n            try {\n                setItems(JSON.parse(savedCart));\n            } catch (error) {\n                console.error(\"Error loading cart from localStorage:\", error);\n            }\n        }\n    }, []);\n    // Save cart to localStorage whenever items change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        localStorage.setItem(\"cart\", JSON.stringify(items));\n    }, [\n        items\n    ]);\n    const itemCount = items.reduce((total, item)=>total + item.quantity, 0);\n    const totalPrice = items.reduce((total, item)=>total + item.price * item.quantity, 0);\n    const addItem = (newItem)=>{\n        setItems((currentItems)=>{\n            const existingItem = currentItems.find((item)=>item.id === newItem.id);\n            if (existingItem) {\n                return currentItems.map((item)=>item.id === newItem.id ? {\n                        ...item,\n                        quantity: item.quantity + 1\n                    } : item);\n            } else {\n                return [\n                    ...currentItems,\n                    {\n                        ...newItem,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const removeItem = (id)=>{\n        setItems((currentItems)=>currentItems.filter((item)=>item.id !== id));\n    };\n    const updateQuantity = (id, quantity)=>{\n        if (quantity <= 0) {\n            removeItem(id);\n            return;\n        }\n        setItems((currentItems)=>currentItems.map((item)=>item.id === id ? {\n                    ...item,\n                    quantity\n                } : item));\n    };\n    const clearCart = ()=>{\n        setItems([]);\n    };\n    return {\n        items,\n        itemCount,\n        totalPrice,\n        addItem,\n        removeItem,\n        updateQuantity,\n        clearCart\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCart.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/product.ts":
/*!******************************!*\
  !*** ./src/types/product.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeviceType: () => (/* binding */ DeviceType),\n/* harmony export */   OrderStatus: () => (/* binding */ OrderStatus),\n/* harmony export */   PaymentStatus: () => (/* binding */ PaymentStatus),\n/* harmony export */   ProductCategory: () => (/* binding */ ProductCategory),\n/* harmony export */   ProductType: () => (/* binding */ ProductType),\n/* harmony export */   WallpaperStyle: () => (/* binding */ WallpaperStyle),\n/* harmony export */   getDeviceTypeLabel: () => (/* binding */ getDeviceTypeLabel),\n/* harmony export */   getProductCategoryLabel: () => (/* binding */ getProductCategoryLabel),\n/* harmony export */   getWallpaperStyleLabel: () => (/* binding */ getWallpaperStyleLabel)\n/* harmony export */ });\nvar ProductCategory;\n(function(ProductCategory) {\n    // Digital Categories\n    ProductCategory[\"WALLPAPERS\"] = \"wallpapers\";\n    ProductCategory[\"DIGITAL_ART\"] = \"digital-art\";\n    ProductCategory[\"TEMPLATES\"] = \"templates\";\n    ProductCategory[\"FONTS\"] = \"fonts\";\n    // Physical Categories\n    ProductCategory[\"CLOTHING\"] = \"clothing\";\n    ProductCategory[\"ACCESSORIES\"] = \"accessories\";\n    ProductCategory[\"HOME_DECOR\"] = \"home-decor\";\n    ProductCategory[\"STATIONERY\"] = \"stationery\";\n    // Services\n    ProductCategory[\"CONSULTING\"] = \"consulting\";\n    ProductCategory[\"DESIGN_SERVICES\"] = \"design-services\";\n})(ProductCategory || (ProductCategory = {}));\nvar ProductType;\n(function(ProductType) {\n    ProductType[\"DIGITAL\"] = \"digital\";\n    ProductType[\"PHYSICAL\"] = \"physical\";\n    ProductType[\"SERVICE\"] = \"service\";\n})(ProductType || (ProductType = {}));\nvar WallpaperStyle;\n(function(WallpaperStyle) {\n    WallpaperStyle[\"ABSTRACT\"] = \"abstract\";\n    WallpaperStyle[\"NATURE\"] = \"nature\";\n    WallpaperStyle[\"MINIMALIST\"] = \"minimalist\";\n    WallpaperStyle[\"GAMING\"] = \"gaming\";\n    WallpaperStyle[\"ANIME\"] = \"anime\";\n    WallpaperStyle[\"PHOTOGRAPHY\"] = \"photography\";\n    WallpaperStyle[\"GRADIENT\"] = \"gradient\";\n    WallpaperStyle[\"PATTERN\"] = \"pattern\";\n    WallpaperStyle[\"DARK\"] = \"dark\";\n    WallpaperStyle[\"LIGHT\"] = \"light\";\n    WallpaperStyle[\"NEON\"] = \"neon\";\n    WallpaperStyle[\"VINTAGE\"] = \"vintage\";\n    WallpaperStyle[\"MODERN\"] = \"modern\";\n    WallpaperStyle[\"ARTISTIC\"] = \"artistic\";\n})(WallpaperStyle || (WallpaperStyle = {}));\nvar DeviceType;\n(function(DeviceType) {\n    DeviceType[\"DESKTOP\"] = \"desktop\";\n    DeviceType[\"LAPTOP\"] = \"laptop\";\n    DeviceType[\"TABLET\"] = \"tablet\";\n    DeviceType[\"MOBILE\"] = \"mobile\";\n    DeviceType[\"ULTRAWIDE\"] = \"ultrawide\";\n    DeviceType[\"DUAL_MONITOR\"] = \"dual-monitor\";\n})(DeviceType || (DeviceType = {}));\nvar OrderStatus;\n(function(OrderStatus) {\n    OrderStatus[\"PENDING\"] = \"pending\";\n    OrderStatus[\"PROCESSING\"] = \"processing\";\n    OrderStatus[\"SHIPPED\"] = \"shipped\";\n    OrderStatus[\"DELIVERED\"] = \"delivered\";\n    OrderStatus[\"CANCELLED\"] = \"cancelled\";\n    OrderStatus[\"REFUNDED\"] = \"refunded\";\n})(OrderStatus || (OrderStatus = {}));\nvar PaymentStatus;\n(function(PaymentStatus) {\n    PaymentStatus[\"PENDING\"] = \"pending\";\n    PaymentStatus[\"PAID\"] = \"paid\";\n    PaymentStatus[\"FAILED\"] = \"failed\";\n    PaymentStatus[\"REFUNDED\"] = \"refunded\";\n})(PaymentStatus || (PaymentStatus = {}));\n// Utility functions\nconst getProductCategoryLabel = (category)=>{\n    const labels = {\n        [\"wallpapers\"]: \"Wallpapers\",\n        [\"digital-art\"]: \"Digital Art\",\n        [\"templates\"]: \"Templates\",\n        [\"fonts\"]: \"Fonts\",\n        [\"clothing\"]: \"Clothing\",\n        [\"accessories\"]: \"Accessories\",\n        [\"home-decor\"]: \"Home Decor\",\n        [\"stationery\"]: \"Stationery\",\n        [\"consulting\"]: \"Consulting\",\n        [\"design-services\"]: \"Design Services\"\n    };\n    return labels[category];\n};\nconst getWallpaperStyleLabel = (style)=>{\n    const labels = {\n        [\"abstract\"]: \"Abstract\",\n        [\"nature\"]: \"Nature\",\n        [\"minimalist\"]: \"Minimalist\",\n        [\"gaming\"]: \"Gaming\",\n        [\"anime\"]: \"Anime\",\n        [\"photography\"]: \"Photography\",\n        [\"gradient\"]: \"Gradient\",\n        [\"pattern\"]: \"Pattern\",\n        [\"dark\"]: \"Dark\",\n        [\"light\"]: \"Light\",\n        [\"neon\"]: \"Neon\",\n        [\"vintage\"]: \"Vintage\",\n        [\"modern\"]: \"Modern\",\n        [\"artistic\"]: \"Artistic\"\n    };\n    return labels[style];\n};\nconst getDeviceTypeLabel = (device)=>{\n    const labels = {\n        [\"desktop\"]: \"Desktop\",\n        [\"laptop\"]: \"Laptop\",\n        [\"tablet\"]: \"Tablet\",\n        [\"mobile\"]: \"Mobile\",\n        [\"ultrawide\"]: \"Ultrawide\",\n        [\"dual-monitor\"]: \"Dual Monitor\"\n    };\n    return labels[device];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/product.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2ee6bcf73a47\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxpc2hvcC1wb3J0Zm9saW8vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzVlYzIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyZWU2YmNmNzNhNDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CartContext */ \"(rsc)/./src/contexts/CartContext.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\n// import { AuthProvider } from '@/contexts/AuthContext'\n\n\n\nconst metadata = {\n    title: \"EliShop - Portfolio & Store\",\n    description: \"Modern portfolio website with integrated shop featuring stunning animations and transitions\",\n    keywords: [\n        \"portfolio\",\n        \"shop\",\n        \"ecommerce\",\n        \"modern\",\n        \"animations\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CartContext__WEBPACK_IMPORTED_MODULE_2__.CartProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"min-h-screen\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/shop/wallpapers/page.tsx":
/*!******************************************!*\
  !*** ./src/app/shop/wallpapers/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\app\shop\wallpapers\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\components\layout\Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\components\layout\Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/contexts/CartContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CartContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CartProvider: () => (/* binding */ e1),
/* harmony export */   useCart: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\CartContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\CartContext.tsx#useCart`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\elishop\src\contexts\CartContext.tsx#CartProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-icons","vendor-chunks/framer-motion","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshop%2Fwallpapers%2Fpage&page=%2Fshop%2Fwallpapers%2Fpage&appPaths=%2Fshop%2Fwallpapers%2Fpage&pagePath=private-next-app-dir%2Fshop%2Fwallpapers%2Fpage.tsx&appDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CADMNI%5CDocuments%5Celishop&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();