'use client';

import React from 'react';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

interface ScrollRevealProps {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right' | 'scale' | 'fade';
  delay?: number;
  duration?: number;
  distance?: number;
  threshold?: number;
  once?: boolean;
  className?: string;
}

const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  direction = 'up',
  delay = 0,
  duration = 0.6,
  distance = 50,
  threshold = 0.1,
  once = true,
  className = '',
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { 
    once,
    margin: '0px 0px -100px 0px',
    amount: threshold 
  });

  const getVariants = () => {
    const baseVariants = {
      visible: {
        opacity: 1,
        x: 0,
        y: 0,
        scale: 1,
        transition: {
          duration,
          delay,
          ease: [0.25, 0.25, 0.25, 0.75],
        },
      },
    };

    switch (direction) {
      case 'up':
        return {
          ...baseVariants,
          hidden: { opacity: 0, y: distance },
        };
      case 'down':
        return {
          ...baseVariants,
          hidden: { opacity: 0, y: -distance },
        };
      case 'left':
        return {
          ...baseVariants,
          hidden: { opacity: 0, x: distance },
        };
      case 'right':
        return {
          ...baseVariants,
          hidden: { opacity: 0, x: -distance },
        };
      case 'scale':
        return {
          ...baseVariants,
          hidden: { opacity: 0, scale: 0.8 },
        };
      case 'fade':
        return {
          ...baseVariants,
          hidden: { opacity: 0 },
        };
      default:
        return {
          ...baseVariants,
          hidden: { opacity: 0, y: distance },
        };
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
      variants={getVariants()}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default ScrollReveal;
