'use client';

import React from 'react';
import { motion } from 'framer-motion';
import HeroTest from '@/components/home/<USER>';
import FeaturedPortfolio from '@/components/home/<USER>';
import FeaturedProducts from '@/components/home/<USER>';
import Skills from '@/components/home/<USER>';
import Testimonials from '@/components/home/<USER>';
import CallToAction from '@/components/home/<USER>';

export default function Home() {
  return (
    <div className="pt-16 lg:pt-20">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="space-y-0"
      >
        <HeroTest />
        <Skills />
        <FeaturedPortfolio />
        <FeaturedProducts />
        <Testimonials />
        <CallToAction />
      </motion.div>

      {/* Scroll to Top Button */}
      <motion.button
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        className="fixed bottom-8 right-8 w-12 h-12 bg-purple-yellow-gradient text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      </motion.button>
    </div>
  );
}
