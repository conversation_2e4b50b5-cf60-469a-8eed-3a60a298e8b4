'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRightIcon, SparklesIcon, PaintBrushIcon, CameraIcon } from '@heroicons/react/24/outline';

// Typing Animation Component
const TypingAnimation: React.FC = () => {
  const [displayText, setDisplayText] = React.useState('<PERSON>');
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [typingTexts, setTypingTexts] = React.useState(['<PERSON>', 'Web Developer']);

  // Load typing texts from localStorage (admin configurable)
  React.useEffect(() => {
    const savedTexts = localStorage.getItem('heroTypingTexts');
    if (savedTexts) {
      try {
        const parsed = JSON.parse(savedTexts);
        if (Array.isArray(parsed) && parsed.length > 0) {
          setTypingTexts(parsed);
        }
      } catch (error) {
        console.log('Using default typing texts');
      }
    }
  }, []);

  React.useEffect(() => {
    const currentText = typingTexts[currentIndex];

    const timeout = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (displayText.length < currentText.length) {
          setDisplayText(currentText.slice(0, displayText.length + 1));
        } else {
          // Finished typing, wait then start deleting
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        // Deleting
        if (displayText.length > 0) {
          setDisplayText(currentText.slice(0, displayText.length - 1));
        } else {
          // Finished deleting, move to next text
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % typingTexts.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [displayText, isDeleting, currentIndex, typingTexts]);

  // Debug: log the current text
  console.log('TypingAnimation displayText:', displayText);

  return (
    <span className="relative inline-block">
      <span className="inline-block min-w-0 font-bold text-purple-600">
        {displayText || 'Elias Mwangi'}
      </span>
      <span className="animate-pulse text-purple-600 ml-1">|</span>
    </span>
  );
};

const HeroTest: React.FC = () => {
  const [profileImage, setProfileImage] = React.useState<string | null>(null);

  // Load profile image from localStorage
  React.useEffect(() => {
    const savedImage = localStorage.getItem('profileImage');
    if (savedImage) {
      setProfileImage(savedImage);
    }
  }, []);
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated Background Shapes */}
        <motion.div
          animate={{
            y: [-10, 10, -10],
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-20 left-10 w-20 h-20 bg-purple-200 rounded-full opacity-60"
        />
        <motion.div
          animate={{
            y: [10, -10, 10],
            rotate: [0, -5, 5, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute top-40 right-20 w-32 h-32 bg-yellow-200 rounded-full opacity-40"
        />
        <motion.div
          animate={{
            y: [-15, 15, -15],
            rotate: [0, 10, -10, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
          className="absolute bottom-20 left-20 w-24 h-24 bg-primary-200 rounded-full opacity-50"
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-center min-h-[80vh]"
        >
          {/* Left Side - Hero Text */}
          <motion.div variants={itemVariants} className="lg:col-span-3 text-center lg:text-left order-1 lg:order-1 lg:pr-8">
            {/* Badge */}
            <motion.div variants={itemVariants} className="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg mb-8 lg:mx-0 mx-auto">
              <SparklesIcon className="w-5 h-5 text-primary-600" />
              <span className="text-primary-600 font-semibold text-sm">Creative Professional</span>
            </motion.div>

            {/* Main Heading with Typing Animation */}
            <motion.h1 variants={itemVariants} className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
              <span className="block">Hi, I'm</span>
              <span className="inline-block whitespace-nowrap min-w-[280px] md:min-w-[380px] lg:min-w-[480px]">
                <TypingAnimation />
              </span>
            </motion.h1>

            {/* Subtitle */}
            <motion.p variants={itemVariants} className="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl lg:max-w-none leading-relaxed">
              I bring ideas to life through visually appealing and functional designs that capture attention and tell compelling stories.
            </motion.p>

            {/* Skills Icons */}
            <motion.div variants={itemVariants} className="flex flex-wrap justify-center lg:justify-start gap-4 mb-12">
              <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full">
                <PaintBrushIcon className="w-5 h-5 text-purple-600" />
                <span className="text-sm font-medium text-gray-700">Graphic Design</span>
              </div>
              <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full">
                <CameraIcon className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Photography</span>
              </div>
              <div className="flex items-center space-x-2 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full">
                <SparklesIcon className="w-5 h-5 text-yellow-600" />
                <span className="text-sm font-medium text-gray-700">Video Editing</span>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start items-center">
              <Link href="/portfolio">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="group bg-purple-yellow-gradient text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2"
                >
                  <span>View My Work</span>
                  <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
                </motion.button>
              </Link>

              <Link href="/contact">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white/80 backdrop-blur-sm text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-primary-300"
                >
                  Get In Touch
                </motion.button>
              </Link>
            </motion.div>

            {/* Stats */}
            <motion.div variants={itemVariants} className="mt-16 grid grid-cols-2 gap-6 max-w-md lg:max-w-none">
              {[
                { number: '50+', label: 'Projects Completed' },
                { number: '5+', label: 'Years Experience' },
                { number: '4', label: 'Specializations' },
                { number: '100%', label: 'Client Satisfaction' },
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="text-center lg:text-left"
                >
                  <div className="text-2xl md:text-3xl font-bold bg-purple-yellow-gradient bg-clip-text text-transparent">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Right Side - Profile Image */}
          <motion.div variants={itemVariants} className="lg:col-span-2 flex justify-center lg:justify-end order-2 lg:order-2">
            <div className="relative">
              {/* Main Profile Image Circle */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="w-64 h-64 md:w-80 md:h-80 lg:w-72 lg:h-72 rounded-full overflow-hidden shadow-2xl border-8 border-white/20 backdrop-blur-sm bg-gradient-to-br from-primary-100 to-secondary-100"
              >
                {/* Profile Image - Dynamic */}
                {profileImage ? (
                  <img
                    src={profileImage}
                    alt="Elias Mwangi - Creative Professional"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-200 to-secondary-200">
                    <div className="text-center">
                      <div className="text-6xl md:text-8xl font-bold text-primary-600 mb-4">EM</div>
                      <p className="text-primary-700 font-medium">Your Photo Here</p>
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Decorative Elements Around Image */}
              <motion.div
                animate={{
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: 'linear',
                }}
                className="absolute -top-4 -right-4 w-20 h-20 border-4 border-yellow-400 rounded-full opacity-60"
              />

              <motion.div
                animate={{
                  rotate: [360, 0],
                }}
                transition={{
                  duration: 15,
                  repeat: Infinity,
                  ease: 'linear',
                }}
                className="absolute -bottom-6 -left-6 w-16 h-16 border-4 border-purple-400 rounded-full opacity-60"
              />

              {/* Floating Skill Icons */}
              <motion.div
                animate={{
                  y: [-10, 10, -10],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
                className="absolute top-8 -left-6 bg-white/90 backdrop-blur-sm p-2.5 rounded-full shadow-lg"
              >
                <PaintBrushIcon className="w-5 h-5 text-purple-600" />
              </motion.div>

              <motion.div
                animate={{
                  y: [10, -10, 10],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 1,
                }}
                className="absolute top-16 -right-8 bg-white/90 backdrop-blur-sm p-2.5 rounded-full shadow-lg"
              >
                <CameraIcon className="w-5 h-5 text-blue-600" />
              </motion.div>

              <motion.div
                animate={{
                  y: [-8, 8, -8],
                }}
                transition={{
                  duration: 3.5,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 2,
                }}
                className="absolute bottom-12 -right-6 bg-white/90 backdrop-blur-sm p-2.5 rounded-full shadow-lg"
              >
                <SparklesIcon className="w-5 h-5 text-yellow-600" />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2"></div>
        </div>
      </motion.div>
    </section>
  );
};

export default HeroTest;
