"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/wallpapers/page",{

/***/ "(app-pages-browser)/./src/app/admin/wallpapers/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/admin/wallpapers/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DeviceTabletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,CloudArrowUpIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DeviceTabletIcon,EyeIcon,FunnelIcon,MagnifyingGlassIcon,PencilIcon,PhotoIcon,PlusIcon,TrashIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/wallpapers */ \"(app-pages-browser)/./src/data/wallpapers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst WallpapersManagement = ()=>{\n    _s();\n    const [wallpapers, setWallpapers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showUploadModal, setShowUploadModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [wallpaperToDelete, setWallpaperToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [wallpaperToEdit, setWallpaperToEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [wallpaperToView, setWallpaperToView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpaperCategories.map((cat)=>cat.toLowerCase());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Convert shared wallpapers data to admin format\n        const adminWallpapers = _data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.wallpapersData.map(_data_wallpapers__WEBPACK_IMPORTED_MODULE_2__.convertToAdminWallpaper);\n        setWallpapers(adminWallpapers);\n        setLoading(false);\n    }, []);\n    const filteredWallpapers = wallpapers.filter((wallpaper)=>{\n        const matchesSearch = wallpaper.title.toLowerCase().includes(searchTerm.toLowerCase()) || wallpaper.description.toLowerCase().includes(searchTerm.toLowerCase()) || wallpaper.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = selectedCategory === \"all\" || wallpaper.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    const handleDeleteWallpaper = (id)=>{\n        setWallpaperToDelete(id);\n        setShowDeleteModal(true);\n    };\n    const confirmDelete = ()=>{\n        if (wallpaperToDelete) {\n            setWallpapers((prev)=>prev.filter((w)=>w.id !== wallpaperToDelete));\n            setWallpaperToDelete(null);\n            setShowDeleteModal(false);\n        }\n    };\n    const toggleFeatured = (id)=>{\n        setWallpapers((prev)=>prev.map((w)=>w.id === id ? {\n                    ...w,\n                    featured: !w.featured\n                } : w));\n    };\n    const handleViewWallpaper = (wallpaper)=>{\n        setWallpaperToView(wallpaper);\n        setShowViewModal(true);\n    };\n    const handleEditWallpaper = (wallpaper)=>{\n        setWallpaperToEdit(wallpaper);\n        setShowEditModal(true);\n    };\n    const handleUpdateWallpaper = (updatedWallpaper)=>{\n        setWallpapers((prev)=>prev.map((w)=>w.id === updatedWallpaper.id ? updatedWallpaper : w));\n        setShowEditModal(false);\n        setWallpaperToEdit(null);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Wallpapers Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage your wallpaper collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 sm:mt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUploadModal(true),\n                            className: \"text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center space-x-2\",\n                            style: {\n                                background: \"linear-gradient(135deg, #8B5CF6 0%, #F59E0B 100%)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Add Wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Wallpapers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-8 h-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Total Downloads\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.reduce((sum, w)=>sum + w.downloads, 0).toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: wallpapers.filter((w)=>w.featured).length\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 text-yellow-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: categories.length - 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search wallpapers...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedCategory(category),\n                                    className: \"px-4 py-2 rounded-lg font-medium transition-all duration-200 \".concat(selectedCategory === category ? \"bg-purple-yellow-gradient text-white shadow-lg\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                    children: category === \"all\" ? \"All Categories\" : category\n                                }, category, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    children: filteredWallpapers.map((wallpaper)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            layout: true,\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            exit: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gradient-to-br from-primary-100 to-secondary-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-16 h-16 text-primary-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        wallpaper.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 left-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-purple-yellow-gradient text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-2 right-2 flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleFeatured(wallpaper.id),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    title: wallpaper.featured ? \"Remove from featured\" : \"Add to featured\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(wallpaper.featured ? \"text-purple-600\" : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteWallpaper(wallpaper.id),\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1.5 rounded-full hover:bg-white transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-2 left-2 flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/90 backdrop-blur-sm p-1 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-3 h-3 text-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 mb-1\",\n                                            children: wallpaper.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                            children: wallpaper.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                            children: [\n                                                wallpaper.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\",\n                                                        children: tag\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                wallpaper.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        wallpaper.tags.length - 3\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                wallpaper.downloads,\n                                                                \" downloads\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                wallpaper.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: wallpaper.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, wallpaper.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            filteredWallpapers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No wallpapers found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Try adjusting your search or filter criteria\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WallpaperUploadModal, {\n                isOpen: showUploadModal,\n                onClose: ()=>setShowUploadModal(false),\n                onUpload: (wallpaper)=>{\n                    setWallpapers((prev)=>[\n                            wallpaper,\n                            ...prev\n                        ]);\n                    setShowUploadModal(false);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Delete Wallpaper\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Are you sure you want to delete this wallpaper? This action cannot be undone.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDelete,\n                                    className: \"flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors duration-200\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WallpapersManagement, \"QAM2Au/tK8ESN4/9KmUcYgjp1zI=\");\n_c = WallpapersManagement;\n// Wallpaper Upload Modal Component\nconst WallpaperUploadModal = (param)=>{\n    let { isOpen, onClose, onUpload } = param;\n    _s1();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"Abstract\",\n        tags: \"\",\n        price: \"\",\n        originalPrice: \"\",\n        featured: false\n    });\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadStatus, setUploadStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const fileInputRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setUploading(true);\n        setUploadStatus(\"idle\");\n        try {\n            // Simulate upload\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            const newWallpaper = {\n                id: Date.now().toString(),\n                title: formData.title,\n                description: formData.description,\n                category: formData.category,\n                tags: formData.tags.split(\",\").map((tag)=>tag.trim()).filter(Boolean),\n                price: parseFloat(formData.price) || 0,\n                originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,\n                featured: formData.featured,\n                downloads: 0,\n                imageUrl: \"/api/placeholder/1920/1080\",\n                thumbnailUrl: \"/api/placeholder/400/300\",\n                resolutions: {\n                    desktop: {\n                        width: 1920,\n                        height: 1080,\n                        url: \"/api/placeholder/1920/1080\"\n                    },\n                    mobile: {\n                        width: 1080,\n                        height: 1920,\n                        url: \"/api/placeholder/1080/1920\"\n                    },\n                    tablet: {\n                        width: 1536,\n                        height: 2048,\n                        url: \"/api/placeholder/1536/2048\"\n                    }\n                },\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            onUpload(newWallpaper);\n            setUploadStatus(\"success\");\n            // Reset form\n            setFormData({\n                title: \"\",\n                description: \"\",\n                category: \"Abstract\",\n                tags: \"\",\n                price: \"\",\n                originalPrice: \"\",\n                featured: false\n            });\n        } catch (error) {\n            setUploadStatus(\"error\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: \"Upload New Wallpaper\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 442,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Wallpaper Image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>{\n                                                var _fileInputRef_current;\n                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                            },\n                                            className: \"text-primary-600 hover:text-primary-700 font-medium\",\n                                            children: \"Choose Image Files\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            multiple: true,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-2\",\n                                            children: \"Upload desktop, tablet, and mobile versions (JPG, PNG)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Title *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            required: true,\n                                            value: formData.title,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        title: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"Enter wallpaper title\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Category *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            required: true,\n                                            value: formData.category,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        category: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Abstract\",\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Nature\",\n                                                    children: \"Nature\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Geometric\",\n                                                    children: \"Geometric\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Minimal\",\n                                                    children: \"Minimal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Dark\",\n                                                    children: \"Dark\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"Light\",\n                                                    children: \"Light\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Description *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    required: true,\n                                    value: formData.description,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                description: e.target.value\n                                            })),\n                                    rows: 3,\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Describe your wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Tags\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.tags,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                tags: e.target.value\n                                            })),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                    placeholder: \"Enter tags separated by commas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Price ($) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            required: true,\n                                            value: formData.price,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        price: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Original Price ($)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            value: formData.originalPrice,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        originalPrice: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\n                                            placeholder: \"0.00\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    id: \"featured\",\n                                    checked: formData.featured,\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                featured: e.target.checked\n                                            })),\n                                    className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"featured\",\n                                    className: \"ml-2 block text-sm text-gray-700\",\n                                    children: \"Mark as featured wallpaper\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, undefined),\n                        uploadStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-800\",\n                                    children: \"Wallpaper uploaded successfully!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, undefined),\n                        uploadStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"w-5 h-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-800\",\n                                    children: \"Upload failed. Please try again.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: uploading,\n                                    className: \"flex-1 bg-purple-yellow-gradient text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                    children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Uploading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_CloudArrowUpIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DeviceTabletIcon_EyeIcon_FunnelIcon_MagnifyingGlassIcon_PencilIcon_PhotoIcon_PlusIcon_TrashIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Upload Wallpaper\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors duration-200\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n            lineNumber: 441,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\elishop\\\\src\\\\app\\\\admin\\\\wallpapers\\\\page.tsx\",\n        lineNumber: 440,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(WallpaperUploadModal, \"FFa/r8q3sl1tbMWGr6sEhy9jyn4=\");\n_c1 = WallpaperUploadModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (WallpapersManagement);\nvar _c, _c1;\n$RefreshReg$(_c, \"WallpapersManagement\");\n$RefreshReg$(_c1, \"WallpaperUploadModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/wallpapers/page.tsx\n"));

/***/ })

});